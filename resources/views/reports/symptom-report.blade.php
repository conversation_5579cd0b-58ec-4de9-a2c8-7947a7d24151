<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Symptom Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e74c3c;
        }
        .header h1 {
            color: #e74c3c;
            margin: 0;
            font-size: 24px;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .info-row {
            margin: 5px 0;
        }
        .label {
            font-weight: bold;
            display: inline-block;
            width: 150px;
        }
        .symptoms {
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .meal-item {
            padding: 10px;
            margin: 10px 0;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .meal-header {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .ingredients {
            font-size: 11px;
            color: #666;
        }
        .disclaimer {
            margin-top: 30px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 5px;
            font-size: 11px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 10px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>GallDiet Symptom Report</h1>
        <p>Emergency Medical Reference Document</p>
    </div>

    <div class="section">
        <div class="section-title">Attack Information</div>
        <div class="info-row">
            <span class="label">Attack Date/Time:</span>
            <span>{{ $attack->onset_at->format('F j, Y g:i A') }}</span>
        </div>
        <div class="info-row">
            <span class="label">Pain Intensity:</span>
            <span>{{ $attack->pain_intensity }}/10</span>
        </div>
        <div class="info-row">
            <span class="label">Duration:</span>
            <span>{{ $attack->duration_minutes }} minutes</span>
        </div>
        <div class="info-row">
            <span class="label">Medical Care:</span>
            <span>{{ ucwords(str_replace('_', ' ', $attack->medical_care_type)) }}</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Symptoms Experienced</div>
        <div class="symptoms">
            @if(is_array($attack->symptoms) && count($attack->symptoms) > 0)
                <ul>
                    @foreach($attack->symptoms as $symptom)
                        <li>{{ $symptom }}</li>
                    @endforeach
                </ul>
            @else
                <p>No symptoms recorded</p>
            @endif
        </div>
    </div>

    <div class="section">
        <div class="section-title">Recent Meals (8 Hours Before Attack)</div>
        @if($recentScans->count() > 0)
            @foreach($recentScans as $scan)
                <div class="meal-item">
                    <div class="meal-header">
                        {{ $scan->meal_name ?? 'Meal' }}
                        <span style="float: right; font-size: 11px; color: #666;">
                            {{ $scan->created_at->format('g:i A') }}
                            ({{ number_format($scan->created_at->diffInHours($attack->onset_at, true), 1) }} hours before attack)
                        </span>
                    </div>
                    @if($scan->detected_ingredients)
                        <div class="ingredients">
                            <strong>Ingredients:</strong>
                            {{ is_array($scan->detected_ingredients) ? implode(', ', $scan->detected_ingredients) : $scan->detected_ingredients }}
                        </div>
                    @endif
                    @if($scan->adjusted_score !== null)
                        <div class="ingredients">
                            <strong>Safety Score:</strong> {{ $scan->adjusted_score }}/100
                        </div>
                    @endif
                </div>
            @endforeach
        @else
            <p>No meals recorded in the 8 hours before this attack.</p>
        @endif
    </div>

    <div class="disclaimer">
        <strong>Medical Disclaimer:</strong> This report is generated from self-reported data in the GallDiet application.
        It is intended to assist medical professionals in understanding dietary patterns and should not be used as a
        substitute for professional medical diagnosis or treatment. Always consult with a qualified healthcare provider.
    </div>

    <div class="footer">
        <p>Generated: {{ $generatedAt->format('F j, Y g:i A') }}</p>
        <p>Report ID: SYMP-{{ $attack->id }}-{{ $generatedAt->timestamp }}</p>
    </div>
</body>
</html>
