<?php

namespace App\Jobs;

use App\Models\Scan;
use App\Services\GeminiVisionService;
use App\Services\PersonalizationEngine;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class AnalyzeScanPhotoJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 2;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 30; // Increased to allow for Gemini API retries

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Scan $scan
    ) {}

    /**
     * Execute the job.
     */
    public function handle(
        GeminiVisionService $visionService,
        PersonalizationEngine $personalizationEngine
    ): void {
        $startTime = microtime(true);

        try {
            Log::info('Starting scan analysis', [
                'scan_id' => $this->scan->id,
                'user_id' => $this->scan->user_id,
            ]);

            // Load user relationships
            $user = $this->scan->user()->with(['profile', 'triggers'])->first();

            if (! $user) {
                throw new Exception('User not found for scan');
            }

            // Build user context for AI
            $userContext = $this->buildUserContext($user);

            // Call Gemini Vision API to analyze the meal photo
            $analysisResult = $visionService->analyzeMeal($this->scan->image_path, $userContext);

            Log::info('Gemini analysis completed', [
                'scan_id' => $this->scan->id,
                'is_food' => $analysisResult['is_food'],
                'meal_name' => $analysisResult['meal_name'],
                'confidence' => $analysisResult['confidence'],
                'detected_ingredients' => $analysisResult['detected_ingredients'],
            ]);

            // Determine if this is actually food
            // Check both AI's is_food flag AND heuristics (no ingredients detected, "no meal" in name)
            $isActuallyFood = $analysisResult['is_food']
                && ! empty($analysisResult['detected_ingredients'])
                && ! str_contains(strtolower($analysisResult['meal_name']), 'not a meal')
                && ! str_contains(strtolower($analysisResult['meal_name']), 'no meal');

            Log::info('Food detection heuristics', [
                'scan_id' => $this->scan->id,
                'ai_is_food' => $analysisResult['is_food'],
                'has_ingredients' => ! empty($analysisResult['detected_ingredients']),
                'ingredients_count' => count($analysisResult['detected_ingredients']),
                'meal_name_lower' => strtolower($analysisResult['meal_name']),
                'contains_not_meal' => str_contains(strtolower($analysisResult['meal_name']), 'not a meal'),
                'contains_no_meal' => str_contains(strtolower($analysisResult['meal_name']), 'no meal'),
                'final_is_actually_food' => $isActuallyFood,
            ]);

            // Handle non-food images
            if (! $isActuallyFood) {
                $durationMs = (int) ((microtime(true) - $startTime) * 1000);

                // Generate appropriate non-food message
                $nonFoodReasoning = $analysisResult['reasoning'] ??
                    "This image doesn't appear to contain any food or meals that we can analyze for safety. Please take a photo of your meal to get a personalized safety analysis.";

                // Explicitly set is_food to false
                $this->scan->is_food = false;
                $this->scan->status = 'completed';
                $this->scan->meal_name = 'Not a meal';
                $this->scan->detected_ingredients = [];
                $this->scan->adjusted_score = null;
                $this->scan->base_score = null;
                $this->scan->confidence_score = $analysisResult['confidence'];
                $this->scan->personalized_reasoning = $nonFoodReasoning;
                $this->scan->trigger_warnings = [];
                $this->scan->analyzed_at = now();
                $this->scan->save();

                Log::info('Non-food image processed', [
                    'scan_id' => $this->scan->id,
                    'duration_ms' => $durationMs,
                    'original_meal_name' => $analysisResult['meal_name'],
                    'db_is_food_value' => $this->scan->is_food,
                    'db_is_food_type' => gettype($this->scan->is_food),
                ]);

                return;
            }

            // Calculate personalized safety score for food images
            $scoreResult = $personalizationEngine->calculateSafetyScore(
                $user,
                $analysisResult['detected_ingredients']
            );

            Log::info('Safety score calculated', [
                'scan_id' => $this->scan->id,
                'safety_score' => $scoreResult['safety_score'],
                'trigger_count' => count($scoreResult['trigger_warnings']),
                'allergen_detected' => $scoreResult['allergen_detected'],
            ]);

            // Generate personalized reasoning
            $personalizedReasoning = $personalizationEngine->generatePersonalizedReasoning(
                $user,
                $scoreResult
            );

            // Calculate analysis duration
            $durationMs = (int) ((microtime(true) - $startTime) * 1000);

            // Update scan with results
            $this->scan->update([
                'status' => 'completed',
                'is_food' => true,
                'meal_name' => $analysisResult['meal_name'],
                'detected_ingredients' => $analysisResult['detected_ingredients'],
                'adjusted_score' => $scoreResult['safety_score'],
                'base_score' => $scoreResult['base_score'],
                'confidence_score' => $analysisResult['confidence'],
                'personalized_reasoning' => $personalizedReasoning,
                'trigger_warnings' => $scoreResult['trigger_warnings'],
                'analyzed_at' => now(),
            ]);

            Log::info('Scan analysis completed successfully', [
                'scan_id' => $this->scan->id,
                'duration_ms' => $durationMs,
            ]);
        } catch (Exception $e) {
            Log::error('Scan analysis failed', [
                'scan_id' => $this->scan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Mark scan as failed
            $this->scan->update([
                'status' => 'failed',
            ]);

            // Re-throw to trigger retry if attempts remaining
            throw $e;
        }
    }

    /**
     * Build user context array for AI personalization
     */
    private function buildUserContext($user): array
    {
        $context = [];

        if ($user->profile) {
            if ($user->profile->allergens) {
                $context['allergens'] = $user->profile->allergens;
            }

            if ($user->profile->dietary_preferences) {
                $context['dietary_preferences'] = $user->profile->dietary_preferences;
            }
        }

        if ($user->triggers && $user->triggers->count() > 0) {
            $context['triggers'] = $user->triggers->map(function ($trigger) {
                return [
                    'name' => $trigger->trigger_name,
                    'severity' => $trigger->severity,
                    'confirmed' => $trigger->confirmed_at !== null,
                ];
            })->toArray();
        }

        return $context;
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Scan analysis job permanently failed', [
            'scan_id' => $this->scan->id,
            'error' => $exception->getMessage(),
        ]);

        // Ensure scan is marked as failed
        $this->scan->update([
            'status' => 'failed',
        ]);
    }
}
