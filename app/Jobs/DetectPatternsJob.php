<?php

namespace App\Jobs;

use App\Models\Attack;
use App\Services\PatternDetectionService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class DetectPatternsJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Attack $attack
    ) {}

    /**
     * Execute the job.
     */
    public function handle(PatternDetectionService $patternService): void
    {
        Log::info('Starting pattern detection job', [
            'attack_id' => $this->attack->id,
            'user_id' => $this->attack->user_id,
        ]);

        try {
            $suggestions = $patternService->detectPatterns($this->attack);

            Log::info('Pattern detection job completed', [
                'attack_id' => $this->attack->id,
                'suggestions_count' => count($suggestions),
            ]);
        } catch (Exception $e) {
            Log::error('Pattern detection job failed', [
                'attack_id' => $this->attack->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Pattern detection job permanently failed', [
            'attack_id' => $this->attack->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
