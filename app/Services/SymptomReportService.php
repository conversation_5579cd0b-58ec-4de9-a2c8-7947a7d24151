<?php

namespace App\Services;

use App\Models\Attack;
use App\Models\Scan;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class SymptomReportService
{
    /**
     * Generate a PDF symptom report for an attack.
     * T179: Creates PDF with attack symptoms and recent meals (last 8 hours).
     *
     * @return array ['report_url' => string, 'report_format' => 'pdf']
     */
    public function generatePDF(Attack $attack): array
    {
        // Get recent scans within 8 hours before attack (T180)
        $recentScans = Scan::where('user_id', $attack->user_id)
            ->where('status', 'completed')
            ->where('created_at', '>=', $attack->onset_at->copy()->subHours(8))
            ->where('created_at', '<=', $attack->onset_at)
            ->orderBy('created_at', 'desc')
            ->get();

        // Prepare data for PDF
        $data = [
            'attack' => $attack,
            'recentScans' => $recentScans,
            'generatedAt' => now(),
        ];

        // Generate PDF using DomPDF
        $pdf = Pdf::loadView('reports.symptom-report', $data);

        // Generate unique filename
        $filename = 'symptom-report-'.$attack->id.'-'.now()->timestamp.'.pdf';
        $path = 'reports/'.$filename;

        // Store PDF in public disk (shareable)
        Storage::disk('public')->put($path, $pdf->output());

        // Return report URL
        return [
            'report_url' => Storage::disk('public')->url($path),
            'report_format' => 'pdf',
        ];
    }
}
