<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenFoodFactsService
{
    /**
     * Look up product information by barcode.
     *
     * @param  string  $barcode  The product barcode (EAN-13, UPC, etc.)
     * @return array|null Product data or null if not found
     *
     * @throws \Exception If API is unavailable
     */
    public function lookupBarcode(string $barcode): ?array
    {
        // Check cache first (Redis, 90-day TTL per research.md line 84)
        return Cache::remember("barcode:{$barcode}", now()->addDays(90), function () use ($barcode) {
            try {
                $response = Http::timeout(5)
                    ->get("https://world.openfoodfacts.org/api/v2/product/{$barcode}.json");

                // Product not found (status = 0)
                if ($response->failed() || $response->json('status') === 0) {
                    Log::info('Product not found in Open Food Facts', ['barcode' => $barcode]);

                    return null;
                }

                $product = $response->json('product');

                return $this->normalizeProduct($product);
            } catch (\Illuminate\Http\Client\ConnectionException $e) {
                // Connection timeout or network error
                Log::error('Open Food Facts API connection failed', [
                    'barcode' => $barcode,
                    'error' => $e->getMessage(),
                ]);

                throw $e;
            } catch (\Exception $e) {
                // Other errors (server error, malformed response, etc.)
                Log::error('Open Food Facts API error', [
                    'barcode' => $barcode,
                    'error' => $e->getMessage(),
                ]);

                throw $e;
            }
        });
    }

    /**
     * Normalize raw Open Food Facts product data to our standard format.
     *
     * @param  array  $raw  Raw product data from Open Food Facts API
     * @return array Normalized product data
     */
    private function normalizeProduct(array $raw): array
    {
        return [
            'name' => $raw['product_name'] ?? 'Unknown Product',
            'ingredients' => $raw['ingredients_text'] ?? '',
            'ingredients_list' => $raw['ingredients'] ?? [],
            'allergens' => $raw['allergens'] ?? '', // String format for easy searching
            'allergens_tags' => $raw['allergens_tags'] ?? [], // Array format for structured data
            'fat_100g' => (float) ($raw['nutriments']['fat_100g'] ?? 0.0),
            'saturated_fat_100g' => (float) ($raw['nutriments']['saturated-fat_100g'] ?? 0.0),
            'image_url' => $raw['image_url'] ?? null,
        ];
    }
}
