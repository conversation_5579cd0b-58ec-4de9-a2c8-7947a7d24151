<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * GeminiVisionService handles AI-powered meal analysis using Google's Gemini models
 *
 * Primary: Gemini 2.0 Flash Thinking (experimental)
 * Fallback: Gemini 1.5 Flash (stable)
 *
 * Implements circuit breaker pattern and exponential backoff retry logic
 */
class GeminiVisionService
{
    private const PRIMARY_MODEL = 'gemini-2.0-flash-thinking-exp-01-21';

    private const FALLBACK_MODEL = 'gemini-1.5-flash';

    private const TIMEOUT_SECONDS = 30;

    private const MAX_RETRIES = 2;

    private string $apiKey;

    private int $circuitBreakerFailures = 0;

    private const CIRCUIT_BREAKER_THRESHOLD = 5;

    private const CIRCUIT_BREAKER_TIMEOUT = 60; // seconds

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');

        if (empty($this->apiKey)) {
            throw new Exception('GEMINI_API_KEY not configured');
        }
    }

    /**
     * Analyze a meal photo and return structured data about ingredients and safety
     *
     * @param  string  $imagePath  Path to the meal photo
     * @param  array  $userContext  User profile data for personalization
     * @return array Structured meal analysis
     *
     * @throws Exception When analysis fails after all retries
     */
    public function analyzeMeal(string $imagePath, array $userContext = []): array
    {
        // Check circuit breaker
        if ($this->isCircuitBreakerOpen()) {
            throw new Exception('AI service temporarily unavailable (circuit breaker open)');
        }

        try {
            // Try primary model first
            $result = $this->analyzeWithRetry($imagePath, $userContext, self::PRIMARY_MODEL);

            // Reset circuit breaker on success
            $this->circuitBreakerFailures = 0;

            return $result;
        } catch (Exception $e) {
            Log::warning('Primary Gemini model failed, falling back to stable model', [
                'error' => $e->getMessage(),
            ]);

            // Fallback to stable model
            try {
                $result = $this->analyzeWithRetry($imagePath, $userContext, self::FALLBACK_MODEL);

                // Reset circuit breaker on success
                $this->circuitBreakerFailures = 0;

                return $result;
            } catch (Exception $fallbackError) {
                // Increment circuit breaker failures
                $this->circuitBreakerFailures++;

                Log::error('Both Gemini models failed', [
                    'primary_error' => $e->getMessage(),
                    'fallback_error' => $fallbackError->getMessage(),
                    'circuit_breaker_failures' => $this->circuitBreakerFailures,
                ]);

                throw new Exception('AI service unavailable: '.$fallbackError->getMessage());
            }
        }
    }

    /**
     * Analyze meal with exponential backoff retry logic
     */
    private function analyzeWithRetry(string $imagePath, array $userContext, string $model): array
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < self::MAX_RETRIES) {
            try {
                return $this->callGeminiApi($imagePath, $userContext, $model);
            } catch (Exception $e) {
                $lastException = $e;
                $attempt++;

                if ($attempt < self::MAX_RETRIES) {
                    // Exponential backoff: 1s, 2s, 4s
                    $waitSeconds = pow(2, $attempt - 1);

                    Log::info('Retrying Gemini API call', [
                        'attempt' => $attempt,
                        'max_retries' => self::MAX_RETRIES,
                        'wait_seconds' => $waitSeconds,
                        'model' => $model,
                    ]);

                    sleep($waitSeconds);
                }
            }
        }

        throw $lastException;
    }

    /**
     * Call Gemini API with the specified model
     */
    private function callGeminiApi(string $imagePath, array $userContext, string $model): array
    {
        // Read and encode image from public storage
        if (! Storage::disk('public')->exists($imagePath)) {
            throw new Exception("Image file not found: {$imagePath}");
        }

        $imageData = base64_encode(Storage::disk('public')->get($imagePath));
        $mimeType = Storage::disk('public')->mimeType($imagePath);

        // Build prompt with user context
        $prompt = $this->buildPrompt($userContext);

        // Make API request
        $response = Http::timeout(self::TIMEOUT_SECONDS)
            ->post("https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent?key={$this->apiKey}", [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'text' => $prompt,
                            ],
                            [
                                'inline_data' => [
                                    'mime_type' => $mimeType,
                                    'data' => $imageData,
                                ],
                            ],
                        ],
                    ],
                ],
                'generationConfig' => [
                    'temperature' => 0.4,
                    'topK' => 32,
                    'topP' => 1,
                    'maxOutputTokens' => 2048,
                    'responseMimeType' => 'application/json',
                ],
            ]);

        if (! $response->successful()) {
            throw new Exception("Gemini API error: {$response->status()} - {$response->body()}");
        }

        $data = $response->json();

        // Extract and validate response
        return $this->parseAndValidateResponse($data, $model);
    }

    /**
     * Build the analysis prompt with user context
     */
    private function buildPrompt(array $userContext): string
    {
        $basePrompt = 'Analyze this image and determine if it contains food or a meal.';

        $basePrompt .= "\n\nReturn JSON with this exact structure:
{
  \"is_food\": true,
  \"meal_name\": \"Name of the meal or 'Not a meal' if no food detected\",
  \"detected_ingredients\": [\"ingredient1\", \"ingredient2\", ...],
  \"base_score\": 100,
  \"confidence\": 0.95,
  \"reasoning\": \"Brief explanation of the meal analysis or why this is not food\"
}

IMPORTANT:
- Set \"is_food\" to false if the image does not contain any food or meal
- If is_food is false, set meal_name to \"Not a meal\", detected_ingredients to [], base_score to null, and explain in reasoning why this is not food
- Only analyze food safety if is_food is true";

        // Add user context if available
        if (! empty($userContext)) {
            $basePrompt .= "\n\nUser context for personalization:";

            if (isset($userContext['triggers']) && count($userContext['triggers']) > 0) {
                $triggerNames = array_map(fn ($t) => $t['name'], $userContext['triggers']);
                $basePrompt .= "\n- Known triggers: ".implode(', ', $triggerNames);
            }

            if (isset($userContext['allergens']) && count($userContext['allergens']) > 0) {
                $basePrompt .= "\n- Allergens: ".implode(', ', $userContext['allergens']);
            }

            if (isset($userContext['dietary_preferences']) && count($userContext['dietary_preferences']) > 0) {
                $basePrompt .= "\n- Dietary preferences: ".implode(', ', $userContext['dietary_preferences']);
            }
        }

        return $basePrompt;
    }

    /**
     * Parse and validate Gemini API response
     */
    private function parseAndValidateResponse(array $data, string $model): array
    {
        // Extract JSON from response
        if (! isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new Exception('Invalid Gemini API response structure');
        }

        $jsonText = $data['candidates'][0]['content']['parts'][0]['text'];
        $result = json_decode($jsonText, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('Gemini JSON parse error', [
                'error' => json_last_error_msg(),
                'raw_text' => substr($jsonText, 0, 500), // Log first 500 chars
            ]);
            throw new Exception('Failed to parse Gemini response as JSON: '.json_last_error_msg());
        }

        // Log the parsed result to debug validation issues
        Log::info('Gemini API response parsed', [
            'has_meal_name' => isset($result['meal_name']),
            'has_ingredients' => isset($result['detected_ingredients']),
            'ingredients_count' => isset($result['detected_ingredients']) ? count($result['detected_ingredients']) : 0,
            'has_base_score' => isset($result['base_score']),
            'response_keys' => array_keys($result ?? []),
        ]);

        // Validate required fields
        $this->validateSchema($result);

        // Add metadata
        $result['ai_model_used'] = $model;
        $result['analyzed_at'] = now();

        return $result;
    }

    /**
     * Validate JSON schema matches expected structure
     */
    private function validateSchema(array $data): void
    {
        $requiredFields = ['is_food', 'meal_name', 'detected_ingredients', 'base_score', 'confidence', 'reasoning'];

        foreach ($requiredFields as $field) {
            if (! array_key_exists($field, $data)) {
                throw new Exception("Missing required field in Gemini response: {$field}");
            }
        }

        // Validate is_food
        if (! is_bool($data['is_food'])) {
            throw new Exception('is_food must be a boolean');
        }

        // Validate types
        if (! is_string($data['meal_name']) || empty($data['meal_name'])) {
            throw new Exception('meal_name must be a non-empty string');
        }

        if (! is_array($data['detected_ingredients'])) {
            throw new Exception('detected_ingredients must be an array');
        }

        // Log non-food images
        if (! $data['is_food']) {
            Log::info('Non-food image detected', [
                'meal_name' => $data['meal_name'],
                'reasoning' => $data['reasoning'],
            ]);
        }

        // Allow empty array or null base_score for non-food images
        if ($data['is_food'] && count($data['detected_ingredients']) === 0) {
            Log::warning('Gemini detected no ingredients in food image', [
                'meal_name' => $data['meal_name'] ?? 'unknown',
            ]);
        }

        // base_score can be null for non-food images
        if ($data['base_score'] !== null && (! is_numeric($data['base_score']) || $data['base_score'] < 0 || $data['base_score'] > 100)) {
            throw new Exception('base_score must be null or a number between 0 and 100');
        }

        if (! is_numeric($data['confidence']) || $data['confidence'] < 0 || $data['confidence'] > 1) {
            throw new Exception('confidence must be a number between 0 and 1');
        }

        if (! is_string($data['reasoning']) || empty($data['reasoning'])) {
            throw new Exception('reasoning must be a non-empty string');
        }
    }

    /**
     * Check if circuit breaker is open (too many recent failures)
     */
    private function isCircuitBreakerOpen(): bool
    {
        return $this->circuitBreakerFailures >= self::CIRCUIT_BREAKER_THRESHOLD;
    }
}
