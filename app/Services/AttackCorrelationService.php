<?php

namespace App\Services;

use App\Models\Attack;
use App\Models\Scan;
use Illuminate\Support\Facades\DB;

class AttackCorrelationService
{
    /**
     * Correlate scans with an attack.
     * T183: Find scans 3-8 hours before attack onset.
     * T184: Implements 3-8 hour time window per FR-065.
     * T185: Creates attack_scans pivot records with hours_before_attack and match_weight.
     *
     * @return int Number of correlations created
     */
    public function correlateScans(Attack $attack): int
    {
        $attackTime = $attack->onset_at;

        // Find scans 3-8 hours before attack (T184)
        $scans = Scan::where('user_id', $attack->user_id)
            ->where('status', 'completed') // Only completed scans
            ->where('created_at', '>=', $attackTime->copy()->subHours(8))
            ->where('created_at', '<=', $attackTime->copy()->subHours(3))
            ->get();

        $correlationCount = 0;

        foreach ($scans as $scan) {
            // Calculate hours before attack
            $hoursBeforeAttack = $attackTime->diffInHours($scan->created_at, true);

            // Calculate match weight (T185)
            $matchWeight = $this->calculateMatchWeight($scan, $attack);

            // Create correlation record in attack_scans pivot table
            DB::table('attack_scans')->insert([
                'attack_id' => $attack->id,
                'scan_id' => $scan->id,
                'hours_before_attack' => $hoursBeforeAttack,
                'match_weight' => $matchWeight,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $correlationCount++;
        }

        return $correlationCount;
    }

    /**
     * Calculate match weight for a scan-attack correlation.
     * Match weight represents how closely the scan ingredients match known triggers.
     *
     * @return int Match weight (0-100)
     */
    private function calculateMatchWeight(Scan $scan, Attack $attack): int
    {
        $baseWeight = 50; // Base weight for any correlation

        // Get user's triggers
        $userTriggers = $attack->user->triggers()
            ->where('status', 'active')
            ->get();

        if ($userTriggers->isEmpty()) {
            return $baseWeight;
        }

        // Check if scan contains any known triggers
        $detectedIngredients = $scan->detected_ingredients ?? [];
        if (! is_array($detectedIngredients)) {
            return $baseWeight;
        }

        $triggerMatches = 0;
        foreach ($userTriggers as $trigger) {
            foreach ($detectedIngredients as $ingredient) {
                $ingredientName = is_array($ingredient) ? ($ingredient['name'] ?? $ingredient) : $ingredient;

                if (stripos($ingredientName, $trigger->trigger_name) !== false ||
                    stripos($trigger->trigger_name, $ingredientName) !== false) {
                    $triggerMatches++;
                    // Add weight based on severity
                    $baseWeight += match ($trigger->severity) {
                        'high' => 20,
                        'moderate' => 15,
                        'low' => 10,
                        default => 10,
                    };
                }
            }
        }

        // Cap at 100
        return min(100, $baseWeight);
    }
}
