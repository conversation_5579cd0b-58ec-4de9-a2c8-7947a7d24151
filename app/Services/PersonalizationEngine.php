<?php

namespace App\Services;

use App\Models\User;

/**
 * PersonalizationEngine calculates personalized safety scores and generates
 * customized reasoning based on user's triggers, allergens, and health profile
 */
class PersonalizationEngine
{
    // Trigger severity point deductions
    private const DEDUCTION_LOW = 15;

    private const DEDUCTION_MODERATE = 25;

    private const DEDUCTION_HIGH = 40;

    // Common ingredient categories for semantic matching
    private const INGREDIENT_CATEGORIES = [
        'dairy' => ['milk', 'cheese', 'cream', 'butter', 'yogurt', 'ranch', 'sour cream', 'ice cream', 'whey', 'casein'],
        'nuts' => ['almond', 'walnut', 'pecan', 'cashew', 'pistachio', 'hazelnut', 'macadamia'],
        'shellfish' => ['shrimp', 'crab', 'lobster', 'clam', 'oyster', 'mussel', 'scallop'],
        'peanut' => ['peanut', 'peanuts'],
        'tree nuts' => ['almond', 'walnut', 'pecan', 'cashew', 'pistachio', 'hazelnut', 'macadamia'],
    ];

    /**
     * Analyze a barcode-scanned product and return personalized safety score
     * This is the synchronous analysis method for barcode scans (T137)
     *
     * @param  User  $user  The user to personalize for
     * @param  array  $product  Product data from Open Food Facts
     * @return array Analysis result with safety_score and personalized_reasoning
     */
    public function analyzeProduct(User $user, array $product): array
    {
        // Extract ingredients from product data
        $ingredients = [];

        // Add ingredients from text
        if (! empty($product['ingredients'])) {
            $ingredientsText = $product['ingredients'];
            // Split by common delimiters: comma, semicolon, period
            $ingredients = array_merge($ingredients, preg_split('/[,;.]/', $ingredientsText, -1, PREG_SPLIT_NO_EMPTY));
        }

        // Add allergens
        if (! empty($product['allergens'])) {
            if (is_array($product['allergens'])) {
                $ingredients = array_merge($ingredients, $product['allergens']);
            } else {
                $ingredients = array_merge($ingredients, preg_split('/[,;.]/', $product['allergens'], -1, PREG_SPLIT_NO_EMPTY));
            }
        }

        // Trim whitespace from all ingredients
        $ingredients = array_map('trim', $ingredients);
        $ingredients = array_filter($ingredients); // Remove empty strings

        // Calculate safety score using existing logic
        $scoreResult = $this->calculateSafetyScore($user, $ingredients);

        // Generate personalized reasoning
        $personalizedReasoning = $this->generatePersonalizedReasoning($user, $scoreResult);

        return [
            'safety_score' => $scoreResult['safety_score'],
            'personalized_reasoning' => $personalizedReasoning,
            'trigger_warnings' => $scoreResult['trigger_warnings'],
            'allergen_detected' => $scoreResult['allergen_detected'],
            'detected_allergens' => $scoreResult['detected_allergens'] ?? [],
        ];
    }

    /**
     * Calculate personalized safety score for a meal based on user's triggers and allergens
     *
     * @param  User  $user  The user to personalize for
     * @param  array  $ingredients  List of detected ingredients
     * @return array Safety score calculation result
     */
    public function calculateSafetyScore(User $user, array $ingredients): array
    {
        $baseScore = 100;
        $triggerWarnings = [];
        $allergenDetected = false;
        $detectedAllergens = [];

        // Load user profile and triggers
        $user->load(['profile', 'triggers']);

        // Check for allergens first (highest priority - overrides everything)
        if ($user->profile && $user->profile->allergens) {
            foreach ($user->profile->allergens as $allergen) {
                if ($this->ingredientContains($ingredients, $allergen)) {
                    $allergenDetected = true;
                    $detectedAllergens[] = $allergen;
                }
            }
        }

        // If allergen detected, score = 0 immediately
        if ($allergenDetected) {
            return [
                'safety_score' => 0,
                'base_score' => 100,
                'deductions' => 100,
                'trigger_warnings' => [],
                'allergen_detected' => true,
                'detected_allergens' => $detectedAllergens,
            ];
        }

        // Check user's confirmed triggers
        $deductions = 0;

        foreach ($user->triggers as $trigger) {
            // Only consider confirmed triggers (not denied or pending)
            if ($trigger->confirmed_at === null || $trigger->denied_at !== null) {
                continue;
            }

            // Check if this trigger is in the ingredients
            if ($this->ingredientContains($ingredients, $trigger->trigger_name)) {
                // Deduct points based on severity
                $deduction = $this->getDeductionForSeverity($trigger->severity);
                $deductions += $deduction;

                $triggerWarnings[] = [
                    'trigger' => $trigger->trigger_name,
                    'severity' => $trigger->severity,
                    'deduction' => $deduction,
                ];
            }
        }

        // Calculate final score (cannot go below 0)
        $finalScore = max(0, $baseScore - $deductions);

        return [
            'safety_score' => $finalScore,
            'base_score' => $baseScore,
            'deductions' => $deductions,
            'trigger_warnings' => $triggerWarnings,
            'allergen_detected' => false,
            'detected_allergens' => [],
        ];
    }

    /**
     * Generate personalized reasoning text for the user
     *
     * @param  User  $user  The user to personalize for
     * @param  array  $scoreResult  Result from calculateSafetyScore()
     * @param  string|null  $mealName  Optional meal name to check for safe history (T229)
     * @return string Personalized reasoning text
     */
    public function generatePersonalizedReasoning(User $user, array $scoreResult, ?string $mealName = null): string
    {
        $score = $scoreResult['safety_score'];

        // Allergen critical warning
        if ($scoreResult['allergen_detected']) {
            $allergenList = implode(', ', $scoreResult['detected_allergens']);

            return "⚠️ CRITICAL ALLERGEN ALERT ⚠️\n\n".
                   "This meal contains YOUR allergen(s): {$allergenList}.\n\n".
                   'DO NOT EAT THIS MEAL. This could cause a dangerous allergic reaction. '.
                   'Always check with restaurant staff about allergen ingredients.';
        }

        // High trigger warnings
        if ($score <= 30 && count($scoreResult['trigger_warnings']) > 0) {
            $triggerNames = array_map(fn ($tw) => $tw['trigger'], $scoreResult['trigger_warnings']);
            $triggerList = implode(', ', $triggerNames);

            return "🚨 HIGH RISK FOR YOU\n\n".
                   "This meal contains YOUR known trigger(s): {$triggerList}.\n\n".
                   'Based on your personal history, this meal is likely to cause symptoms. '.
                   'Consider choosing a different option.';
        }

        // Moderate trigger warnings
        if ($score <= 60 && count($scoreResult['trigger_warnings']) > 0) {
            $triggerNames = array_map(fn ($tw) => $tw['trigger'], $scoreResult['trigger_warnings']);
            $triggerList = implode(', ', $triggerNames);

            return "⚠️ MODERATE RISK FOR YOU\n\n".
                   "This meal contains: {$triggerList}, which YOU've identified as YOUR triggers.\n\n".
                   'You may experience some symptoms. Consider eating a smaller portion or having it earlier in the day.';
        }

        // Safe score (80-100)
        if ($score >= 80) {
            // Check if user has safely eaten similar meals before (T229)
            $safeHistory = $mealName ? $this->checkSafelyEatenSimilarMeals($user, $mealName) : null;

            if (count($scoreResult['trigger_warnings']) === 0) {
                $message = "✅ SAFE FOR YOU\n\n".
                          "Great choice! This meal doesn't contain any of YOUR known triggers. ".
                          'Based on your personal profile, this should be well-tolerated.';

                if ($safeHistory) {
                    $message .= "\n\n💚 {$safeHistory}";
                }

                return $message;
            } else {
                $message = "✅ MOSTLY SAFE FOR YOU\n\n".
                          'This meal has minimal trigger ingredients based on YOUR profile. '.
                          'It should be well-tolerated by most people with your dietary needs.';

                if ($safeHistory) {
                    $message .= "\n\n💚 {$safeHistory}";
                }

                return $message;
            }
        }

        // Default moderate score (61-79)
        if (count($scoreResult['trigger_warnings']) > 0) {
            $triggerNames = array_map(fn ($tw) => $tw['trigger'], $scoreResult['trigger_warnings']);
            $triggerList = implode(', ', $triggerNames);

            return "💛 PROCEED WITH CAUTION\n\n".
                   "This meal contains some ingredients that may affect YOU: {$triggerList}.\n\n".
                   "Everyone's tolerance is different. Pay attention to how you feel after eating.";
        }

        return 'This meal has been analyzed based on YOUR personal profile and triggers.';
    }

    /**
     * Get point deduction for a trigger severity level
     */
    private function getDeductionForSeverity(string $severity): int
    {
        return match ($severity) {
            'low' => self::DEDUCTION_LOW,
            'moderate' => self::DEDUCTION_MODERATE,
            'high' => self::DEDUCTION_HIGH,
            default => 0,
        };
    }

    /**
     * Check if ingredients list contains a specific trigger/allergen
     * Uses fuzzy matching and semantic category matching
     */
    private function ingredientContains(array $ingredients, string $searchTerm): bool
    {
        $searchLower = strtolower($searchTerm);

        foreach ($ingredients as $ingredient) {
            $ingredientLower = strtolower($ingredient);

            // Direct match
            if ($ingredientLower === $searchLower) {
                return true;
            }

            // Partial match (e.g., "fried" matches "fried chicken")
            if (str_contains($ingredientLower, $searchLower)) {
                return true;
            }

            // Reverse partial match (e.g., "chocolate chips" matches "chocolate")
            if (str_contains($searchLower, $ingredientLower)) {
                return true;
            }

            // Semantic category matching (e.g., "dairy" matches "cheese")
            if ($this->checkSemanticMatch($searchLower, $ingredientLower)) {
                return true;
            }

            // Word-level matching (e.g., "Fried Foods" matches "french fries" via "fried")
            $searchWords = explode(' ', $searchLower);
            $ingredientWords = explode(' ', $ingredientLower);

            foreach ($searchWords as $searchWord) {
                // Skip very short words
                if (strlen($searchWord) < 4) {
                    continue;
                }

                foreach ($ingredientWords as $ingredientWord) {
                    // Substring containment check
                    if (str_contains($ingredientWord, $searchWord) || str_contains($searchWord, $ingredientWord)) {
                        return true;
                    }

                    // Similarity check for words like "fried" vs "fries" (share common root)
                    // Use Levenshtein distance for close matches
                    $distance = levenshtein($searchWord, $ingredientWord);
                    $maxLength = max(strlen($searchWord), strlen($ingredientWord));

                    // If words are at least 70% similar and differ by max 2 characters
                    if ($distance <= 2 && ($distance / $maxLength) <= 0.3) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if ingredient matches a semantic category in the search term
     */
    private function checkSemanticMatch(string $searchTerm, string $ingredient): bool
    {
        foreach (self::INGREDIENT_CATEGORIES as $category => $items) {
            // Check if search term contains this category name
            if (str_contains($searchTerm, $category)) {
                // Check if ingredient contains any item from this category
                foreach ($items as $item) {
                    if (str_contains($ingredient, $item)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if user has safely eaten similar meals before (T229)
     * Returns message if similar safe meals found, null otherwise
     *
     * Logic: Find scans with similar meal names + outcome='ate' + no attacks within 8 hours
     *
     * @param  User  $user  The user to check history for
     * @param  string  $mealName  Current meal name to check against
     * @return string|null Message about safely eaten meals, or null if none found
     */
    public function checkSafelyEatenSimilarMeals(User $user, string $mealName): ?string
    {
        if (empty($mealName)) {
            return null;
        }

        // Find similar meals that were eaten (not avoided)
        $similarMeals = \App\Models\Scan::where('user_id', $user->id)
            ->where('outcome', 'ate')
            ->where('status', 'completed')
            ->whereNotNull('meal_name')
            ->get()
            ->filter(function ($scan) use ($mealName) {
                // Check for similar meal names (case-insensitive, partial match)
                return stripos($scan->meal_name, $mealName) !== false
                    || stripos($mealName, $scan->meal_name) !== false
                    || $this->areMealsSimilar($scan->meal_name, $mealName);
            });

        if ($similarMeals->isEmpty()) {
            return null;
        }

        // Check each similar meal for attacks within 8 hours after eating
        $safeMeals = $similarMeals->filter(function ($scan) use ($user) {
            // Check if there were any attacks within 8 hours after this scan
            $hasAttack = \App\Models\Attack::where('user_id', $user->id)
                ->where('onset_at', '>=', $scan->created_at)
                ->where('onset_at', '<=', $scan->created_at->addHours(8))
                ->exists();

            return ! $hasAttack; // Keep only meals that had NO attacks
        });

        if ($safeMeals->isEmpty()) {
            return null;
        }

        $count = $safeMeals->count();
        $lastSafeDate = $safeMeals->sortByDesc('created_at')->first()->created_at;

        if ($count === 1) {
            return "You've safely eaten a similar meal before ({$lastSafeDate->diffForHumans()}) with no symptoms.";
        } else {
            return "You've safely eaten similar meals {$count} times before with no symptoms. Last time: {$lastSafeDate->diffForHumans()}.";
        }
    }

    /**
     * Check if two meal names are similar using fuzzy matching
     */
    private function areMealsSimilar(string $meal1, string $meal2): bool
    {
        // Normalize meal names (lowercase, remove common words)
        $commonWords = ['with', 'and', 'or', 'the', 'a', 'an'];

        $normalize = function ($meal) use ($commonWords) {
            $meal = strtolower($meal);
            foreach ($commonWords as $word) {
                $meal = str_replace(" $word ", ' ', $meal);
            }

            return trim($meal);
        };

        $normalized1 = $normalize($meal1);
        $normalized2 = $normalize($meal2);

        // Extract main food items (first 2-3 words)
        $words1 = explode(' ', $normalized1);
        $words2 = explode(' ', $normalized2);

        // Check if main words overlap
        $mainWords1 = array_slice($words1, 0, min(3, count($words1)));
        $mainWords2 = array_slice($words2, 0, min(3, count($words2)));

        $overlap = count(array_intersect($mainWords1, $mainWords2));

        // Consider similar if at least 2 main words match
        return $overlap >= 2;
    }
}
