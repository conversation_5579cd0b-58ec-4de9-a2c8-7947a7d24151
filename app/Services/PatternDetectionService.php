<?php

namespace App\Services;

use App\Models\Attack;
use App\Models\PatternSuggestion;
use App\Models\Scan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class PatternDetectionService
{
    /**
     * Detect patterns from an attack and generate suggestions
     */
    public function detectPatterns(Attack $attack): array
    {
        Log::info('Starting pattern detection', [
            'attack_id' => $attack->id,
            'user_id' => $attack->user_id,
        ]);

        // Find correlations (scans 3-8 hours before attack)
        $correlations = $this->detectCorrelations($attack);

        if (empty($correlations)) {
            Log::info('No correlations found', ['attack_id' => $attack->id]);

            return [];
        }

        Log::info('Correlations detected', [
            'attack_id' => $attack->id,
            'correlation_count' => count($correlations),
        ]);

        // Group correlations by suspected ingredient
        $ingredientGroups = $this->groupByIngredient($correlations);

        // Calculate confidence scores for each suspected trigger
        $suggestions = [];
        foreach ($ingredientGroups as $ingredient => $ingredientCorrelations) {
            $confidenceScore = $this->calculateConfidenceScore($attack->user, $ingredient, $ingredientCorrelations);

            // Only create suggestions meeting 60% minimum threshold
            if ($confidenceScore >= 60.0) {
                $suggestion = $this->createOrUpdateSuggestion(
                    $attack->user,
                    $ingredient,
                    $confidenceScore,
                    $ingredientCorrelations
                );

                $suggestions[] = $suggestion;
            }
        }

        Log::info('Pattern detection complete', [
            'attack_id' => $attack->id,
            'suggestions_created' => count($suggestions),
        ]);

        return $suggestions;
    }

    /**
     * Find scans 3-8 hours before attack
     */
    public function detectCorrelations(Attack $attack): array
    {
        $attackTime = $attack->onset_at;

        // Calculate time window: 3-8 hours before attack
        $windowStart = $attackTime->copy()->subHours(8);
        $windowEnd = $attackTime->copy()->subHours(3);

        // Find completed scans within window
        $scans = Scan::query()
            ->where('user_id', $attack->user_id)
            ->where('status', 'completed')
            ->whereBetween('created_at', [$windowStart, $windowEnd])
            ->orderBy('created_at', 'desc')
            ->get();

        Log::info('Scanning for correlations', [
            'attack_id' => $attack->id,
            'window_start' => $windowStart->toISOString(),
            'window_end' => $windowEnd->toISOString(),
            'scans_found' => $scans->count(),
        ]);

        $correlations = [];

        foreach ($scans as $scan) {
            $hoursBeforeAttack = $scan->created_at->diffInHours($attackTime, false);

            // Create attack_scans pivot record
            $attack->scans()->syncWithoutDetaching([
                $scan->id => [
                    'hours_before_attack' => abs($hoursBeforeAttack),
                    'match_weight' => 100, // Exact ingredient match (default)
                ],
            ]);

            // Build correlation data
            foreach ($scan->detected_ingredients ?? [] as $ingredient) {
                $correlations[] = [
                    'scan_id' => $scan->id,
                    'attack_id' => $attack->id,
                    'ingredient' => $ingredient,
                    'hours_before_attack' => abs($hoursBeforeAttack),
                    'match_weight' => 100,
                    'scan_date' => $scan->created_at,
                ];
            }
        }

        return $correlations;
    }

    /**
     * Group correlations by ingredient
     */
    private function groupByIngredient(array $correlations): array
    {
        $groups = [];

        foreach ($correlations as $correlation) {
            // Handle ingredient as array or string
            $ingredientData = $correlation['ingredient'];
            $ingredientName = is_array($ingredientData)
                ? ($ingredientData['name'] ?? $ingredientData[0] ?? 'unknown')
                : $ingredientData;

            $ingredient = strtolower($ingredientName);

            if (! isset($groups[$ingredient])) {
                $groups[$ingredient] = [];
            }

            $groups[$ingredient][] = $correlation;
        }

        return $groups;
    }

    /**
     * Get all correlations for a specific ingredient across user's attacks
     */
    private function getCorrelationsForIngredient(User $user, string $ingredient): array
    {
        $ingredient = strtolower($ingredient);
        $correlations = [];

        // Get all user's attacks with their associated scans
        $attacks = Attack::query()
            ->where('user_id', $user->id)
            ->with(['scans' => function ($query) {
                $query->withPivot(['hours_before_attack', 'match_weight']);
            }])
            ->get();

        foreach ($attacks as $attack) {
            foreach ($attack->scans as $scan) {
                // Check if this scan contains the ingredient
                $scanIngredients = array_map('strtolower', $scan->detected_ingredients ?? []);

                if (in_array($ingredient, $scanIngredients)) {
                    $correlations[] = [
                        'scan_id' => $scan->id,
                        'attack_id' => $attack->id,
                        'ingredient' => $ingredient,
                        'hours_before_attack' => $scan->pivot->hours_before_attack,
                        'match_weight' => $scan->pivot->match_weight,
                        'scan_date' => $scan->created_at,
                    ];
                }
            }
        }

        return $correlations;
    }

    /**
     * Calculate confidence score per clarifications.md Q1 formula
     *
     * Formula: (Correlation Count × 25%) + (Match Weight × 20%) + (Consistency × 30%) + (Recency × 25%)
     */
    public function calculateConfidence(User $user, string $ingredient): float
    {
        // Get all attack_scans correlations for this user and ingredient
        $correlations = $this->getCorrelationsForIngredient($user, $ingredient);

        if (empty($correlations)) {
            return 0.0;
        }

        return $this->calculateConfidenceScore($user, $ingredient, $correlations);
    }

    /**
     * Internal confidence score calculation
     */
    private function calculateConfidenceScore(User $user, string $ingredient, array $correlations): float
    {
        // Correlation Count component (25%)
        $correlationCount = count($correlations);
        $correlationCountScore = min(100, ($correlationCount / 3) * 100) * 0.25; // 3+ correlations = 100%

        // Match Weight component (20%)
        $avgMatchWeight = collect($correlations)->avg('match_weight');
        $matchWeightScore = $avgMatchWeight * 0.20;

        // Consistency component (30%) - how consistent are the time gaps?
        $timeGaps = collect($correlations)->pluck('hours_before_attack')->toArray();
        $avgGap = collect($timeGaps)->avg();
        $variance = $this->calculateVariance($timeGaps, $avgGap);
        $consistency = max(0, 100 - ($variance * 10)); // Lower variance = higher consistency
        $consistencyScore = $consistency * 0.30;

        // Recency component (25%) - more recent attacks weighted higher
        $mostRecentDays = collect($correlations)
            ->map(fn ($c) => Carbon::parse($c['scan_date'])->diffInDays(now()))
            ->min();
        $recencyScore = max(0, 100 - ($mostRecentDays * 2)) * 0.25; // Decay 2% per day

        $totalScore = $correlationCountScore + $matchWeightScore + $consistencyScore + $recencyScore;

        Log::info('Confidence score calculated', [
            'ingredient' => $ingredient,
            'correlation_count' => $correlationCount,
            'correlation_count_score' => round($correlationCountScore, 2),
            'match_weight_score' => round($matchWeightScore, 2),
            'consistency_score' => round($consistencyScore, 2),
            'recency_score' => round($recencyScore, 2),
            'total_score' => round($totalScore, 2),
        ]);

        return round($totalScore, 2);
    }

    /**
     * Calculate variance for consistency measurement
     */
    private function calculateVariance(array $values, float $mean): float
    {
        if (count($values) <= 1) {
            return 0;
        }

        $sumSquaredDiff = collect($values)
            ->map(fn ($val) => pow($val - $mean, 2))
            ->sum();

        return $sumSquaredDiff / count($values);
    }

    /**
     * Create or update pattern suggestion
     */
    private function createOrUpdateSuggestion(
        User $user,
        string $ingredient,
        float $confidenceScore,
        array $correlations
    ): PatternSuggestion {
        // Check if suggestion already exists for this ingredient
        $suggestion = PatternSuggestion::query()
            ->where('user_id', $user->id)
            ->where('suspected_trigger_name', $ingredient)
            ->where('status', 'pending')
            ->first();

        // T189: Store evidence array with scan_id, attack_id, hours_before, match_weight
        $evidence = [
            'correlations' => collect($correlations)->map(function ($c) {
                // Get attack date for this correlation
                $attack = Attack::find($c['attack_id']);

                return [
                    'scan_id' => $c['scan_id'],
                    'attack_id' => $c['attack_id'],
                    'hours_before_attack' => $c['hours_before_attack'],
                    'match_weight' => $c['match_weight'] ?? 100,
                    'scan_date' => Carbon::parse($c['scan_date'])->toISOString(),
                    'attack_date' => $attack?->onset_at?->toISOString(), // T188: Include attack date
                ];
            })->toArray(),
            'total_occurrences' => count($correlations),
            'avg_time_gap' => round(collect($correlations)->avg('hours_before_attack'), 1),
        ];

        if ($suggestion) {
            // Update existing suggestion with new evidence
            $suggestion->update([
                'confidence_score' => $confidenceScore,
                'correlation_count' => count($correlations),
                'evidence' => $evidence,
            ]);

            Log::info('Updated existing pattern suggestion', [
                'suggestion_id' => $suggestion->id,
                'ingredient' => $ingredient,
                'confidence_score' => $confidenceScore,
            ]);
        } else {
            // Create new suggestion
            $suggestion = PatternSuggestion::create([
                'user_id' => $user->id,
                'suspected_trigger_name' => $ingredient,
                'confidence_score' => $confidenceScore,
                'correlation_count' => count($correlations),
                'evidence' => $evidence,
                'detected_at' => now(),
                'status' => 'pending',
            ]);

            Log::info('Created new pattern suggestion', [
                'suggestion_id' => $suggestion->id,
                'ingredient' => $ingredient,
                'confidence_score' => $confidenceScore,
            ]);
        }

        return $suggestion;
    }
}
