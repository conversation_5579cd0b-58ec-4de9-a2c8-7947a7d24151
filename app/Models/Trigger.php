<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Trigger extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'category',
        'description',
        'severity',
    ];

    /**
     * Get the users that have this trigger (many-to-many with pivot data).
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_triggers')
            ->withPivot('confidence_score', 'confirmed_at', 'denied_at', 'last_occurred_at', 'occurrences_count')
            ->withTimestamps();
    }
}
