<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Scan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'is_food',
        'image_path',
        'barcode',
        'meal_name',
        'meal_type',
        'detected_ingredients',
        'base_score',
        'adjusted_score',
        'confidence_score',
        'personalized_reasoning',
        'trigger_warnings',
        'status',
        'analyzed_at',
        'outcome',
        'reaction_severity',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_food' => 'boolean',
            'detected_ingredients' => 'array',
            'trigger_warnings' => 'array',
            'base_score' => 'integer',
            'adjusted_score' => 'integer',
            'confidence_score' => 'decimal:2',
            'analyzed_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the scan.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the ingredients detected in this scan.
     */
    public function ingredients(): HasMany
    {
        return $this->hasMany(ScanIngredient::class);
    }
}
