<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SyncQueueItem extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sync_queue';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'change_type',
        'entity_type',
        'entity_id',
        'change_data',
        'status',
        'broadcast_via_sse',
        'processed_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'entity_id' => 'integer',
            'change_data' => 'array',
            'broadcast_via_sse' => 'boolean',
            'processed_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the sync queue item.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
