<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScanIngredient extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'scan_id',
        'ingredient_name',
        'trigger_id',
        'category',
        'risk_level',
        'matched_by',
    ];

    /**
     * Get the scan that owns this ingredient.
     */
    public function scan(): BelongsTo
    {
        return $this->belongsTo(Scan::class);
    }

    /**
     * Get the trigger associated with this ingredient (nullable).
     */
    public function trigger(): BelongsTo
    {
        return $this->belongsTo(Trigger::class);
    }
}
