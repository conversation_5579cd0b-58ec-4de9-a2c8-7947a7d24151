<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Profile extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'age',
        'sex',
        'weight_kg',
        'height_cm',
        'activity_level',
        'health_conditions',
        'dietary_preferences',
        'allergens',
        'completion_percentage',
        'onboarding_completed',
        'onboarding_step',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'age' => 'integer',
            'weight_kg' => 'decimal:2',
            'height_cm' => 'decimal:2',
            'health_conditions' => 'array',
            'dietary_preferences' => 'array',
            'allergens' => 'array',
            'completion_percentage' => 'integer',
            'onboarding_completed' => 'boolean',
            'onboarding_step' => 'integer',
        ];
    }

    /**
     * Get the user that owns the profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
