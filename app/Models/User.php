<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Cashier\Billable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use Billable, HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'subscription_tier',
        'trial_used',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'trial_used' => 'boolean',
            'subscription_expires_at' => 'datetime',
            'trial_ends_at' => 'datetime',
            'last_synced_at' => 'datetime',
        ];
    }

    /**
     * Get the user's profile.
     */
    public function profile(): HasOne
    {
        return $this->hasOne(Profile::class);
    }

    /**
     * Get the user's personal triggers (direct relationship, not pivot).
     */
    public function triggers(): HasMany
    {
        return $this->hasMany(UserTrigger::class);
    }

    /**
     * Get the user's scans.
     */
    public function scans(): HasMany
    {
        return $this->hasMany(Scan::class);
    }

    /**
     * Get the user's attacks.
     */
    public function attacks(): HasMany
    {
        return $this->hasMany(Attack::class);
    }

    /**
     * Get the user's pattern suggestions.
     */
    public function patternSuggestions(): HasMany
    {
        return $this->hasMany(PatternSuggestion::class);
    }

    /**
     * Get the user's sync queue items.
     */
    public function syncQueue(): HasMany
    {
        return $this->hasMany(SyncQueueItem::class);
    }
}
