<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BarcodeCache extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'barcode_cache';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'barcode',
        'product_name',
        'ingredients_text',
        'ingredients_list',
        'allergens',
        'fat_100g',
        'saturated_fat_100g',
        'image_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'ingredients_list' => 'array',
            'allergens' => 'array',
            'fat_100g' => 'decimal:2',
            'saturated_fat_100g' => 'decimal:2',
        ];
    }
}
