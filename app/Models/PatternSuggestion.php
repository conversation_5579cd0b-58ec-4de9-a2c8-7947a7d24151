<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PatternSuggestion extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'pattern_detections';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'trigger_id',
        'suspected_trigger_name',
        'detected_at',
        'correlation_count',
        'match_weight',
        'consistency_score',
        'recency_score',
        'confidence_score',
        'evidence',
        'status',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'detected_at' => 'datetime',
            'correlation_count' => 'integer',
            'match_weight' => 'decimal:2',
            'consistency_score' => 'decimal:2',
            'recency_score' => 'decimal:2',
            'confidence_score' => 'decimal:2',
            'evidence' => 'array',
        ];
    }

    /**
     * Get the user that owns this pattern suggestion.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the trigger associated with this pattern (nullable).
     */
    public function trigger(): BelongsTo
    {
        return $this->belongsTo(Trigger::class);
    }
}
