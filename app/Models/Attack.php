<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attack extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'onset_at',
        'duration_minutes',
        'pain_intensity',
        'pain_location',
        'symptoms',
        'medical_care_type',
        'diagnosis_received',
        'treatment_received',
        'correlated_scans_analyzed',
        'suspected_trigger_id',
        'correlation_confidence',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'onset_at' => 'datetime',
            'duration_minutes' => 'integer',
            'pain_intensity' => 'integer',
            'pain_location' => 'array',
            'symptoms' => 'array',
            'correlated_scans_analyzed' => 'boolean',
            'correlation_confidence' => 'decimal:2',
        ];
    }

    /**
     * Get the user that owns the attack.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the suspected trigger for this attack.
     */
    public function suspectedTrigger(): BelongsTo
    {
        return $this->belongsTo(UserTrigger::class, 'suspected_trigger_id');
    }

    /**
     * Get the scans associated with this attack (for pattern detection).
     */
    public function scans(): BelongsToMany
    {
        return $this->belongsToMany(Scan::class, 'attack_scans')
            ->withPivot(['hours_before_attack', 'match_weight'])
            ->withTimestamps();
    }
}
