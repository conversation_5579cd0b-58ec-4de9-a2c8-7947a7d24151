<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTrigger extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_triggers';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'trigger_name',
        'severity',
        'identification_source',
        'confidence_score',
        'attack_correlation_count',
        'total_exposures',
        'last_attack_date',
        'notes',
        'status',
        'confirmed_at',
        'denied_at',
        'rejection_count',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'confidence_score' => 'decimal:2',
            'attack_correlation_count' => 'integer',
            'total_exposures' => 'integer',
            'rejection_count' => 'integer',
            'last_attack_date' => 'datetime',
            'confirmed_at' => 'datetime',
            'denied_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the trigger.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to only get active (confirmed) triggers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
            ->whereNotNull('confirmed_at')
            ->whereNull('denied_at');
    }

    /**
     * Scope to only get pending triggers.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending_confirmation');
    }

    /**
     * Scope to only get rejected triggers.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected')
            ->whereNotNull('denied_at');
    }
}
