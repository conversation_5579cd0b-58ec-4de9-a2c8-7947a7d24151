<?php

namespace App\Console\Commands;

use App\Models\Scan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class MigrateScansToPublic extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scans:migrate-to-public {--dry-run : Preview changes without executing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing scan images from local storage to public storage';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('Running in DRY RUN mode - no files will be moved');
        }

        // Get all scans with image_path
        $scans = Scan::whereNotNull('image_path')->get();

        if ($scans->isEmpty()) {
            $this->info('No scans found with images to migrate.');

            return self::SUCCESS;
        }

        $this->info("Found {$scans->count()} scans with images to migrate.");

        $migrated = 0;
        $skipped = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar($scans->count());
        $progressBar->start();

        foreach ($scans as $scan) {
            try {
                $oldPath = $scan->image_path;

                // Check if file exists in local storage
                if (! Storage::disk('local')->exists($oldPath)) {
                    $this->newLine();
                    $this->warn("File not found in local storage: {$oldPath}");
                    $skipped++;
                    $progressBar->advance();

                    continue;
                }

                // Check if file already exists in public storage
                if (Storage::disk('public')->exists($oldPath)) {
                    $this->newLine();
                    $this->comment("File already exists in public storage: {$oldPath}");
                    $skipped++;
                    $progressBar->advance();

                    continue;
                }

                if (! $isDryRun) {
                    // Copy file from local to public storage
                    $fileContents = Storage::disk('local')->get($oldPath);
                    Storage::disk('public')->put($oldPath, $fileContents);

                    // Verify the copy was successful
                    if (Storage::disk('public')->exists($oldPath)) {
                        // Optionally delete from local storage to free up space
                        // Storage::disk('local')->delete($oldPath);

                        $migrated++;
                    } else {
                        throw new \Exception('Failed to verify file copy to public storage');
                    }
                } else {
                    $migrated++;
                }

                $progressBar->advance();
            } catch (\Exception $e) {
                $this->newLine();
                $this->error("Failed to migrate scan {$scan->id}: {$e->getMessage()}");
                $errors++;
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->info('Migration Summary:');
        $this->table(
            ['Status', 'Count'],
            [
                ['Migrated', $migrated],
                ['Skipped', $skipped],
                ['Errors', $errors],
                ['Total', $scans->count()],
            ]
        );

        if ($isDryRun) {
            $this->newLine();
            $this->info('This was a dry run. Run without --dry-run to actually migrate files.');
        }

        return $errors > 0 ? self::FAILURE : self::SUCCESS;
    }
}
