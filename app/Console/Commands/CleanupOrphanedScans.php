<?php

namespace App\Console\Commands;

use App\Models\Scan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupOrphanedScans extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scans:cleanup-orphaned {--dry-run : Preview files to be deleted} {--force : Skip confirmation prompt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove orphaned scan files that have no database records';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('Running in DRY RUN mode - no files will be deleted');
        }

        // Get all scan IDs from database
        $validScanIds = Scan::pluck('id')->toArray();

        $this->info('Found '.count($validScanIds).' valid scans in database');

        // Check local storage
        $localOrphans = $this->findOrphans('local', $validScanIds);

        // Check public storage
        $publicOrphans = $this->findOrphans('public', $validScanIds);

        $totalOrphans = count($localOrphans) + count($publicOrphans);

        if ($totalOrphans === 0) {
            $this->info('No orphaned scan files found.');

            return self::SUCCESS;
        }

        $this->info("Found {$totalOrphans} orphaned scan directories");

        if (! $isDryRun && ! $this->option('force')) {
            $this->warn('This will permanently delete orphaned files!');
            if (! $this->confirm('Do you want to continue?')) {
                $this->info('Aborted.');

                return self::SUCCESS;
            }
        }

        $deleted = 0;
        $errors = 0;

        // Delete local orphans
        foreach ($localOrphans as $dir) {
            try {
                if (! $isDryRun) {
                    Storage::disk('local')->deleteDirectory($dir);
                }
                $this->line("  Deleted: local/{$dir}");
                $deleted++;
            } catch (\Exception $e) {
                $this->error("  Failed to delete local/{$dir}: {$e->getMessage()}");
                $errors++;
            }
        }

        // Delete public orphans
        foreach ($publicOrphans as $dir) {
            try {
                if (! $isDryRun) {
                    Storage::disk('public')->deleteDirectory($dir);
                }
                $this->line("  Deleted: public/{$dir}");
                $deleted++;
            } catch (\Exception $e) {
                $this->error("  Failed to delete public/{$dir}: {$e->getMessage()}");
                $errors++;
            }
        }

        $this->newLine();
        $this->info('Cleanup Summary:');
        $this->table(
            ['Status', 'Count'],
            [
                ['Deleted', $deleted],
                ['Errors', $errors],
                ['Total', $totalOrphans],
            ]
        );

        if ($isDryRun) {
            $this->newLine();
            $this->info('This was a dry run. Run without --dry-run to actually delete files.');
        }

        return $errors > 0 ? self::FAILURE : self::SUCCESS;
    }

    /**
     * Find orphaned scan directories in a disk
     */
    private function findOrphans(string $disk, array $validScanIds): array
    {
        $orphans = [];

        if (! Storage::disk($disk)->exists('scans')) {
            return $orphans;
        }

        $directories = Storage::disk($disk)->directories('scans');

        foreach ($directories as $dir) {
            $scanId = (int) basename($dir);

            if (! in_array($scanId, $validScanIds)) {
                $orphans[] = $dir;
            }
        }

        return $orphans;
    }
}
