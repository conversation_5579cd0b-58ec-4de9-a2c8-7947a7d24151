<?php

namespace App\Http\Middleware;

use App\Models\Scan;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionMiddleware
{
    /**
     * Handle an incoming request and check subscription limits (T146-T149)
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $scanType = 'photo'): Response
    {
        $user = $request->user();

        // Only enforce limits for photo scans (barcode scans are unlimited per FR-018)
        if ($scanType !== 'photo') {
            return $next($request);
        }

        // Premium users bypass limits (T149)
        if ($this->hasPremiumAccess($user)) {
            return $next($request);
        }

        // Free users: check daily photo scan limit (T146, T147)
        $dailyLimit = 3;
        $scansToday = $this->getDailyPhotoScanCount($user);

        if ($scansToday >= $dailyLimit) {
            return response()->json([
                'message' => 'Daily limit reached. Upgrade to Premium for unlimited scans.',
                'upgrade_required' => true,
                'scans_used_today' => $scansToday,
                'daily_limit' => $dailyLimit,
            ], 403); // T148
        }

        return $next($request);
    }

    /**
     * Check if user has active Premium access
     */
    private function hasPremiumAccess($user): bool
    {
        // Check if subscription tier is premium
        if ($user->subscription_tier !== 'premium') {
            return false;
        }

        // Check if subscription is expired (if expiration date exists)
        if ($user->subscription_expires_at && Carbon::parse($user->subscription_expires_at)->isPast()) {
            return false;
        }

        // Check if trial is still active
        if ($user->trial_ends_at && Carbon::parse($user->trial_ends_at)->isFuture()) {
            return true;
        }

        // Premium subscription is active
        return true;
    }

    /**
     * Get count of photo scans today for the user
     * Resets at midnight user local time (T147)
     */
    private function getDailyPhotoScanCount($user): int
    {
        $startOfDay = Carbon::now()->startOfDay();

        return Scan::where('user_id', $user->id)
            ->where('type', 'photo')
            ->where('created_at', '>=', $startOfDay)
            ->count();
    }
}
