<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Attack;
use App\Services\SymptomReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EmergencyController extends Controller
{
    public function __construct(
        private SymptomReportService $symptomReportService
    ) {}

    /**
     * Get emergency support guidance.
     * T176: Returns emergency guidance JSON with prominent "SEEK MEDICAL CARE NOW" message
     * and action buttons (Call 911, Find ER, Call Doctor) per FR-058.
     */
    public function show(): JsonResponse
    {
        return response()->json([
            'message' => 'SEEK MEDICAL CARE NOW',
            'guidance' => 'If you are experiencing severe symptoms such as intense pain, vomiting, fever, or jaundice, you should seek immediate medical attention. Do not wait or try to manage severe symptoms on your own.',
            'actions' => [
                [
                    'type' => 'call_911',
                    'label' => 'Call 911',
                    'action' => 'tel:911',
                    'priority' => 'high',
                ],
                [
                    'type' => 'find_er',
                    'label' => 'Find Nearest ER',
                    'action' => 'maps',
                    'priority' => 'high',
                ],
                [
                    'type' => 'call_doctor',
                    'label' => 'Call My Doctor',
                    'action' => 'call_doctor',
                    'priority' => 'medium',
                ],
                [
                    'type' => 'emergency_contact',
                    'label' => 'Text Emergency Contact',
                    'action' => 'text_contact',
                    'priority' => 'medium',
                ],
            ],
            'disclaimer' => 'This app provides dietary guidance only. In case of severe symptoms, always seek professional medical care immediately.',
        ]);
    }

    /**
     * Generate symptom report PDF for an attack.
     * T181: Creates shareable PDF with attack symptoms and recent meals (last 8 hours).
     */
    public function generateReport(Request $request): JsonResponse
    {
        $request->validate([
            'attack_id' => 'required|integer|exists:attacks,id',
        ]);

        // Load attack
        $attack = Attack::findOrFail($request->attack_id);

        // Verify user owns this attack
        if ($attack->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Forbidden. You can only generate reports for your own attacks.',
            ], 403);
        }

        // Generate PDF report
        $reportData = $this->symptomReportService->generatePDF($attack);

        return response()->json($reportData);
    }
}
