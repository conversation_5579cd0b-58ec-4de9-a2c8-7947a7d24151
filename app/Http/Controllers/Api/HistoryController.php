<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\History\ScanHistoryRequest;
use App\Http\Resources\ScanResource;
use App\Models\Scan;
use Illuminate\Http\JsonResponse;

class HistoryController extends Controller
{
    /**
     * Get scan history with filters and pagination (T141-T144)
     */
    public function index(ScanHistoryRequest $request): JsonResponse
    {
        $user = auth()->user();

        // Build query for user's completed scans
        $query = Scan::where('user_id', $user->id)
            ->where('status', 'completed')
            ->orderBy('created_at', 'desc'); // Most recent first per T-requirement

        // Date range filter (T143)
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        // Safety score range filter (T143)
        if ($request->filled('safety_score_min')) {
            $query->where('adjusted_score', '>=', $request->safety_score_min);
        }

        if ($request->filled('safety_score_max')) {
            $query->where('adjusted_score', '<=', $request->safety_score_max);
        }

        // Meal type filter (T143)
        if ($request->filled('meal_type')) {
            $query->where('meal_type', $request->meal_type);
        }

        // Search by meal name (T143) - case-insensitive
        if ($request->filled('search')) {
            $query->whereRaw('LOWER(meal_name) LIKE ?', ['%'.strtolower($request->search).'%']);
        }

        // Cursor-based pagination for infinite scroll (T142, T144)
        $perPage = $request->input('per_page', 20);

        $scans = $query->cursorPaginate($perPage);

        return response()->json([
            'scans' => [
                'data' => ScanResource::collection($scans->items()),
                'next_cursor' => $scans->nextCursor()?->encode(),
                'prev_cursor' => $scans->previousCursor()?->encode(),
            ],
        ]);
    }

    /**
     * Get a specific scan from history by ID
     */
    public function show(Scan $scan): JsonResponse
    {
        // Ensure user owns this scan
        if ($scan->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        return response()->json([
            'scan' => new ScanResource($scan),
        ]);
    }
}
