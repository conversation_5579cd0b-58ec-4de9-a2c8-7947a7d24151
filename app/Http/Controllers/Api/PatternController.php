<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PatternSuggestionResource;
use App\Models\PatternSuggestion;
use App\Models\Trigger;
use App\Models\UserTrigger;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PatternController extends Controller
{
    /**
     * Get pending pattern suggestions for authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        $suggestions = PatternSuggestion::query()
            ->where('user_id', $user->id)
            ->where('status', 'pending')
            ->orderBy('confidence_score', 'desc')
            ->get();

        return response()->json([
            'data' => PatternSuggestionResource::collection($suggestions),
        ]);
    }

    /**
     * Confirm a pattern suggestion - promote to user trigger
     */
    public function confirm(Request $request, PatternSuggestion $suggestion): JsonResponse
    {
        // Ensure user owns this suggestion
        if ($suggestion->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Check if suggestion is already confirmed or rejected
        if ($suggestion->status !== 'pending') {
            $message = $suggestion->status === 'confirmed'
                ? 'Pattern already confirmed'
                : 'Pattern already rejected';

            return response()->json([
                'message' => $message,
            ], 422);
        }

        // Create or update UserTrigger
        $userTrigger = UserTrigger::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'trigger_name' => $suggestion->suspected_trigger_name,
            ],
            [
                'severity' => 'moderate', // Default severity for AI-detected triggers
                'identification_source' => 'pattern_detected',
                'confidence_score' => $suggestion->confidence_score,
                'status' => 'active',
                'confirmed_at' => now(),
                'denied_at' => null,
                'notes' => 'Detected by pattern analysis',
            ]
        );

        // Mark suggestion as confirmed
        $suggestion->update(['status' => 'confirmed']);

        return response()->json([
            'message' => 'Pattern confirmed and added to your triggers',
            'trigger' => [
                'id' => $userTrigger->id,
                'trigger_name' => $userTrigger->trigger_name,
                'severity' => $userTrigger->severity,
                'confidence_score' => $userTrigger->confidence_score,
            ],
        ]);
    }

    /**
     * Reject a pattern suggestion
     */
    public function reject(Request $request, PatternSuggestion $suggestion): JsonResponse
    {
        // Ensure user owns this suggestion
        if ($suggestion->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Check if suggestion is already confirmed or rejected
        if ($suggestion->status !== 'pending') {
            $message = $suggestion->status === 'confirmed'
                ? 'Pattern already confirmed'
                : 'Pattern already rejected';

            return response()->json([
                'message' => $message,
            ], 422);
        }

        // Mark suggestion as rejected
        $suggestion->update(['status' => 'rejected']);

        return response()->json([
            'message' => 'Pattern suggestion rejected',
        ]);
    }
}
