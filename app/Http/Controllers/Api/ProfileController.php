<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Profile\UpdateProfileRequest;
use App\Http\Resources\ProfileResource;
use App\Models\Profile;
use Illuminate\Http\JsonResponse;

class ProfileController extends Controller
{
    /**
     * Display the authenticated user's profile.
     */
    public function show(): JsonResponse
    {
        $profile = auth()->user()->profile;

        if (! $profile) {
            return response()->json([
                'message' => 'Profile not found',
            ], 404);
        }

        return response()->json([
            'profile' => new ProfileResource($profile),
        ]);
    }

    /**
     * Update the authenticated user's profile.
     */
    public function update(UpdateProfileRequest $request): JsonResponse
    {
        $user = auth()->user();
        $profile = $user->profile;

        if (! $profile) {
            // Create profile if it doesn't exist
            $profile = Profile::create([
                'user_id' => $user->id,
                ...$request->validated(),
            ]);
        } else {
            // Update existing profile
            $profile->update($request->validated());
        }

        // Calculate completion percentage (but don't override onboarding_completed if explicitly set)
        $this->updateCompletionPercentage($profile, $request->has('onboarding_completed'));

        return response()->json([
            'message' => 'Profile updated successfully',
            'profile' => new ProfileResource($profile->fresh()),
        ]);
    }

    /**
     * Calculate and update the profile completion percentage.
     */
    private function updateCompletionPercentage(Profile $profile, bool $skipOnboardingFlag = false): void
    {
        $fields = [
            'age',
            'sex',
            'weight_kg',
            'height_cm',
            'activity_level',
            'health_conditions',
            'dietary_preferences',
            'allergens',
        ];

        $completed = 0;
        $total = count($fields);

        foreach ($fields as $field) {
            $value = $profile->$field;
            if ($value !== null && $value !== '' && $value !== []) {
                $completed++;
            }
        }

        $percentage = (int) round(($completed / $total) * 100);

        // Only update onboarding_completed if it wasn't explicitly set in the request
        $updateData = ['completion_percentage' => $percentage];

        if (! $skipOnboardingFlag) {
            $updateData['onboarding_completed'] = $percentage === 100;
        }

        $profile->update($updateData);
    }
}
