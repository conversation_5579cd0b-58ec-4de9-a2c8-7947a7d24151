<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scan;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    /**
     * Get current scan usage and limits for the authenticated user
     */
    public function scanCount(Request $request): JsonResponse
    {
        $user = $request->user();

        // Get photo scans today
        $startOfDay = Carbon::now()->startOfDay();

        $scansToday = Scan::where('user_id', $user->id)
            ->where('type', 'photo')
            ->where('created_at', '>=', $startOfDay)
            ->count();

        // Determine if user has premium access
        $hasPremium = $this->hasPremiumAccess($user);

        return response()->json([
            'scans_used_today' => $scansToday,
            'daily_limit' => $hasPremium ? null : 3, // null = unlimited
            'scans_remaining' => $hasPremium ? null : max(0, 3 - $scansToday),
            'subscription_tier' => $user->subscription_tier,
            'has_premium' => $hasPremium,
        ]);
    }

    /**
     * Check if user has active Premium access
     */
    private function hasPremiumAccess($user): bool
    {
        // Check if subscription tier is premium
        if ($user->subscription_tier !== 'premium') {
            return false;
        }

        // Check if subscription is expired
        if ($user->subscription_expires_at && Carbon::parse($user->subscription_expires_at)->isPast()) {
            return false;
        }

        // Check if trial is still active
        if ($user->trial_ends_at && Carbon::parse($user->trial_ends_at)->isFuture()) {
            return true;
        }

        return true;
    }
}
