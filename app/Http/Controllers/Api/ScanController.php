<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Scan\StoreScanBarcodeRequest;
use App\Http\Requests\Scan\StoreScanPhotoRequest;
use App\Http\Resources\ScanResource;
use App\Jobs\AnalyzeScanPhotoJob;
use App\Models\Scan;
use App\Services\OpenFoodFactsService;
use App\Services\PersonalizationEngine;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ScanController extends Controller
{
    /**
     * Store a new photo scan and dispatch analysis job
     */
    public function storePhoto(StoreScanPhotoRequest $request): JsonResponse
    {
        $user = auth()->user();

        // Create scan record first to get the ID
        $scan = Scan::create([
            'user_id' => $user->id,
            'type' => 'photo',
            'status' => 'pending',
            'meal_type' => $request->meal_type,
        ]);

        // Store the uploaded photo with scan ID in path (public disk for URL access)
        $photo = $request->file('photo');
        $path = $photo->store("scans/{$scan->id}", 'public');

        // Update scan with image path (relative path, not absolute)
        $scan->update([
            'image_path' => $path,
            'status' => 'analyzing',
        ]);

        // Dispatch analysis job
        AnalyzeScanPhotoJob::dispatch($scan);

        return response()->json([
            'scan' => new ScanResource($scan),
        ], 201);
    }

    /**
     * Store a new barcode scan with synchronous analysis (T134-T138)
     */
    public function storeBarcode(StoreScanBarcodeRequest $request): JsonResponse
    {
        $user = auth()->user();
        $barcode = $request->barcode;

        try {
            // Look up product in Open Food Facts API (with 90-day caching)
            $openFoodFactsService = app(OpenFoodFactsService::class);
            $product = $openFoodFactsService->lookupBarcode($barcode);

            // Product not found (T136)
            if (! $product) {
                return response()->json([
                    'message' => 'Product not in database. Try photo scan instead.',
                ], 404);
            }

            // Create scan record
            $scan = Scan::create([
                'user_id' => $user->id,
                'type' => 'barcode',
                'status' => 'analyzing',
                'meal_type' => $request->meal_type,
                'meal_name' => $product['name'],
                'detected_ingredients' => [
                    'ingredients_text' => $product['ingredients'],
                    'allergens' => $product['allergens'],
                    'fat_100g' => $product['fat_100g'],
                    'saturated_fat_100g' => $product['saturated_fat_100g'],
                ],
                'barcode' => $barcode,
            ]);

            // Synchronous analysis with PersonalizationEngine (T137)
            $personalizationEngine = app(PersonalizationEngine::class);

            // Calculate safety score based on product ingredients
            $analysisResult = $personalizationEngine->analyzeProduct($user, $product);

            // Update scan with analysis results (T138)
            $scan->update([
                'base_score' => 100, // Base score before personalization
                'adjusted_score' => $analysisResult['safety_score'],
                'personalized_reasoning' => $analysisResult['personalized_reasoning'],
                'trigger_warnings' => $analysisResult['trigger_warnings'] ?? [],
                'status' => 'completed',
                'analyzed_at' => now(),
            ]);

            // Return immediately (synchronous, < 2 seconds per FR-013)
            return response()->json([
                'scan' => new ScanResource($scan),
            ], 201);

        } catch (\Exception $e) {
            // API failure handling (T136 fallback)
            \Log::error('Barcode scan failed', [
                'barcode' => $barcode,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Barcode lookup service temporarily unavailable. Please try again or use photo scan.',
            ], 503);
        }
    }

    /**
     * Get scan status (for polling)
     */
    public function showStatus(Request $request, Scan $scan): JsonResponse
    {
        // Ensure user owns this scan
        if ($scan->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        return response()->json([
            'scan' => new ScanResource($scan),
        ]);
    }

    /**
     * Get complete scan result
     */
    public function showResult(Request $request, Scan $scan): JsonResponse
    {
        // Ensure user owns this scan
        if ($scan->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        // Only return result if analysis is complete
        if ($scan->status !== 'completed' && $scan->status !== 'failed') {
            return response()->json([
                'message' => 'Scan is still being analyzed',
                'status' => $scan->status,
            ], 202); // 202 Accepted - still processing
        }

        return response()->json([
            'scan' => new ScanResource($scan),
        ]);
    }

    /**
     * Update scan consumption outcome - T110
     */
    public function updateOutcome(Request $request, Scan $scan): JsonResponse
    {
        // Ensure user owns this scan
        if ($scan->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        $request->validate([
            'outcome' => 'required|in:ate,avoided',
        ]);

        $scan->update([
            'outcome' => $request->outcome,
        ]);

        return response()->json([
            'scan' => new ScanResource($scan),
            'message' => 'Consumption status updated successfully',
        ]);
    }

    /**
     * Get recent scans for symptom report - T114
     * Returns scans from the last N hours (for attack logging)
     */
    public function getRecent(Request $request): JsonResponse
    {
        $user = auth()->user();

        $request->validate([
            'since' => 'required|date',
        ]);

        $scans = Scan::where('user_id', $user->id)
            ->where('created_at', '>=', $request->since)
            ->whereIn('status', ['completed', 'analyzing'])
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        return response()->json([
            'scans' => ScanResource::collection($scans),
        ]);
    }
}
