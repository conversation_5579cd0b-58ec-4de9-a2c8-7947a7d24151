<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Attack\StoreAttackRequest;
use App\Http\Resources\AttackResource;
use App\Jobs\DetectPatternsJob;
use App\Models\Attack;
use App\Services\AttackCorrelationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AttackController extends Controller
{
    public function __construct(
        private AttackCorrelationService $attackCorrelationService
    ) {}

    /**
     * Get paginated list of user's attacks
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        $perPage = min($request->input('per_page', 15), 100); // Max 100 per page

        $attacks = Attack::query()
            ->where('user_id', $user->id)
            ->orderBy('onset_at', 'desc')
            ->paginate($perPage);

        // Return Laravel standard paginated resource collection
        return AttackResource::collection($attacks)->response();
    }

    /**
     * Store a new attack log
     */
    public function store(StoreAttackRequest $request): JsonResponse
    {
        $user = auth()->user();

        $attack = Attack::create([
            'user_id' => $user->id,
            'onset_at' => $request->onset_at,
            'pain_intensity' => $request->pain_intensity,
            'duration_minutes' => $request->duration_minutes,
            'pain_location' => $request->pain_location,
            'symptoms' => $request->symptoms,
            'medical_care_type' => $request->medical_care_type,
            'diagnosis_received' => $request->diagnosis_received,
            'treatment_received' => $request->treatment_received,
            'notes' => $request->notes,
        ]);

        // T186: Correlate scans with attack (3-8 hours before)
        $this->attackCorrelationService->correlateScans($attack);

        // T187: Dispatch pattern detection job to analyze correlations
        DetectPatternsJob::dispatch($attack);

        return response()->json([
            'attack' => new AttackResource($attack),
        ], 201);
    }

    /**
     * Get a specific attack
     */
    public function show(Request $request, Attack $attack): JsonResponse
    {
        // Ensure user owns this attack
        if ($attack->user_id !== auth()->id()) {
            return response()->json([
                'message' => 'Unauthorized',
            ], 403);
        }

        return response()->json([
            'attack' => new AttackResource($attack),
        ]);
    }
}
