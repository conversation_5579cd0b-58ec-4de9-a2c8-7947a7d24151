<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TriggerResource;
use App\Http\Resources\UserTriggerResource;
use App\Models\Trigger;
use App\Models\UserTrigger;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TriggerController extends Controller
{
    /**
     * List all available triggers (master list for reference/autocomplete).
     */
    public function index(): AnonymousResourceCollection
    {
        $triggers = Trigger::orderBy('category')
            ->orderBy('name')
            ->get();

        return TriggerResource::collection($triggers);
    }

    /**
     * List the authenticated user's personal triggers.
     */
    public function userTriggers(): AnonymousResourceCollection
    {
        $user = auth()->user();

        $triggers = $user->triggers()
            ->where('status', 'active')
            ->whereNot<PERSON>ull('confirmed_at')
            ->whereNull('denied_at')
            ->orderBy('severity', 'desc')
            ->orderBy('confidence_score', 'desc')
            ->orderBy('trigger_name')
            ->get();

        return UserTriggerResource::collection($triggers);
    }

    /**
     * Confirm a trigger for the authenticated user.
     *
     * Route parameter {trigger} is the Trigger model ID from master list.
     */
    public function confirm(Trigger $trigger, Request $request): JsonResponse
    {
        $user = auth()->user();

        $triggerName = $trigger->name;
        $severity = $request->input('severity', $trigger->severity);
        $notes = $request->input('notes');

        // Check if trigger already exists for this user
        $userTrigger = UserTrigger::where('user_id', $user->id)
            ->where('trigger_name', $triggerName)
            ->first();

        if ($userTrigger) {
            // Update existing trigger to confirmed status
            $userTrigger->update([
                'status' => 'active',
                'confirmed_at' => now(),
                'denied_at' => null,
                'severity' => $severity,
                'notes' => $notes ?? $userTrigger->notes,
            ]);
        } else {
            // Create new confirmed trigger
            $userTrigger = UserTrigger::create([
                'user_id' => $user->id,
                'trigger_name' => $triggerName,
                'severity' => $severity,
                'identification_source' => $request->input('identification_source', 'user_input'),
                'confidence_score' => 100.00,
                'status' => 'active',
                'confirmed_at' => now(),
                'notes' => $notes,
            ]);
        }

        return response()->json([
            'message' => 'Trigger confirmed successfully',
            'trigger' => [
                'id' => $trigger->id,
                'name' => $trigger->name,
                'pivot' => [
                    'severity' => $userTrigger->severity,
                    'confirmed_at' => $userTrigger->confirmed_at,
                    'confidence_score' => $userTrigger->confidence_score,
                ],
            ],
        ]);
    }

    /**
     * Deny/reject a trigger for the authenticated user.
     *
     * Route parameter {trigger} is the Trigger model ID from master list.
     */
    public function deny(Trigger $trigger, Request $request): JsonResponse
    {
        $user = auth()->user();

        $triggerName = $trigger->name;

        // Check if trigger exists for this user
        $userTrigger = UserTrigger::where('user_id', $user->id)
            ->where('trigger_name', $triggerName)
            ->first();

        if ($userTrigger) {
            // Update existing trigger to denied status
            $userTrigger->update([
                'status' => 'rejected',
                'denied_at' => now(),
                'confirmed_at' => null,
                'rejection_count' => $userTrigger->rejection_count + 1,
            ]);
        } else {
            // Create new denied trigger record
            $userTrigger = UserTrigger::create([
                'user_id' => $user->id,
                'trigger_name' => $triggerName,
                'severity' => 'low',
                'identification_source' => $request->input('identification_source', 'user_input'),
                'confidence_score' => 0.00,
                'status' => 'rejected',
                'denied_at' => now(),
                'rejection_count' => 1,
            ]);
        }

        return response()->json([
            'message' => 'Trigger denied successfully',
            'trigger' => [
                'id' => $trigger->id,
                'name' => $trigger->name,
                'pivot' => [
                    'severity' => $userTrigger->severity,
                    'denied_at' => $userTrigger->denied_at,
                    'rejection_count' => $userTrigger->rejection_count,
                ],
            ],
        ]);
    }

    /**
     * Remove a trigger association from the authenticated user.
     */
    public function remove(int $id): JsonResponse
    {
        $user = auth()->user();

        $userTrigger = UserTrigger::where('user_id', $user->id)
            ->where('id', $id)
            ->firstOrFail();

        $userTrigger->delete();

        return response()->json([
            'message' => 'Trigger removed successfully',
        ]);
    }
}
