<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttackResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'onset_at' => $this->onset_at?->toISOString(),
            'pain_intensity' => $this->pain_intensity,
            'duration_minutes' => $this->duration_minutes,
            'pain_location' => $this->pain_location,
            'symptoms' => $this->symptoms,
            'medical_care_type' => $this->medical_care_type,
            'diagnosis_received' => $this->diagnosis_received,
            'treatment_received' => $this->treatment_received,
            'correlated_scans_analyzed' => $this->correlated_scans_analyzed,
            'suspected_trigger_id' => $this->suspected_trigger_id,
            'correlation_confidence' => $this->correlation_confidence,
            'notes' => $this->notes,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
