<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserTriggerResource extends JsonResource
{
    /**
     * Transform the UserTrigger resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'trigger_name' => $this->trigger_name,
            'severity' => $this->severity,
            'identification_source' => $this->identification_source,
            'confidence_score' => (float) $this->confidence_score,
            'attack_correlation_count' => $this->attack_correlation_count,
            'total_exposures' => $this->total_exposures,
            'last_attack_date' => $this->last_attack_date?->toISOString(),
            'notes' => $this->notes,
            'status' => $this->status,
            'confirmed_at' => $this->confirmed_at?->toISOString(),
            'denied_at' => $this->denied_at?->toISOString(),
            'rejection_count' => $this->rejection_count,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
