<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ScanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'is_food' => $this->is_food ?? true,
            'status' => $this->status,
            'image_url' => $this->image_path ? \Storage::disk('public')->url($this->image_path) : null,
            'meal_name' => $this->meal_name,
            'meal_type' => $this->meal_type,
            'detected_ingredients' => $this->detected_ingredients,
            'adjusted_score' => $this->adjusted_score,
            'safety_score' => $this->adjusted_score, // Alias for mobile app compatibility
            'base_score' => $this->base_score,
            'confidence_score' => $this->confidence_score,
            'personalized_reasoning' => $this->personalized_reasoning,
            'trigger_warnings' => $this->trigger_warnings ?? [],
            'outcome' => $this->outcome,
            'reaction_severity' => $this->reaction_severity,
            'analyzed_at' => $this->analyzed_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
