<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'age' => $this->age,
            'sex' => $this->sex,
            'weight_kg' => $this->weight_kg,
            'height_cm' => $this->height_cm,
            'activity_level' => $this->activity_level,
            'health_conditions' => $this->health_conditions,
            'dietary_preferences' => $this->dietary_preferences,
            'allergens' => $this->allergens,
            'completion_percentage' => $this->completion_percentage,
            'onboarding_completed' => $this->onboarding_completed,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
