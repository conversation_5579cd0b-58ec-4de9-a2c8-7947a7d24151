<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TriggerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'category' => $this->category,
            'severity' => $this->severity,
            'description' => $this->description,
            // Include pivot data when loaded through a user relationship
            'confidence_score' => $this->whenPivotLoaded('user_triggers', fn () => $this->pivot->confidence_score),
            'confirmed_at' => $this->whenPivotLoaded('user_triggers', fn () => $this->pivot->confirmed_at?->toISOString()),
            'denied_at' => $this->whenPivotLoaded('user_triggers', fn () => $this->pivot->denied_at?->toISOString()),
            'last_occurred_at' => $this->whenPivotLoaded('user_triggers', fn () => $this->pivot->last_occurred_at?->toISOString()),
            'occurrences_count' => $this->whenPivotLoaded('user_triggers', fn () => $this->pivot->occurrences_count),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
