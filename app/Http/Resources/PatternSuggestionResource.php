<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PatternSuggestionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'suspected_trigger_name' => $this->suspected_trigger_name,
            'confidence_score' => $this->confidence_score,
            'correlation_count' => $this->correlation_count,
            'status' => $this->status,
            'evidence' => $this->evidence,
            'evidence_summary' => $this->generateEvidenceSummary(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }

    /**
     * Generate human-readable evidence summary with attack history.
     * T188: Reference attack history - "This caused an attack for you on [date]"
     * T190: Present suggestions with evidence - "You ate X, 6 hours later had attack. Pattern occurred 3 times."
     */
    private function generateEvidenceSummary(): string
    {
        $count = $this->correlation_count;
        $ingredient = $this->suspected_trigger_name;
        $avgGap = $this->evidence['avg_time_gap'] ?? 0;
        $correlations = $this->evidence['correlations'] ?? [];

        if ($count === 1 && ! empty($correlations)) {
            // Single occurrence - show specific date
            $firstCorrelation = $correlations[0];
            $attackDate = $firstCorrelation['attack_date'] ?? null;

            if ($attackDate) {
                $formattedDate = \Carbon\Carbon::parse($attackDate)->format('M j, Y');

                return "You ate {$ingredient}, {$avgGap} hours later had an attack on {$formattedDate}.";
            }

            return "You ate {$ingredient}, {$avgGap} hours later had an attack.";
        }

        // Multiple occurrences - show pattern with most recent date
        if (! empty($correlations)) {
            $mostRecentAttackDate = collect($correlations)
                ->pluck('attack_date')
                ->filter()
                ->sort()
                ->last();

            if ($mostRecentAttackDate) {
                $formattedDate = \Carbon\Carbon::parse($mostRecentAttackDate)->format('M j, Y');

                return "You ate {$ingredient}, {$avgGap} hours later had an attack. Pattern occurred {$count} times (most recent: {$formattedDate}).";
            }
        }

        return "You ate {$ingredient}, {$avgGap} hours later had an attack. Pattern occurred {$count} times.";
    }
}
