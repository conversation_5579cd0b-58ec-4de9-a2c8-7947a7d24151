<?php

namespace App\Http\Requests\Scan;

use Illuminate\Foundation\Http\FormRequest;

class StoreScanPhotoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'photo' => ['required', 'image', 'mimes:jpg,jpeg,png', 'max:10240'], // Max 10MB
            'meal_type' => ['required', 'string', 'in:breakfast,lunch,dinner,snack'],
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'photo.required' => 'Please upload a photo of your meal',
            'photo.image' => 'The file must be an image',
            'photo.mimes' => 'The photo must be a JPG, JPEG, or PNG file',
            'photo.max' => 'The photo must not be larger than 10MB',
            'meal_type.required' => 'Please select when you ate this meal',
            'meal_type.in' => 'Meal type must be breakfast, lunch, dinner, or snack',
        ];
    }
}
