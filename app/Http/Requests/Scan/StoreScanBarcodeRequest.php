<?php

namespace App\Http\Requests\Scan;

use Illuminate\Foundation\Http\FormRequest;

class StoreScanBarcodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by auth:sanctum middleware
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'barcode' => [
                'required',
                'string',
                'min:8',
                'max:18', // EAN-13, UPC-A, etc.
                'regex:/^[0-9]+$/', // Only digits
            ],
            'meal_type' => [
                'required',
                'in:breakfast,lunch,dinner,snack',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'barcode.required' => 'A barcode is required to scan a product.',
            'barcode.regex' => 'Barcode must contain only numbers.',
            'barcode.min' => 'Barcode must be at least 8 digits.',
            'barcode.max' => 'Barcode must not exceed 18 digits.',
            'meal_type.required' => 'Please select when you plan to eat this.',
            'meal_type.in' => 'Meal type must be breakfast, lunch, dinner, or snack.',
        ];
    }
}
