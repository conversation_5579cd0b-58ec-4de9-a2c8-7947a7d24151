<?php

namespace App\Http\Requests\Profile;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'age' => ['nullable', 'integer', 'min:1', 'max:120'],
            'sex' => ['nullable', 'string', 'in:male,female,other'],
            'weight_kg' => ['nullable', 'numeric', 'min:1', 'max:500'],
            'height_cm' => ['nullable', 'numeric', 'min:1', 'max:300'],
            'activity_level' => ['nullable', 'string', 'in:sedentary,light,moderate,active,very_active'],
            'health_conditions' => ['nullable', 'array'],
            'health_conditions.*' => ['string', 'max:255'],
            'dietary_preferences' => ['nullable', 'array'],
            'dietary_preferences.*' => ['string', 'max:255'],
            'allergens' => ['nullable', 'array'],
            'allergens.*' => ['string', 'max:255'],
            'onboarding_completed' => ['nullable', 'boolean'],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'age.integer' => 'Age must be a valid number.',
            'age.min' => 'Age must be at least 1.',
            'age.max' => 'Age cannot exceed 120.',
            'sex.in' => 'Please select a valid sex option.',
            'weight_kg.numeric' => 'Weight must be a valid number.',
            'weight_kg.min' => 'Weight must be at least 1 kg.',
            'weight_kg.max' => 'Weight cannot exceed 500 kg.',
            'height_cm.numeric' => 'Height must be a valid number.',
            'height_cm.min' => 'Height must be at least 1 cm.',
            'height_cm.max' => 'Height cannot exceed 300 cm.',
            'activity_level.in' => 'Please select a valid activity level.',
            'health_conditions.array' => 'Health conditions must be a list.',
            'dietary_preferences.array' => 'Dietary preferences must be a list.',
            'allergens.array' => 'Allergens must be a list.',
        ];
    }
}
