<?php

namespace App\Http\Requests\Attack;

use Illuminate\Foundation\Http\FormRequest;

class StoreAttackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'onset_at' => ['required', 'date', 'before_or_equal:now'],
            'pain_intensity' => ['required', 'integer', 'min:1', 'max:10'],
            'duration_minutes' => ['nullable', 'integer', 'min:1', 'max:10080'], // Max 1 week
            'pain_location' => ['nullable', 'array'],
            'pain_location.x' => ['required_with:pain_location', 'numeric'],
            'pain_location.y' => ['required_with:pain_location', 'numeric'],
            'pain_location.region' => ['required_with:pain_location', 'string', 'max:255'],
            'symptoms' => ['nullable', 'array'],
            'symptoms.*' => ['string', 'max:255'],
            'medical_care_type' => ['nullable', 'string', 'in:none,home_treatment,doctor_call,primary_care,urgent_care,emergency_room,hospital_admission,hospitalization'],
            'diagnosis_received' => ['nullable', 'string', 'max:1000'],
            'treatment_received' => ['nullable', 'string', 'max:1000'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'onset_at.required' => 'Please specify when the attack started.',
            'onset_at.before_or_equal' => 'Attack onset time cannot be in the future.',
            'pain_intensity.required' => 'Please rate your pain intensity.',
            'pain_intensity.min' => 'Pain intensity must be at least 1.',
            'pain_intensity.max' => 'Pain intensity cannot exceed 10.',
            'duration_minutes.max' => 'Duration cannot exceed 1 week (10080 minutes).',
            'medical_care_type.in' => 'Invalid medical care type. Must be one of: none, home_treatment, doctor_call, urgent_care, emergency_room, hospital_admission.',
        ];
    }
}
