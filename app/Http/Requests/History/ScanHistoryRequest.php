<?php

namespace App\Http\Requests\History;

use Illuminate\Foundation\Http\FormRequest;

class ScanHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by auth:sanctum middleware
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'safety_score_min' => 'nullable|integer|min:0|max:100',
            'safety_score_max' => 'nullable|integer|min:0|max:100',
            'meal_type' => 'nullable|in:breakfast,lunch,dinner,snack',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
            'cursor' => 'nullable|string',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Only validate max >= min if both are present
            if ($this->filled('safety_score_min') && $this->filled('safety_score_max')) {
                if ($this->safety_score_max < $this->safety_score_min) {
                    $validator->errors()->add('safety_score_max', 'Maximum safety score must be greater than or equal to minimum.');
                }
            }
        });
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'date_to.after_or_equal' => 'End date must be after or equal to start date.',
            'safety_score_max.gte' => 'Maximum safety score must be greater than or equal to minimum.',
            'meal_type.in' => 'Meal type must be breakfast, lunch, dinner, or snack.',
            'per_page.max' => 'Cannot retrieve more than 100 scans at a time.',
        ];
    }
}
