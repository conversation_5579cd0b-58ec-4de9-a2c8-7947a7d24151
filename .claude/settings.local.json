{"permissions": {"allow": ["<PERSON><PERSON>(curl -I http://localhost/api/profile)", "Bash(./vendor/bin/sail artisan route:list --path=profile)", "<PERSON><PERSON>(curl -s -o /dev/null -w \"%{http_code}\" http://localhost:80/api/user)", "Bash(./vendor/bin/sail artisan route:list --path=profile --json)", "Bash(vendor/bin/pint app/Http/Controllers/Api/ProfileController.php routes/api.php)", "Bash(.specify/scripts/bash/check-prerequisites.sh:*)", "Bash(if [ -d \"/Users/<USER>/apps/GallDiet/specs/001-galldiet-mvp-personalized/checklists\" ])", "Bash(then echo \"Checklists directory exists\")", "Bash(else echo \"No checklists directory found\")", "Bash(fi)", "Read(//Users/<USER>/Downloads/**)", "mcp__laravel-boost__database-schema", "mcp__laravel-boost__get-config", "Bash(./vendor/bin/sail artisan migrate:status:*)", "Bash(./vendor/bin/sail artisan test:*)", "Bash(./vendor/bin/sail artisan make:resource:*)", "Bash(vendor/bin/pint:*)", "mcp__laravel-boost__tinker", "mcp__laravel-boost__database-connections", "Bash(./vendor/bin/sail artisan queue:work:*)", "mcp__laravel-boost__read-log-entries", "Bash(./vendor/bin/sail artisan tinker --execute=\"echo implode('', '', Schema::getColumnListing(''scans''));\")", "Bash(./vendor/bin/sail artisan route:list:*)", "Bash(./vendor/bin/sail artisan tinker:*)", "Bash(npm install:*)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "Bash(git rev-parse:*)", "Bash(./vendor/bin/sail artisan make:controller:*)", "<PERSON><PERSON>(./vendor/bin/sail composer:*)"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["laravel-boost"]}