import { useState, useEffect } from 'react';
import attackService from '@/services/attack-service';

export interface AttackFreeDaysData {
  days: number;
  lastAttackDate: string | null;
  loading: boolean;
  error: string | null;
  isMilestone: boolean;
  milestoneDay: number | null;
}

/**
 * Hook to track attack-free days (T205)
 *
 * Calculates the number of days since the user's last gallstone attack.
 * Identifies milestone days (7, 14, 30, 60, 90, 180, 365) for celebrations.
 */
export function useAttackFreeDays(): AttackFreeDaysData {
  const [data, setData] = useState<AttackFreeDaysData>({
    days: 0,
    lastAttackDate: null,
    loading: true,
    error: null,
    isMilestone: false,
    milestoneDay: null,
  });

  const MILESTONE_DAYS = [7, 14, 30, 60, 90, 180, 365];

  useEffect(() => {
    loadAttackFreeDays();
  }, []);

  const loadAttackFreeDays = async () => {
    try {
      console.log('[useAttackFreeDays] Starting to load attack-free days...');
      setData((prev) => ({ ...prev, loading: true, error: null }));

      // Fetch recent attacks (page 1, 1 per page to get the most recent)
      console.log('[useAttackFreeDays] Fetching attacks from API...');
      const response = await attackService.getAttacks(1, 1);
      console.log('[useAttackFreeDays] API Response:', response);

      // Handle empty response or no attacks
      if (!response || !Array.isArray(response) || response.length === 0) {
        // No attacks recorded - user is attack-free since joining!
        // This is NOT an error - just means user has never had an attack
        console.log('[useAttackFreeDays] No attacks found - hiding counter');
        setData({
          days: 0,
          lastAttackDate: null,
          loading: false,
          error: null,
          isMilestone: false,
          milestoneDay: null,
        });
        return;
      }

      const lastAttack = response[0];

      // Validate attack data
      if (!lastAttack || !lastAttack.onset_at) {
        console.warn('Invalid attack data received:', lastAttack);
        setData({
          days: 0,
          lastAttackDate: null,
          loading: false,
          error: null,
          isMilestone: false,
          milestoneDay: null,
        });
        return;
      }

      const lastAttackDate = new Date(lastAttack.onset_at);
      const today = new Date();

      // Calculate days since last attack
      const diffTime = Math.abs(today.getTime() - lastAttackDate.getTime());
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      // Check if current day is a milestone
      const isMilestone = MILESTONE_DAYS.includes(diffDays);
      const milestoneDay = isMilestone ? diffDays : null;

      console.log('[useAttackFreeDays] ✅ Calculated:', {
        days: diffDays,
        isMilestone,
        milestoneDay,
        lastAttackDate: lastAttack.onset_at
      });

      setData({
        days: diffDays,
        lastAttackDate: lastAttack.onset_at,
        loading: false,
        error: null,
        isMilestone,
        milestoneDay,
      });
    } catch (error: any) {
      console.error('[useAttackFreeDays] ❌ Error loading attack-free days:', error);
      console.error('[useAttackFreeDays] Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status
      });
      // Don't show error to user - just hide the attack-free counter
      // This is a non-critical feature
      setData({
        days: 0,
        lastAttackDate: null,
        loading: false,
        error: null, // Set to null to hide error from user
        isMilestone: false,
        milestoneDay: null,
      });
    }
  };

  return data;
}

export default useAttackFreeDays;
