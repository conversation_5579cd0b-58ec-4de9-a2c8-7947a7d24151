import React, { createContext, useContext, useState, ReactNode } from 'react';

interface Trigger {
  name: string;
  severity: 'low' | 'moderate' | 'high';
}

interface OnboardingData {
  // Profile basic
  age: string;
  sex: 'male' | 'female' | 'other' | null;
  hasGallbladder: boolean | null;

  // Triggers
  triggers: Trigger[];

  // Allergens
  allergens: string[];
}

interface OnboardingContextType {
  data: OnboardingData;
  updateProfileBasic: (age: string, sex: 'male' | 'female' | 'other' | null, hasGallbladder: boolean | null) => void;
  updateTriggers: (triggers: Trigger[]) => void;
  updateAllergens: (allergens: string[]) => void;
  resetOnboarding: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

const initialData: OnboardingData = {
  age: '',
  sex: null,
  hasGallbladder: null,
  triggers: [],
  allergens: [],
};

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const [data, setData] = useState<OnboardingData>(initialData);

  const updateProfileBasic = (age: string, sex: 'male' | 'female' | 'other' | null, hasGallbladder: boolean | null) => {
    setData((prev) => ({ ...prev, age, sex, hasGallbladder }));
  };

  const updateTriggers = (triggers: Trigger[]) => {
    setData((prev) => ({ ...prev, triggers }));
  };

  const updateAllergens = (allergens: string[]) => {
    setData((prev) => ({ ...prev, allergens }));
  };

  const resetOnboarding = () => {
    setData(initialData);
  };

  return (
    <OnboardingContext.Provider
      value={{
        data,
        updateProfileBasic,
        updateTriggers,
        updateAllergens,
        resetOnboarding,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
