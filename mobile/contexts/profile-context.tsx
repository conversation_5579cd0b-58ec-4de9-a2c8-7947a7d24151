import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from './auth-context';

export interface UserProfile {
  id: number;
  user_id: number;
  age: number | null;
  sex: 'male' | 'female' | 'other' | null;
  weight_kg: number | null;
  height_cm: number | null;
  activity_level: string | null;
  health_conditions: string[] | null;
  dietary_preferences: string[] | null;
  allergens: string[] | null;
  completion_percentage: number;
  onboarding_completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProfileContextType {
  profile: UserProfile | null;
  isLoading: boolean;
  hasInitialized: boolean;
  refreshProfile: () => Promise<void>;
  updateProfile: (data: Partial<UserProfile>) => Promise<void>;
  clearProfile: () => Promise<void>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

const PROFILE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Helper to get user-specific cache keys
const getProfileKey = (userId: number) => `@galldiet_user_profile_${userId}`;
const getProfileTimestampKey = (userId: number) => `@galldiet_profile_timestamp_${userId}`;

interface ProfileProviderProps {
  children: ReactNode;
}

export function ProfileProvider({ children }: ProfileProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);  // Start as true to wait for initial load
  const [hasInitialized, setHasInitialized] = useState(false);

  // Load cached profile when user logs in
  useEffect(() => {
    console.log('[ProfileContext] Auth state changed:', { isAuthenticated, userId: user?.id });
    if (isAuthenticated && user) {
      console.log('[ProfileContext] User authenticated, loading cached profile');
      setHasInitialized(false);  // Mark as not initialized
      setIsLoading(true);  // Set loading immediately to prevent navigation flash
      loadCachedProfile();
    } else {
      console.log('[ProfileContext] User not authenticated, clearing profile');
      setProfile(null);
      setHasInitialized(false);  // Keep as false to prevent flash on next login
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id]);

  const loadCachedProfile = async () => {
    if (!user?.id) {
      console.log('[ProfileContext] No user ID, cannot load profile');
      setIsLoading(false);
      return;
    }

    console.log('[ProfileContext] Loading cached profile for user:', user.id);
    // Note: isLoading is already set to true by the caller
    try {
      const [cachedProfile, timestamp] = await Promise.all([
        AsyncStorage.getItem(getProfileKey(user.id)),
        AsyncStorage.getItem(getProfileTimestampKey(user.id)),
      ]);

      console.log('[ProfileContext] Cache check:', {
        hasCache: !!cachedProfile,
        hasTimestamp: !!timestamp
      });

      if (cachedProfile && timestamp) {
        const cacheAge = Date.now() - parseInt(timestamp, 10);
        const cacheAgeSeconds = Math.floor(cacheAge / 1000);
        console.log('[ProfileContext] Cache age:', cacheAgeSeconds, 'seconds');

        // Use cache if within duration
        if (cacheAge < PROFILE_CACHE_DURATION) {
          const parsed = JSON.parse(cachedProfile);
          console.log('[ProfileContext] Using cached profile:', parsed);
          setProfile(parsed);
        } else {
          // Cache expired, refresh from API
          console.log('[ProfileContext] Cache expired, refreshing from API');
          await refreshProfile();
        }
      } else {
        // No cache, fetch from API
        console.log('[ProfileContext] No cache found, fetching from API');
        await refreshProfile();
      }
    } catch (error) {
      console.error('[ProfileContext] Failed to load cached profile:', error);
    } finally {
      setIsLoading(false);  // ← Clear loading state when done
      setHasInitialized(true);  // ← Mark as initialized
    }
  };

  const saveProfileToCache = async (newProfile: UserProfile | null) => {
    if (!user?.id) {
      console.log('[ProfileContext] No user ID, cannot save profile to cache');
      return;
    }

    console.log('[ProfileContext] Saving to cache for user', user.id, ':', newProfile ? `Profile ID ${newProfile.id}` : 'null');
    try {
      if (newProfile) {
        await Promise.all([
          AsyncStorage.setItem(getProfileKey(user.id), JSON.stringify(newProfile)),
          AsyncStorage.setItem(getProfileTimestampKey(user.id), Date.now().toString()),
        ]);
        console.log('[ProfileContext] Profile saved to cache and state');
        setProfile(newProfile);
      } else {
        // No profile yet - clear cache for this user
        console.log('[ProfileContext] Clearing profile cache for user', user.id);
        await Promise.all([
          AsyncStorage.removeItem(getProfileKey(user.id)),
          AsyncStorage.removeItem(getProfileTimestampKey(user.id)),
        ]);
        setProfile(null);
      }
    } catch (error) {
      console.error('[ProfileContext] Failed to save profile to cache:', error);
      throw error;
    }
  };

  const refreshProfile = async () => {
    if (!isAuthenticated) {
      console.log('[ProfileContext] Not authenticated, skipping refresh');
      return;
    }

    console.log('[ProfileContext] Refreshing profile...');
    // Note: isLoading is managed by caller (loadCachedProfile or external caller)
    try {
      const profileService = (await import('@/services/profile-service')).default;
      const freshProfile = await profileService.getProfile();
      console.log('[ProfileContext] Profile fetched:', freshProfile);
      await saveProfileToCache(freshProfile);
    } catch (error: any) {
      // If 404, user hasn't created a profile yet - this is OK
      if (error?.response?.status === 404) {
        console.log('[ProfileContext] No profile found (404) - user needs to create one');
        await saveProfileToCache(null);
      } else {
        console.error('[ProfileContext] Failed to refresh profile:', error);
        throw error;
      }
    }
  };

  const updateProfile = async (data: Partial<UserProfile>) => {
    if (!isAuthenticated) {
      throw new Error('Must be authenticated to update profile');
    }

    console.log('[ProfileContext] Updating profile with data:', data);
    setIsLoading(true);
    try {
      const profileService = (await import('@/services/profile-service')).default;
      // Filter out null values for the update
      const updateData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value !== null && value !== undefined) {
          acc[key as keyof typeof acc] = value;
        }
        return acc;
      }, {} as any);

      console.log('[ProfileContext] Sending update to API:', updateData);
      const updatedProfile = await profileService.updateProfile(updateData);
      console.log('[ProfileContext] Profile updated successfully:', updatedProfile);
      await saveProfileToCache(updatedProfile);
    } catch (error) {
      console.error('[ProfileContext] Failed to update profile:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const clearProfile = async () => {
    if (!user?.id) {
      console.log('[ProfileContext] No user ID, just clearing state');
      setProfile(null);
      return;
    }

    console.log('[ProfileContext] Clearing profile for user:', user.id);
    try {
      await Promise.all([
        AsyncStorage.removeItem(getProfileKey(user.id)),
        AsyncStorage.removeItem(getProfileTimestampKey(user.id)),
      ]);
      setProfile(null);
    } catch (error) {
      console.error('[ProfileContext] Failed to clear profile:', error);
      throw error;
    }
  };

  const value: ProfileContextType = {
    profile,
    isLoading,
    hasInitialized,
    refreshProfile,
    updateProfile,
    clearProfile,
  };

  return <ProfileContext.Provider value={value}>{children}</ProfileContext.Provider>;
}

export function useProfile(): ProfileContextType {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}
