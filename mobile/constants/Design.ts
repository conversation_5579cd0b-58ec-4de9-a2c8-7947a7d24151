/**
 * Design System Constants for GallDiet
 *
 * Centralized design tokens for colors, typography, spacing, and shadows.
 * Ensures visual consistency across the application.
 */

export const Colors = {
  // Primary Brand Colors
  primary: {
    50: '#E8F5E9',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#4CAF50',  // Main brand color (safe/healthy green)
    600: '#43A047',
    700: '#388E3C',
    800: '#2E7D32',
    900: '#1B5E20',
  },

  // Safety Score Colors (Semantic)
  safety: {
    safe: '#4CAF50',      // 80-100: Green (safe for user)
    caution: '#FFA726',   // 50-79: Yellow/Orange (moderate risk)
    danger: '#EF5350',    // 0-49: Red (high risk)
  },

  // Severity Colors (for triggers)
  severity: {
    low: '#FFD54F',       // Low severity trigger
    moderate: '#FF9800',  // Moderate severity trigger
    high: '#F44336',      // High severity trigger
  },

  // UI Grays
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },

  // Semantic Colors
  success: '#4CAF50',
  warning: '#FFA726',
  error: '#EF5350',
  info: '#2196F3',

  // Background Colors
  background: {
    primary: '#FFFFFF',
    secondary: '#F5F5F5',
    tertiary: '#EEEEEE',
  },

  // Text Colors
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#BDBDBD',
    inverse: '#FFFFFF',
  },

  // Border Colors
  border: {
    light: '#E0E0E0',
    medium: '#BDBDBD',
    dark: '#757575',
  },

  // Dark Mode Support (for future implementation)
  dark: {
    background: {
      primary: '#121212',
      secondary: '#1E1E1E',
      tertiary: '#2C2C2C',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#B0B0B0',
      disabled: '#757575',
    },
  },
};

export const Typography = {
  // Font Families
  fontFamily: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
    // Note: Using system fonts for MVP. Can be replaced with custom fonts later.
  },

  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  // Font Weights
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },

  // Line Heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

export const Spacing = {
  // Spacing Scale (based on 4px grid)
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,

  // Component-specific spacing
  component: {
    buttonPaddingVertical: 12,
    buttonPaddingHorizontal: 24,
    cardPadding: 16,
    screenPadding: 16,
    sectionMargin: 24,
  },
};

export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,  // For circular elements
};

export const Shadows = {
  // Elevation levels (Material Design inspired)
  none: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },

  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },

  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 8,
  },

  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 12,
  },
};

export const Layout = {
  // Screen breakpoints (for responsive design)
  breakpoints: {
    sm: 375,   // Small phones
    md: 428,   // Regular phones
    lg: 768,   // Tablets
    xl: 1024,  // Large tablets
  },

  // Container max widths
  containerMaxWidth: {
    sm: 640,
    md: 768,
    lg: 1024,
  },
};

export const Animation = {
  // Duration (in milliseconds)
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },

  // Easing functions (React Native Animated compatible)
  easing: {
    linear: 'linear' as const,
    easeIn: 'ease-in' as const,
    easeOut: 'ease-out' as const,
    easeInOut: 'ease-in-out' as const,
  },
};

/**
 * Utility function to get safety color based on score
 */
export const getSafetyColor = (score: number): string => {
  if (score >= 80) return Colors.safety.safe;
  if (score >= 50) return Colors.safety.caution;
  return Colors.safety.danger;
};

/**
 * Utility function to get severity color
 */
export const getSeverityColor = (severity: 'low' | 'moderate' | 'high'): string => {
  return Colors.severity[severity];
};

export default {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Layout,
  Animation,
  getSafetyColor,
  getSeverityColor,
};
