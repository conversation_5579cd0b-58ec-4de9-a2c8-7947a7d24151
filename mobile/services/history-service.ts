import apiClient from '@/lib/api-client';
import { ScanResponse } from './scan-service';

export interface HistoryFilters {
  date_from?: string;
  date_to?: string;
  safety_score_min?: number;
  safety_score_max?: number;
  meal_type?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  search?: string;
}

export interface HistoryResponse {
  data: ScanResponse[];
  next_cursor: string | null;
  prev_cursor: string | null;
  per_page: number;
}

class HistoryService {
  /**
   * Get scan history with cursor-based pagination (T141-T144)
   */
  async getHistory(cursor?: string | null, filters?: HistoryFilters): Promise<HistoryResponse> {
    const params: any = {};

    if (cursor) {
      params.cursor = cursor;
    }

    if (filters) {
      if (filters.date_from) params.date_from = filters.date_from;
      if (filters.date_to) params.date_to = filters.date_to;
      if (filters.safety_score_min !== undefined) params.safety_score_min = filters.safety_score_min;
      if (filters.safety_score_max !== undefined) params.safety_score_max = filters.safety_score_max;
      if (filters.meal_type) params.meal_type = filters.meal_type;
      if (filters.search) params.search = filters.search;
    }

    const response = await apiClient.get<{ scans: HistoryResponse }>('/history', { params });
    return response.data.scans;
  }

  /**
   * Get single scan by ID
   */
  async getScan(scanId: number): Promise<ScanResponse> {
    const response = await apiClient.get<{ scan: ScanResponse }>(`/history/${scanId}`);
    return response.data.scan;
  }
}

export default new HistoryService();
