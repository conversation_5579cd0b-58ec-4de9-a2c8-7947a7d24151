import apiClient from '@/lib/api-client';

export interface PatternSuggestion {
  id: number;
  suspected_trigger_name: string;
  confidence_score: number;
  correlation_count: number;
  status: 'pending' | 'confirmed' | 'rejected';
  evidence: {
    avg_time_gap: number;
    scans: Array<{
      id: number;
      created_at: string;
      ingredients: string[];
    }>;
    attacks: Array<{
      id: number;
      onset_at: string;
      pain_intensity: number;
    }>;
  };
  evidence_summary: string;
  created_at: string;
  updated_at: string;
}

export interface PatternResponse {
  data: PatternSuggestion[];
}

export interface ConfirmResponse {
  message: string;
  trigger: {
    id: number;
    name: string;
    severity: string;
    confidence_score: number;
  };
}

export interface RejectResponse {
  message: string;
}

class PatternService {
  /**
   * Get pending pattern suggestions
   */
  async getSuggestions(): Promise<PatternSuggestion[]> {
    const response = await apiClient.get<PatternResponse>('/patterns/suggestions');
    return response.data.data;
  }

  /**
   * Confirm a pattern suggestion (promotes to user trigger)
   */
  async confirmPattern(suggestionId: number): Promise<ConfirmResponse> {
    const response = await apiClient.post<ConfirmResponse>(
      `/patterns/${suggestionId}/confirm`
    );
    return response.data;
  }

  /**
   * Reject a pattern suggestion
   */
  async rejectPattern(suggestionId: number): Promise<RejectResponse> {
    const response = await apiClient.post<RejectResponse>(`/patterns/${suggestionId}/reject`);
    return response.data;
  }
}

export default new PatternService();
