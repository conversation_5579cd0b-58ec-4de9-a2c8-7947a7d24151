import apiClient from '@/lib/api-client';

export interface ScanPhoto {
  photo: {
    uri: string;
    type: string;
    name: string;
  };
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export interface ScanResponse {
  id: number;
  type: string;
  is_food: boolean;
  status: 'pending' | 'analyzing' | 'completed' | 'failed';
  image_url: string | null;
  meal_name: string | null;
  safety_score: number | null;
  personalized_reasoning: string | null;
  trigger_warnings: Array<{
    trigger_name: string;
    severity: string;
  }>;
  outcome: 'ate' | 'avoided' | null;
  created_at: string;
}

export interface ScanStatusResponse {
  status: 'pending' | 'analyzing' | 'completed' | 'failed';
  progress?: number;
}

class ScanService {
  /**
   * Upload a photo for scanning
   */
  async uploadPhoto(data: ScanPhoto): Promise<ScanResponse> {
    const formData = new FormData();

    // Add photo
    formData.append('photo', {
      uri: data.photo.uri,
      type: data.photo.type,
      name: data.photo.name,
    } as any);

    // Add meal type (required)
    formData.append('meal_type', data.meal_type);

    const response = await apiClient.post<{ scan: ScanResponse }>('/scan/photo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // Backend returns { scan: {...} }
    return response.data.scan;
  }

  /**
   * Get scan status (for polling)
   */
  async getStatus(scanId: number): Promise<ScanStatusResponse> {
    try {
      const response = await apiClient.get<{ scan: ScanResponse }>(`/scan/${scanId}/status`);
      // Backend returns { scan: {...} }, extract just the status
      const scan = response.data.scan;
      if (!scan || !scan.status) {
        throw new Error('Invalid scan status response');
      }
      return {
        status: scan.status,
        progress: undefined, // Could calculate based on status if needed
      };
    } catch (error) {
      console.error('getStatus error:', error);
      throw error;
    }
  }

  /**
   * Get scan result
   */
  async getResult(scanId: number): Promise<ScanResponse> {
    const response = await apiClient.get<{ scan: ScanResponse } | ScanResponse>(`/scan/${scanId}/result`);
    // Result endpoint might return { scan: {...} } or just {...}
    return (response.data as any).scan || response.data;
  }

  /**
   * Poll for scan completion
   * T219: Aggressive polling every 1 second for quick results (< 4s target)
   */
  async pollForCompletion(
    scanId: number,
    onProgress?: (status: ScanStatusResponse) => void,
    maxAttempts: number = 120 // 2 minutes max (120 attempts at 1 second each)
  ): Promise<ScanResponse> {
    let attempts = 0;

    const poll = async (): Promise<ScanResponse> => {
      attempts++;

      if (attempts > maxAttempts) {
        throw new Error('Scan timed out. Please try again.');
      }

      const status = await this.getStatus(scanId);

      if (onProgress) {
        onProgress(status);
      }

      if (status.status === 'completed') {
        return await this.getResult(scanId);
      }

      if (status.status === 'failed') {
        throw new Error('Scan failed. Please try again.');
      }

      // T219: Wait 1 second before next poll (aggressive polling)
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return poll();
    };

    return poll();
  }

  /**
   * Update scan consumption outcome (T110)
   */
  async updateOutcome(scanId: number, outcome: 'ate' | 'avoided'): Promise<ScanResponse> {
    const response = await apiClient.patch<{ scan: ScanResponse }>(`/scan/${scanId}/outcome`, {
      outcome,
    });
    return response.data.scan;
  }

  /**
   * Scan barcode (T153)
   */
  async scanBarcode(barcode: string, meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'): Promise<ScanResponse> {
    const response = await apiClient.post<{ scan: ScanResponse }>('/scan/barcode', {
      barcode,
      meal_type,
    });
    return response.data.scan;
  }
}

export default new ScanService();
