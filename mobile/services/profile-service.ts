import apiClient from '@/lib/api-client';
import { UserProfile } from '@/contexts/profile-context';

export interface UpdateProfileData {
  age?: number;
  sex?: 'male' | 'female' | 'other';
  has_gallbladder?: boolean;
  diagnosis_date?: string;
  onboarding_completed?: boolean;
}

class ProfileService {
  async getProfile(): Promise<UserProfile> {
    const response = await apiClient.get<{ profile: UserProfile }>('/profile');
    return response.data.profile;
  }

  async updateProfile(data: UpdateProfileData): Promise<UserProfile> {
    const response = await apiClient.put<{ profile: UserProfile }>('/profile', data);
    return response.data.profile;
  }

  async createProfile(data: UpdateProfileData): Promise<UserProfile> {
    const response = await apiClient.post<{ profile: UserProfile }>('/profile', data);
    return response.data.profile;
  }
}

export default new ProfileService();
