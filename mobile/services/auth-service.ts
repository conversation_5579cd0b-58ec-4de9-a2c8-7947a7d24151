import apiClient from '@/lib/api-client';
import { User } from '@/contexts/auth-context';

export interface LoginResponse {
  user: User;
  token: string;
}

export interface RegisterResponse {
  user: User;
  token: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/login', credentials);
    return response.data;
  }

  async register(credentials: RegisterCredentials): Promise<RegisterResponse> {
    const response = await apiClient.post<RegisterResponse>('/register', credentials);
    return response.data;
  }

  async logout(): Promise<void> {
    await apiClient.post('/logout');
  }

  async getAuthenticatedUser(): Promise<User> {
    const response = await apiClient.get<{ user: User }>('/user');
    return response.data.user;
  }
}

export default new AuthService();
