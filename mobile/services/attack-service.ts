import apiClient from '@/lib/api-client';
import { ScanResponse } from './scan-service';

export interface AttackData {
  onset_at: string; // ISO 8601 datetime
  pain_intensity: number; // 1-10
  duration_minutes?: number;
  pain_location?: {
    x: number;
    y: number;
    region: string;
  };
  symptoms?: string[];
  medical_care_type?:
    | 'none'
    | 'home_treatment'
    | 'doctor_call'
    | 'primary_care'
    | 'urgent_care'
    | 'emergency_room'
    | 'hospital_admission'
    | 'hospitalization';
  diagnosis_received?: string;
  treatment_received?: string;
  notes?: string;
}

export interface AttackResponse {
  id: number;
  user_id: number;
  onset_at: string;
  pain_intensity: number;
  duration_minutes: number | null;
  pain_location: {
    x: number;
    y: number;
    region: string;
  } | null;
  symptoms: string[];
  medical_care_type: string | null;
  diagnosis_received: string | null;
  treatment_received: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

class AttackService {
  /**
   * Log a new attack
   */
  async logAttack(data: AttackData): Promise<AttackResponse> {
    const response = await apiClient.post<{ attack: AttackResponse }>('/attacks', data);
    return response.data.attack;
  }

  /**
   * Get user's attack history
   */
  async getAttacks(page: number = 1, perPage: number = 20): Promise<AttackResponse[]> {
    try {
      console.log('[AttackService] Fetching attacks...', { page, perPage });
      const response = await apiClient.get<{ data: AttackResponse[] }>('/attacks', {
        params: { page, per_page: perPage },
      });
      console.log('[AttackService] Response received:', response);
      console.log('[AttackService] Response data:', response.data);
      console.log('[AttackService] Attacks array:', response.data?.data);
      // Laravel pagination returns {data: [], links: {}, meta: {}}
      return response.data.data;
    } catch (error: any) {
      console.error('[AttackService] Error fetching attacks:', error);
      console.error('[AttackService] Error response:', error?.response?.data);
      console.error('[AttackService] Error status:', error?.response?.status);
      throw error;
    }
  }

  /**
   * Get a specific attack
   */
  async getAttack(attackId: number): Promise<AttackResponse> {
    const response = await apiClient.get<{ attack: AttackResponse }>(`/attacks/${attackId}`);
    return response.data.attack;
  }

  /**
   * Get recent scans (last N hours) for symptom report
   * This is useful for showing what the user ate before the attack
   */
  async getRecentScans(hoursAgo: number = 8): Promise<ScanResponse[]> {
    const sinceDate = new Date();
    sinceDate.setHours(sinceDate.getHours() - hoursAgo);

    const response = await apiClient.get<{ scans: ScanResponse[] }>('/scan/recent', {
      params: {
        since: sinceDate.toISOString(),
      },
    });

    return response.data.scans;
  }
}

export default new AttackService();
