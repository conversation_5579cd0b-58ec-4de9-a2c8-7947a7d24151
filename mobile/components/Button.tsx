import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  TouchableOpacityProps,
} from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../constants/Design';

export type ButtonVariant = 'primary' | 'secondary' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  /** Button text */
  children: string;

  /** Visual variant */
  variant?: ButtonVariant;

  /** Button size */
  size?: ButtonSize;

  /** Disabled state */
  disabled?: boolean;

  /** Loading state (shows spinner) */
  loading?: boolean;

  /** Full width button */
  fullWidth?: boolean;

  /** Custom style override */
  style?: TouchableOpacityProps['style'];
}

/**
 * Base Button Component
 *
 * Features:
 * - Three variants: primary (brand green), secondary (outline), danger (red)
 * - Three sizes: small, medium, large
 * - Loading state with spinner
 * - Disabled state with reduced opacity
 * - Haptic feedback on press (iOS)
 *
 * Usage:
 * ```tsx
 * <Button variant="primary" size="large" onPress={handlePress}>
 *   Save Profile
 * </Button>
 * ```
 */
export function Button({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = false,
  onPress,
  style,
  ...restProps
}: ButtonProps) {
  const isDisabled = disabled || loading;

  // Get variant styles
  const variantStyles = {
    primary: {
      backgroundColor: Colors.primary[500],
      borderColor: Colors.primary[500],
    },
    secondary: {
      backgroundColor: 'transparent',
      borderColor: Colors.primary[500],
    },
    danger: {
      backgroundColor: Colors.error,
      borderColor: Colors.error,
    },
  };

  // Get size styles
  const sizeStyles = {
    small: {
      paddingVertical: Spacing.sm,
      paddingHorizontal: Spacing.base,
      minHeight: 36,
    },
    medium: {
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.xl,
      minHeight: 44,
    },
    large: {
      paddingVertical: Spacing.base + 2,
      paddingHorizontal: Spacing['2xl'],
      minHeight: 52,
    },
  };

  // Get text variant styles
  const textVariantStyles = {
    primary: {
      color: Colors.text.inverse,
    },
    secondary: {
      color: Colors.primary[500],
    },
    danger: {
      color: Colors.text.inverse,
    },
  };

  // Get text size styles
  const textSizeStyles = {
    small: {
      fontSize: Typography.fontSize.sm,
      lineHeight: Typography.fontSize.sm * 1.3,
    },
    medium: {
      fontSize: Typography.fontSize.base,
      lineHeight: Typography.fontSize.base * 1.3,
    },
    large: {
      fontSize: Typography.fontSize.lg,
      lineHeight: Typography.fontSize.lg * 1.3,
    },
  };

  // Loading spinner color
  const spinnerColor = variant === 'secondary' ? Colors.primary[500] : Colors.text.inverse;

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={0.7}
      style={[
        styles.button,
        variantStyles[variant],
        sizeStyles[size],
        fullWidth && styles.fullWidth,
        isDisabled && styles.disabled,
        style,
      ]}
      {...restProps}
    >
      {loading ? (
        <ActivityIndicator size="small" color={spinnerColor} />
      ) : (
        <Text
          style={[
            styles.text,
            textVariantStyles[variant],
            textSizeStyles[size],
          ]}
        >
          {children}
        </Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.md,
    borderWidth: 2,
    overflow: 'visible', // Ensure text isn't clipped
  },
  fullWidth: {
    width: '100%',
  },
  text: {
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
    flexShrink: 1, // Allow text to shrink if needed
    includeFontPadding: false, // Android: remove extra padding
  },
  disabled: {
    opacity: 0.5,
  },
});

export default Button;
