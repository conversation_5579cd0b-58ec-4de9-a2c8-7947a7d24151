import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';

interface UpgradePromptModalProps {
  visible: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  message?: string;
}

export default function UpgradePromptModal({
  visible,
  onClose,
  onUpgrade,
  message,
}: UpgradePromptModalProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <ScrollView contentContainerStyle={styles.content}>
            <View style={styles.iconContainer}>
              <Text style={styles.icon}>⭐</Text>
            </View>

            <Text style={styles.title}>Upgrade to Premium</Text>

            <Text style={styles.message}>
              {message ||
                "You're discovering YOUR triggers quickly. Premium gives you unlimited scans to help you stay safe."}
            </Text>

            <View style={styles.benefitsContainer}>
              <View style={styles.benefit}>
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.benefitText}>Unlimited photo scans</Text>
              </View>
              <View style={styles.benefit}>
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.benefitText}>Unlimited barcode lookups</Text>
              </View>
              <View style={styles.benefit}>
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.benefitText}>Unlimited scan history</Text>
              </View>
              <View style={styles.benefit}>
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.benefitText}>AI recipe modifications</Text>
              </View>
              <View style={styles.benefit}>
                <Text style={styles.checkmark}>✓</Text>
                <Text style={styles.benefitText}>Priority support</Text>
              </View>
            </View>

            <View style={styles.priceContainer}>
              <Text style={styles.price}>$9.99/month</Text>
              <Text style={styles.priceSubtext}>7-day free trial</Text>
            </View>

            <TouchableOpacity style={styles.upgradeButton} onPress={onUpgrade}>
              <Text style={styles.upgradeButtonText}>Start Free Trial</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>Maybe Later</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modal: {
    backgroundColor: '#fff',
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  content: {
    padding: 24,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  icon: {
    fontSize: 64,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  benefitsContainer: {
    marginBottom: 24,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkmark: {
    fontSize: 20,
    color: '#10b981',
    marginRight: 12,
    fontWeight: 'bold',
  },
  benefitText: {
    fontSize: 16,
    color: '#1a1a1a',
    flex: 1,
  },
  priceContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  price: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  priceSubtext: {
    fontSize: 14,
    color: '#64748b',
  },
  upgradeButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  upgradeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  closeButton: {
    paddingVertical: 12,
  },
  closeButtonText: {
    color: '#64748b',
    fontSize: 16,
    textAlign: 'center',
  },
});
