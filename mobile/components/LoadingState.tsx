import React from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing } from '../constants/Design';

export interface LoadingStateProps {
  /** Loading message to display */
  message?: string;

  /** Optional progress value (0-100) */
  progress?: number;

  /** Size of the spinner */
  size?: 'small' | 'large';
}

/**
 * LoadingState Component
 *
 * Shows a centered loading spinner with an optional personalized message.
 * Used during async operations like photo analysis.
 *
 * Features:
 * - Centered spinner
 * - Optional message below spinner
 * - Optional progress percentage
 * - Personalized loading messages per constitution.md
 *
 * Personalized Messages (examples):
 * - "Uploading photo..."
 * - "Analyzing against YOUR triggers..."
 * - "Checking YOUR profile..."
 * - "Calculating YOUR safety score..."
 * - "Complete!"
 *
 * Usage:
 * ```tsx
 * <LoadingState
 *   message="Analyzing against YOUR triggers..."
 *   progress={65}
 * />
 * ```
 */
export function LoadingState({
  message = 'Loading...',
  progress,
  size = 'large',
}: LoadingStateProps) {
  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color={Colors.primary[500]} />

      {message && (
        <Text style={styles.message}>{message}</Text>
      )}

      {progress !== undefined && progress >= 0 && progress <= 100 && (
        <Text style={styles.progress}>{Math.round(progress)}%</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing['2xl'],
  },
  message: {
    marginTop: Spacing.base,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  progress: {
    marginTop: Spacing.sm,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.normal,
    color: Colors.text.disabled,
  },
});

export default LoadingState;
