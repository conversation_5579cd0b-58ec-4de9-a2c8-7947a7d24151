import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';

interface TriggerAutocompleteProps {
  value: string;
  onChangeText: (text: string) => void;
  onSelect: (trigger: string) => void;
  placeholder?: string;
}

// Common gallstone triggers based on medical literature
const COMMON_TRIGGERS = [
  'Fried foods',
  'Fatty meats',
  'High-fat dairy',
  'Butter',
  'Cheese',
  'Ice cream',
  'Cream',
  'Whole milk',
  'Eggs',
  'Chocolate',
  'Avocado',
  'Nuts',
  'Peanut butter',
  'Coconut',
  'Coconut oil',
  'Palm oil',
  'Fast food',
  'Pizza',
  'Burgers',
  'French fries',
  'Onion rings',
  'Fried chicken',
  'Bacon',
  'Sausage',
  'Hot dogs',
  'Deli meats',
  'Pork',
  'Lamb',
  'Beef',
  'Spicy foods',
  'Caffeine',
  'Coffee',
  'Alcohol',
  'Wine',
  'Beer',
  'Carbonated drinks',
  'Citrus fruits',
  'Tomatoes',
  'Beans',
  'Cabbage',
  'Onions',
  'Garlic',
  'Radishes',
  'Turnips',
  'Cucumbers',
  'Bell peppers',
  'Corn',
  'Artificial sweeteners',
  'Processed foods',
  'Refined sugar',
  'White bread',
  'Pastries',
  'Donuts',
  'Cookies',
  'Cake',
  'Mayonnaise',
  'Salad dressing',
  'Gravy',
  'Creamy sauces',
];

export default function TriggerAutocomplete({
  value,
  onChangeText,
  onSelect,
  placeholder = 'e.g., fried foods, chocolate, dairy',
}: TriggerAutocompleteProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);

  const getSuggestions = () => {
    if (!value.trim()) return [];

    const searchTerm = value.toLowerCase();
    return COMMON_TRIGGERS.filter((trigger) =>
      trigger.toLowerCase().includes(searchTerm)
    ).slice(0, 5); // Limit to 5 suggestions
  };

  const suggestions = getSuggestions();

  const handleSelect = (trigger: string) => {
    onSelect(trigger);
    setShowSuggestions(false);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <TextInput
        style={styles.input}
        placeholder={placeholder}
        value={value}
        onChangeText={(text) => {
          onChangeText(text);
          setShowSuggestions(text.length > 0);
        }}
        onFocus={() => setShowSuggestions(value.length > 0)}
        autoCapitalize="none"
        autoCorrect={false}
      />

      {showSuggestions && suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          {suggestions.map((item, index) => (
            <TouchableOpacity
              key={`${item}-${index}`}
              style={styles.suggestionItem}
              onPress={() => handleSelect(item)}
            >
              <Text style={styles.suggestionText}>{item}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  suggestionItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionText: {
    fontSize: 15,
    color: '#333',
  },
});
