import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import Svg, { Path, Circle } from 'react-native-svg';

const SCREEN_WIDTH = Dimensions.get('window').width;
const DIAGRAM_SIZE = Math.min(SCREEN_WIDTH * 0.7, 300);

export interface PainLocation {
  x: number;
  y: number;
  region: string;
}

interface BodyDiagramProps {
  onLocationSelect: (location: PainLocation) => void;
  selectedLocation?: PainLocation | null;
}

// Body regions with their approximate coordinates (normalized 0-1)
const BODY_REGIONS = [
  { name: 'Upper Right Abdomen', x: 0.4, y: 0.35, radius: 40 },
  { name: 'Upper Central Abdomen', x: 0.5, y: 0.35, radius: 35 },
  { name: 'Upper Left Abdomen', x: 0.6, y: 0.35, radius: 40 },
  { name: 'Middle Right Abdomen', x: 0.4, y: 0.5, radius: 35 },
  { name: 'Middle Central Abdomen', x: 0.5, y: 0.5, radius: 30 },
  { name: 'Middle Left Abdomen', x: 0.6, y: 0.5, radius: 35 },
  { name: 'Lower Right Abdomen', x: 0.4, y: 0.65, radius: 35 },
  { name: 'Lower Central Abdomen', x: 0.5, y: 0.65, radius: 30 },
  { name: 'Lower Left Abdomen', x: 0.6, y: 0.65, radius: 35 },
  { name: 'Right Shoulder', x: 0.35, y: 0.2, radius: 30 },
  { name: 'Back', x: 0.5, y: 0.4, radius: 50 },
];

export default function BodyDiagram({ onLocationSelect, selectedLocation }: BodyDiagramProps) {
  const [pressedRegion, setPressedRegion] = useState<string | null>(null);

  const handleRegionPress = (region: typeof BODY_REGIONS[0]) => {
    // Normalize coordinates
    const location: PainLocation = {
      x: region.x,
      y: region.y,
      region: region.name,
    };
    onLocationSelect(location);
  };

  const isSelected = (regionName: string) => {
    return selectedLocation?.region === regionName;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Where is your pain?</Text>
      <Text style={styles.subtitle}>Tap the area where you feel pain</Text>

      <View style={styles.diagramContainer}>
        <Svg width={DIAGRAM_SIZE} height={DIAGRAM_SIZE} viewBox="0 0 100 100">
          {/* Simple torso outline */}
          <Path
            d="M 35 15 Q 30 15 30 20 L 30 70 Q 30 75 35 75 L 65 75 Q 70 75 70 70 L 70 20 Q 70 15 65 15 Z"
            fill="#f0f0f0"
            stroke="#999"
            strokeWidth="0.5"
          />

          {/* Head/shoulders */}
          <Path
            d="M 40 15 L 40 10 Q 40 5 50 5 Q 60 5 60 10 L 60 15"
            fill="#f0f0f0"
            stroke="#999"
            strokeWidth="0.5"
          />

          {/* Show clickable regions */}
          {BODY_REGIONS.map((region, index) => {
            const selected = isSelected(region.name);
            const pressed = pressedRegion === region.name;
            return (
              <Circle
                key={index}
                cx={region.x * 100}
                cy={region.y * 100}
                r={region.radius / (DIAGRAM_SIZE / 100)}
                fill={selected ? '#3b82f6' : pressed ? '#93c5fd' : 'transparent'}
                stroke={selected ? '#3b82f6' : '#cbd5e1'}
                strokeWidth="0.5"
                strokeDasharray={selected ? '0' : '2,2'}
                opacity={selected ? 0.3 : pressed ? 0.2 : 0.1}
              />
            );
          })}
        </Svg>

        {/* Clickable overlay buttons */}
        <View style={StyleSheet.absoluteFill}>
          {BODY_REGIONS.map((region, index) => {
            const selected = isSelected(region.name);
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.regionButton,
                  {
                    left: region.x * DIAGRAM_SIZE - region.radius / 2,
                    top: region.y * DIAGRAM_SIZE - region.radius / 2,
                    width: region.radius,
                    height: region.radius,
                  },
                  selected && styles.regionButtonSelected,
                ]}
                onPress={() => handleRegionPress(region)}
                onPressIn={() => setPressedRegion(region.name)}
                onPressOut={() => setPressedRegion(null)}
                activeOpacity={0.7}
              />
            );
          })}
        </View>
      </View>

      {selectedLocation && (
        <View style={styles.selectedRegion}>
          <Text style={styles.selectedRegionLabel}>Selected area:</Text>
          <Text style={styles.selectedRegionText}>{selectedLocation.region}</Text>
        </View>
      )}

      <Text style={styles.note}>
        Note: Gallbladder pain commonly occurs in the upper right abdomen and may radiate to the
        right shoulder or back
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
  },
  diagramContainer: {
    width: DIAGRAM_SIZE,
    height: DIAGRAM_SIZE,
    position: 'relative',
    marginBottom: 16,
  },
  regionButton: {
    position: 'absolute',
    borderRadius: 100,
  },
  regionButtonSelected: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  selectedRegion: {
    backgroundColor: '#eff6ff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  selectedRegionLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  selectedRegionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3b82f6',
  },
  note: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingHorizontal: 16,
    lineHeight: 18,
  },
});
