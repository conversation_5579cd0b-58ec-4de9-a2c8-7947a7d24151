import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing } from '../constants/Design';
import { Button } from './Button';

export interface EmptyStateProps {
  /** Icon component (e.g., from @expo/vector-icons) */
  icon?: React.ReactNode;

  /** Heading text */
  heading: string;

  /** Body text */
  body: string;

  /** CTA button text (optional) */
  ctaText?: string;

  /** CTA button press handler */
  onCtaPress?: () => void;
}

/**
 * EmptyState Component
 *
 * Displays when a list or screen has no content.
 * Provides visual feedback and optional call-to-action.
 *
 * Features:
 * - Optional icon
 * - Heading and body text
 * - Optional CTA button
 * - Centered layout
 * - Reusable across all empty screens
 *
 * Common Use Cases:
 * - Empty scan history: "No scans yet"
 * - Empty triggers: "No triggers identified"
 * - Empty recipes: "No saved recipes"
 * - Empty patterns: "No patterns detected yet"
 *
 * Usage:
 * ```tsx
 * <EmptyState
 *   icon={<Ionicons name="camera-outline" size={64} color={Colors.gray[400]} />}
 *   heading="No Scans Yet"
 *   body="Start scanning meals to build your personalized safety history."
 *   ctaText="Scan Your First Meal"
 *   onCtaPress={handleScanPress}
 * />
 * ```
 */
export function EmptyState({
  icon,
  heading,
  body,
  ctaText,
  onCtaPress,
}: EmptyStateProps) {
  return (
    <View style={styles.container}>
      {icon && <View style={styles.iconContainer}>{icon}</View>}

      <Text style={styles.heading}>{heading}</Text>

      <Text style={styles.body}>{body}</Text>

      {ctaText && onCtaPress && (
        <Button
          variant="primary"
          size="large"
          onPress={onCtaPress}
          style={styles.cta}
        >
          {ctaText}
        </Button>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing['3xl'],
  },
  iconContainer: {
    marginBottom: Spacing.xl,
  },
  heading: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  body: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.normal,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
    marginBottom: Spacing.xl,
  },
  cta: {
    marginTop: Spacing.base,
  },
});

export default EmptyState;
