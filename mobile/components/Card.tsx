import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TouchableOpacityProps,
} from 'react-native';
import { Colors, Spacing, BorderRadius, Shadows } from '../constants/Design';

export interface CardProps {
  /** Card content */
  children: React.ReactNode;

  /** Add elevated shadow */
  elevated?: boolean;

  /** Make card pressable */
  onPress?: TouchableOpacityProps['onPress'];

  /** Custom style override */
  style?: ViewStyle;

  /** Disable default padding */
  noPadding?: boolean;
}

/**
 * Base Card Component
 *
 * A container with rounded corners, optional elevation, and optional press handling.
 *
 * Features:
 * - Default padding (can be disabled with noPadding)
 * - Optional elevation shadow
 * - Optional press interaction
 * - Clean, minimal design
 *
 * Usage:
 * ```tsx
 * <Card elevated onPress={handlePress}>
 *   <Text>Scan Result</Text>
 * </Card>
 * ```
 */
export function Card({
  children,
  elevated = false,
  onPress,
  style,
  noPadding = false,
}: CardProps) {
  const cardStyle = [
    styles.card,
    elevated && Shadows.md,
    !noPadding && styles.padding,
    style,
  ];

  // If onPress is provided, use TouchableOpacity
  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.8}
        style={cardStyle}
      >
        {children}
      </TouchableOpacity>
    );
  }

  // Otherwise, use plain View
  return <View style={cardStyle}>{children}</View>;
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  padding: {
    padding: Spacing.component.cardPadding,
  },
});

export default Card;
