import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '@/constants/Design';

const { width } = Dimensions.get('window');

export interface MilestoneCelebrationProps {
  visible: boolean;
  days: number; // Number of attack-free days
  onClose: () => void;
}

/**
 * Milestone Celebration Component (FR-073, T204)
 *
 * Celebrates attack-free milestones:
 * - 7 days: "One week attack-free!"
 * - 14 days: "Two weeks attack-free!"
 * - 30 days: "One month attack-free!"
 * - 60 days: "Two months attack-free!"
 * - 90 days: "Three months attack-free!"
 * - 180 days: "Six months attack-free!"
 * - 365 days: "One year attack-free!"
 *
 * Shows animated celebration with confetti-like effect.
 */
export function MilestoneCelebration({ visible, days, onClose }: MilestoneCelebrationProps) {
  const scaleAnim = new Animated.Value(0);
  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    if (visible) {
      // Scale and fade in animation
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      scaleAnim.setValue(0);
      fadeAnim.setValue(0);
    }
  }, [visible]);

  const getMilestoneInfo = (days: number) => {
    if (days >= 365) {
      return {
        emoji: '🎊',
        title: 'One Year Attack-Free!',
        message: 'Incredible achievement! You\'ve been attack-free for a full year!',
        color: Colors.primary[500],
        badge: '1 Year',
      };
    } else if (days >= 180) {
      return {
        emoji: '🌟',
        title: 'Six Months Attack-Free!',
        message: 'Half a year without attacks! Your dedication is paying off!',
        color: Colors.primary[600],
        badge: '6 Months',
      };
    } else if (days >= 90) {
      return {
        emoji: '🎉',
        title: 'Three Months Attack-Free!',
        message: 'Amazing progress! Three months of staying healthy!',
        color: Colors.primary[500],
        badge: '3 Months',
      };
    } else if (days >= 60) {
      return {
        emoji: '🏆',
        title: 'Two Months Attack-Free!',
        message: 'Fantastic! You\'re building great habits!',
        color: Colors.success,
        badge: '2 Months',
      };
    } else if (days >= 30) {
      return {
        emoji: '✨',
        title: 'One Month Attack-Free!',
        message: 'Congratulations! A full month of avoiding attacks!',
        color: Colors.success,
        badge: '30 Days',
      };
    } else if (days >= 14) {
      return {
        emoji: '🎯',
        title: 'Two Weeks Attack-Free!',
        message: 'Great job! You\'re learning what works for YOUR body!',
        color: Colors.primary[400],
        badge: '2 Weeks',
      };
    } else if (days >= 7) {
      return {
        emoji: '🌱',
        title: 'One Week Attack-Free!',
        message: 'Excellent start! Keep avoiding your triggers!',
        color: Colors.primary[300],
        badge: '7 Days',
      };
    } else {
      return {
        emoji: '💪',
        title: `${days} Days Attack-Free!`,
        message: 'Every day counts! Keep it up!',
        color: Colors.primary[200],
        badge: `${days} Days`,
      };
    }
  };

  const milestoneInfo = getMilestoneInfo(days);

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <TouchableOpacity
          style={styles.modalBackground}
          activeOpacity={1}
          onPress={onClose}
        />

        <Animated.View
          style={[
            styles.celebrationCard,
            {
              transform: [{ scale: scaleAnim }],
              opacity: fadeAnim,
            },
          ]}
        >
          {/* Close Button */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="close-circle" size={32} color={Colors.gray[400]} />
          </TouchableOpacity>

          {/* Emoji/Icon */}
          <Text style={styles.emoji}>{milestoneInfo.emoji}</Text>

          {/* Badge */}
          <View style={[styles.badge, { backgroundColor: milestoneInfo.color }]}>
            <Text style={styles.badgeText}>{milestoneInfo.badge}</Text>
          </View>

          {/* Title */}
          <Text style={styles.title}>{milestoneInfo.title}</Text>

          {/* Message */}
          <Text style={styles.message}>{milestoneInfo.message}</Text>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statBox}>
              <Text style={styles.statValue}>{days}</Text>
              <Text style={styles.statLabel}>Days</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statBox}>
              <Text style={styles.statValue}>{Math.floor(days / 7)}</Text>
              <Text style={styles.statLabel}>Weeks</Text>
            </View>
            {days >= 30 && (
              <>
                <View style={styles.statDivider} />
                <View style={styles.statBox}>
                  <Text style={styles.statValue}>{Math.floor(days / 30)}</Text>
                  <Text style={styles.statLabel}>Months</Text>
                </View>
              </>
            )}
          </View>

          {/* Encouragement */}
          <View style={styles.encouragement}>
            <Ionicons name="heart" size={20} color={Colors.error} />
            <Text style={styles.encouragementText}>
              Your triggers are identified. Keep avoiding them!
            </Text>
          </View>

          {/* Continue Button */}
          <TouchableOpacity
            style={[styles.continueButton, { backgroundColor: milestoneInfo.color }]}
            onPress={onClose}
            activeOpacity={0.8}
          >
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  celebrationCard: {
    width: width - 48,
    maxWidth: 400,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
    alignItems: 'center',
    ...Shadows.xl,
  },
  closeButton: {
    position: 'absolute',
    top: Spacing.md,
    right: Spacing.md,
    zIndex: 10,
  },
  emoji: {
    fontSize: 72,
    marginVertical: Spacing.md,
  },
  badge: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    marginBottom: Spacing.md,
  },
  badgeText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  title: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  message: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
    marginBottom: Spacing.xl,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.base,
    marginBottom: Spacing.lg,
    width: '100%',
    justifyContent: 'space-around',
  },
  statBox: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary[500],
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  statDivider: {
    width: 1,
    backgroundColor: Colors.border.light,
    marginHorizontal: Spacing.sm,
  },
  encouragement: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    backgroundColor: Colors.primary[50],
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.lg,
  },
  encouragementText: {
    flex: 1,
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
    fontWeight: Typography.fontWeight.medium,
  },
  continueButton: {
    width: '100%',
    paddingVertical: Spacing.base,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.inverse,
  },
});

export default MilestoneCelebration;
