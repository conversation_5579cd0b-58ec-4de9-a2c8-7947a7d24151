import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { Stack, useRouter, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState, useRef } from 'react';
import { View, ActivityIndicator } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/use-color-scheme';
import { AuthProvider, ProfileProvider, OnboardingProvider, useAuth, useProfile } from '@/contexts';

// Removed anchor to prevent pre-loading tabs before navigation is ready
// export const unstable_settings = {
//   anchor: '(tabs)',
// };

function RootLayoutNav() {
  const colorScheme = useColorScheme();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { profile, isLoading: profileLoading, hasInitialized } = useProfile();
  const segments = useSegments();
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);
  const hasNavigated = useRef(false);

  // Only reset on logout (when going from authenticated to not authenticated)
  const prevAuth = useRef(isAuthenticated);
  useEffect(() => {
    if (prevAuth.current && !isAuthenticated) {
      // User logged out - reset
      console.log('[Navigation] User logged out, resetting navigation state');
      setIsReady(false);
      hasNavigated.current = false;
    }
    prevAuth.current = isAuthenticated;
  }, [isAuthenticated]);

  useEffect(() => {
    // Wait for both auth and profile to load
    if (authLoading || profileLoading) {
      console.log('[Navigation] Loading... auth:', authLoading, 'profile:', profileLoading);
      return;
    }

    // If authenticated but profile hasn't been initialized yet, wait
    if (isAuthenticated && !hasInitialized) {
      console.log('[Navigation] Waiting for profile initialization...');
      return;
    }

    const currentSegment = segments[0] as string | undefined;
    const inAuthGroup = currentSegment === 'auth';
    const inOnboardingGroup = currentSegment === '(onboarding)';
    const inTabsGroup = currentSegment === '(tabs)';
    const hasNoSegment = !currentSegment;

    // Check if onboarding is fully completed (not just minimum fields)
    const hasCompletedOnboarding = profile && profile.onboarding_completed === true;

    console.log('[Navigation] State:', {
      isAuthenticated,
      hasProfile: !!profile,
      hasCompletedOnboarding,
      segment: currentSegment,
      onboardingCompleted: profile?.onboarding_completed,
    });

    // Not authenticated → redirect to login
    if (!isAuthenticated) {
      if (!inAuthGroup) {
        console.log('[Navigation] Not authenticated, redirecting to login');
        router.replace('/auth/login' as any);
        setTimeout(() => setIsReady(true), 50);
      } else {
        // Already in auth group
        setIsReady(true);
      }
      return;
    }

    // Handle initial navigation (no segment) or auth group transitions
    if (hasNoSegment || inAuthGroup) {
      if (!hasNavigated.current) {
        hasNavigated.current = true;
        if (hasCompletedOnboarding) {
          console.log('[Navigation] Authenticated with completed onboarding, going to tabs');
          router.replace('/(tabs)' as any);
        } else {
          console.log('[Navigation] Authenticated but onboarding not complete, going to onboarding');
          router.replace('/(onboarding)/profile-basic' as any);
        }
        // Small delay to let router.replace take effect before showing UI
        setTimeout(() => setIsReady(true), 50);
      } else {
        // Already navigated, just show UI
        setIsReady(true);
      }
      return;
    }

    // Authenticated - check if user is in the wrong place and redirect immediately
    if (!hasCompletedOnboarding && !inOnboardingGroup) {
      // User hasn't completed onboarding but is not in onboarding → redirect
      console.log('[Navigation] Onboarding incomplete, redirecting to onboarding');
      router.replace('/(onboarding)/profile-basic' as any);
      setTimeout(() => setIsReady(true), 50);
      return;
    }

    if (hasCompletedOnboarding && inOnboardingGroup) {
      // User completed onboarding but is still in onboarding → redirect
      console.log('[Navigation] Onboarding complete but in onboarding group, going to tabs');
      router.replace('/(tabs)' as any);
      setTimeout(() => setIsReady(true), 50);
      return;
    }

    // User is in correct location, show UI
    console.log('[Navigation] User in correct location, showing UI');
    setIsReady(true);
  }, [isAuthenticated, profile, segments, authLoading, profileLoading, hasInitialized]);

  // Debug logging for loading state
  useEffect(() => {
    console.log('[Navigation] State check:', {
      authLoading,
      profileLoading,
      hasInitialized,
      isAuthenticated,
      isReady,
      segment: segments[0],
    });
  }, [authLoading, profileLoading, hasInitialized, isAuthenticated, isReady, segments]);

  // Show loading screen until auth and profile are ready AND navigation is complete
  if (authLoading || profileLoading || (isAuthenticated && !hasInitialized) || !isReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack screenOptions={{ animation: 'none' }}>
        <Stack.Screen name="auth" options={{ headerShown: false }} />
        <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen
          name="emergency-support"
          options={{
            title: 'Emergency Support',
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen
          name="document-symptoms"
          options={{
            title: 'Document Symptoms',
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen
          name="log-attack"
          options={{
            title: 'Log Attack',
            headerBackTitle: 'Back',
          }}
        />
        <Stack.Screen name="modal" options={{ presentation: 'modal', title: 'Modal' }} />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}

export default function RootLayout() {
  return (
    <AuthProvider>
      <ProfileProvider>
        <OnboardingProvider>
          <RootLayoutNav />
        </OnboardingProvider>
      </ProfileProvider>
    </AuthProvider>
  );
}
