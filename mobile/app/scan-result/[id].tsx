import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import scanService, { ScanResponse } from '@/services/scan-service';

export default function ScanResultScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const [scan, setScan] = useState<ScanResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false); // T222: Toggle for ingredient details

  useEffect(() => {
    loadScanResult();
  }, [id]);

  const loadScanResult = async () => {
    try {
      setLoading(true);
      setError(null);

      const scanId = parseInt(id as string, 10);
      if (isNaN(scanId)) {
        throw new Error('Invalid scan ID');
      }

      const result = await scanService.getResult(scanId);
      setScan(result);
    } catch (err: any) {
      console.error('Failed to load scan result:', err);
      setError(err?.message || 'Failed to load scan result');
      Alert.alert(
        'Error',
        'Failed to load scan result. Please try again.',
        [
          { text: 'Go Back', onPress: () => router.back() }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleOutcome = async (outcome: 'ate' | 'avoided') => {
    try {
      const scanId = parseInt(id as string, 10);
      const updatedScan = await scanService.updateOutcome(scanId, outcome);
      setScan(updatedScan);
      Alert.alert(
        'Success',
        outcome === 'ate'
          ? 'Marked as eaten. We\'ll track this to identify gallstone triggers!'
          : 'Marked as avoided. Smart choice to prevent a gallstone attack!'
      );
    } catch (err: any) {
      console.error('Failed to update outcome:', err);
      Alert.alert('Error', 'Failed to update consumption status. Please try again.');
    }
  };

  const getSafetyColor = (score: number | null): string => {
    if (score === null) return '#94a3b8';
    if (score >= 80) return '#10b981'; // Green - safe
    if (score >= 60) return '#f59e0b'; // Amber - moderate risk
    if (score >= 30) return '#f97316'; // Orange - high risk
    return '#ef4444'; // Red - critical
  };

  const getSafetyLabel = (score: number | null): string => {
    if (score === null) return 'Unknown';
    if (score >= 80) return 'Safe for You';
    if (score >= 60) return 'Moderate Risk';
    if (score >= 30) return 'High Risk';
    return 'Critical Risk';
  };

  // T221: Get simple yes/no guidance for quick decisions
  const getSimpleGuidance = (score: number | null): string => {
    if (score === null) return 'Unable to analyze';
    if (score >= 80) return 'Safe to eat';
    if (score >= 60) return 'Proceed with caution';
    if (score >= 30) return 'High risk - consider alternatives';
    return 'Avoid this meal';
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading scan result...</Text>
      </View>
    );
  }

  if (error || !scan) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{error || 'Scan not found'}</Text>
        <TouchableOpacity style={styles.button} onPress={() => router.back()}>
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const safetyScore = scan.safety_score ?? 0;
  const safetyColor = getSafetyColor(safetyScore);
  const safetyLabel = getSafetyLabel(safetyScore);
  const simpleGuidance = getSimpleGuidance(safetyScore);

  // Handle non-food images
  if (!scan.is_food) {
    return (
      <ScrollView style={styles.scrollView}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.mealName}>Not a Meal</Text>
            <Text style={styles.mealType}>{scan.type}</Text>
          </View>

          {/* Image */}
          {scan.image_url && (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: scan.image_url }}
                style={styles.mealImage}
                resizeMode="cover"
              />
            </View>
          )}

          {/* Non-Food Notice */}
          <View style={styles.nonFoodCard}>
            <Text style={styles.nonFoodIcon}>🤔</Text>
            <Text style={styles.nonFoodTitle}>No Food Detected</Text>
            <Text style={styles.nonFoodMessage}>
              {scan.personalized_reasoning ||
                "This image doesn't appear to contain any food or meals that we can analyze for safety."}
            </Text>
            <Text style={styles.nonFoodSubtext}>
              Please take a photo of your meal to get a personalized safety analysis.
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton]}
              onPress={() => router.push('/(tabs)/scan')}
            >
              <Text style={styles.primaryButtonText}>Scan a Meal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={() => router.push('/(tabs)/')}
            >
              <Text style={styles.secondaryButtonText}>Go to Dashboard</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView style={styles.scrollView}>
      <View style={styles.container}>
        {/* Meal Name Header */}
        <View style={styles.header}>
          <Text style={styles.mealName}>{scan.meal_name || 'Meal'}</Text>
          <Text style={styles.mealType}>{scan.type}</Text>
        </View>

        {/* Meal Image */}
        {scan.image_url && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: scan.image_url }}
              style={styles.mealImage}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Critical Allergen Alert - T109 (only for food items) */}
        {safetyScore === 0 && scan.is_food && (
          <View style={styles.allergenAlert}>
            <View style={styles.allergenAlertHeader}>
              <Text style={styles.allergenAlertIcon}>⛔</Text>
              <Text style={styles.allergenAlertTitle}>ALLERGEN DETECTED</Text>
            </View>
            <Text style={styles.allergenAlertMessage}>
              This meal contains ingredients you've marked as allergens. DO NOT EAT.
            </Text>
            <Text style={styles.allergenAlertSubtext}>
              Your safety is our priority. Please avoid this meal entirely.
            </Text>
          </View>
        )}

        {/* Safety Score Card - T221: Simplified with large score and guidance */}
        <View style={styles.scoreCard}>
          <Text style={styles.scoreLabel}>Your Safety Score</Text>
          <View style={[styles.scoreCircle, { borderColor: safetyColor }]}>
            <Text style={[styles.scoreValue, { color: safetyColor }]}>
              {safetyScore}
            </Text>
            <Text style={styles.scoreMax}>/100</Text>
          </View>
          <Text style={[styles.safetyLabel, { color: safetyColor }]}>
            {safetyLabel}
          </Text>
          {/* T221: Simple yes/no guidance */}
          <View style={[styles.guidanceBadge, { backgroundColor: safetyScore >= 80 ? '#d1fae5' : safetyScore >= 60 ? '#fed7aa' : '#fecaca' }]}>
            <Text style={[styles.guidanceText, { color: safetyScore >= 80 ? '#065f46' : safetyScore >= 60 ? '#9a3412' : '#991b1b' }]}>
              {simpleGuidance}
            </Text>
          </View>
        </View>

        {/* Personalized Reasoning */}
        {scan.personalized_reasoning && (
          <View style={styles.reasoningCard}>
            <Text style={styles.reasoningTitle}>Gallbladder Safety Analysis</Text>
            <Text style={styles.reasoningText}>
              {safetyScore === 100
                ? `Great choice! This meal doesn't contain any of your known gallstone triggers. Based on your history, you should be able to enjoy this safely without triggering symptoms.`
                : scan.personalized_reasoning}
            </Text>
          </View>
        )}

        {/* T222: Trigger Warnings - Collapsible "See Details" */}
        {scan.trigger_warnings && scan.trigger_warnings.length > 0 && (
          <View style={styles.warningsCard}>
            <TouchableOpacity
              style={styles.detailsToggle}
              onPress={() => setShowDetails(!showDetails)}
            >
              <Text style={styles.detailsToggleText}>
                {showDetails ? '▼ Hide Details' : '▶ See Details'}
              </Text>
              <Text style={styles.detailsToggleCount}>
                {scan.trigger_warnings.length} trigger{scan.trigger_warnings.length !== 1 ? 's' : ''} detected
              </Text>
            </TouchableOpacity>

            {showDetails && (
              <View style={styles.detailsContent}>
                <Text style={styles.warningsTitle}>Detected Triggers</Text>
                {scan.trigger_warnings.map((warning, index) => (
                  <View key={index} style={styles.warningItem}>
                    <View style={styles.warningIcon}>
                      <Text style={styles.warningIconText}>⚠️</Text>
                    </View>
                    <View style={styles.warningContent}>
                      <Text style={styles.warningTrigger}>{warning.trigger_name}</Text>
                      <Text style={styles.warningSeverity}>
                        Severity: {warning.severity}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        {/* T223: Prominent action buttons - Consumption Tracking */}
        {!scan.outcome && (
          <View style={styles.outcomeCard}>
            <Text style={styles.outcomeTitle}>Did you eat this meal?</Text>
            <Text style={styles.outcomeSubtext}>
              Tracking helps us learn which foods trigger YOUR gallstone attacks
            </Text>
            <View style={styles.outcomeButtons}>
              <TouchableOpacity
                style={[styles.button, styles.ateButton]}
                onPress={() => handleOutcome('ate')}
              >
                <Text style={styles.ateButtonText}>✓ I Ate This</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.avoidedButton]}
                onPress={() => handleOutcome('avoided')}
              >
                <Text style={styles.avoidedButtonText}>✗ I Avoided This</Text>
              </TouchableOpacity>
            </View>
            {/* T223: Quick "Scan Another" action */}
            <TouchableOpacity
              style={[styles.button, styles.scanAnotherButton]}
              onPress={() => router.push('/(tabs)/scan')}
            >
              <Text style={styles.scanAnotherButtonText}>🔍 Scan Another Meal</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Outcome Status */}
        {scan.outcome && (
          <View style={styles.outcomeStatusCard}>
            <Text style={styles.outcomeStatusText}>
              {scan.outcome === 'ate' ? '✓ You ate this meal' : '✗ You avoided this meal'}
            </Text>
          </View>
        )}

        {/* Medical Disclaimer - T111 */}
        <View style={styles.disclaimerCard}>
          <Text style={styles.disclaimerIcon}>ℹ️</Text>
          <Text style={styles.disclaimerText}>
            This analysis is for informational purposes only and should not replace professional medical advice.
            Always consult your healthcare provider for medical decisions.
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={() => router.push('/(tabs)/scan')}
          >
            <Text style={styles.primaryButtonText}>Scan Another Meal</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={() => router.push('/(tabs)/')}
          >
            <Text style={styles.secondaryButtonText}>Go to Dashboard</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  mealName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    textAlign: 'center',
  },
  mealType: {
    fontSize: 14,
    color: '#64748b',
    textTransform: 'capitalize',
    marginTop: 4,
  },
  imageContainer: {
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mealImage: {
    width: '100%',
    height: 240,
  },
  scoreCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scoreLabel: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 16,
  },
  scoreCircle: {
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreValue: {
    fontSize: 56,
    fontWeight: 'bold',
  },
  scoreMax: {
    fontSize: 20,
    color: '#94a3b8',
  },
  safetyLabel: {
    fontSize: 20,
    fontWeight: '600',
  },
  // T221: Simple guidance badge
  guidanceBadge: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
  },
  guidanceText: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  reasoningCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  reasoningTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  reasoningText: {
    fontSize: 15,
    color: '#334155',
    lineHeight: 24,
  },
  warningsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  // T222: Details toggle styles
  detailsToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailsToggleText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3b82f6',
  },
  detailsToggleCount: {
    fontSize: 14,
    color: '#64748b',
  },
  detailsContent: {
    marginTop: 16,
  },
  warningsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  warningItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  warningIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fef3c7',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  warningIconText: {
    fontSize: 20,
  },
  warningContent: {
    flex: 1,
  },
  warningTrigger: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  warningSeverity: {
    fontSize: 14,
    color: '#64748b',
    textTransform: 'capitalize',
  },
  actions: {
    marginTop: 12,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButton: {
    backgroundColor: '#3b82f6',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#f1f5f9',
  },
  secondaryButtonText: {
    color: '#475569',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  allergenAlert: {
    backgroundColor: '#dc2626',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 3,
    borderColor: '#991b1b',
  },
  allergenAlertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  allergenAlertIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  allergenAlertTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    letterSpacing: 1,
  },
  allergenAlertMessage: {
    fontSize: 17,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 24,
  },
  allergenAlertSubtext: {
    fontSize: 15,
    color: '#fecaca',
    textAlign: 'center',
    lineHeight: 22,
  },
  outcomeCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  outcomeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 8,
  },
  outcomeSubtext: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  outcomeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  ateButton: {
    flex: 1,
    backgroundColor: '#10b981',
    paddingVertical: 16,
  },
  ateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  avoidedButton: {
    flex: 1,
    backgroundColor: '#f1f5f9',
    paddingVertical: 16,
  },
  avoidedButtonText: {
    color: '#475569',
    fontSize: 16,
    fontWeight: '600',
  },
  // T223: Scan another button
  scanAnotherButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 14,
    marginTop: 12,
  },
  scanAnotherButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600',
  },
  outcomeStatusCard: {
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#86efac',
  },
  outcomeStatusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#15803d',
    textAlign: 'center',
  },
  disclaimerCard: {
    backgroundColor: '#eff6ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#bfdbfe',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  disclaimerIcon: {
    fontSize: 20,
    marginRight: 12,
    marginTop: 2,
  },
  disclaimerText: {
    flex: 1,
    fontSize: 13,
    color: '#1e40af',
    lineHeight: 18,
  },
  nonFoodCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 32,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nonFoodIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  nonFoodTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 16,
    textAlign: 'center',
  },
  nonFoodMessage: {
    fontSize: 16,
    color: '#334155',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 24,
  },
  nonFoodSubtext: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
});
