import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import DateTimePicker from '@react-native-community/datetimepicker';
import Slider from '@react-native-community/slider';
import BodyDiagram, { PainLocation } from '@/components/BodyDiagram';
import attackService, { AttackData } from '@/services/attack-service';
import { ScanResponse } from '@/services/scan-service';

const COMMON_SYMPTOMS = [
  { id: 'nausea', label: 'Nausea', icon: '🤢' },
  { id: 'vomiting', label: 'Vomiting', icon: '🤮' },
  { id: 'fever', label: 'Fever', icon: '🌡️' },
  { id: 'chills', label: 'Chills', icon: '🥶' },
  { id: 'sweating', label: 'Sweating', icon: '💦' },
  { id: 'jaundice', label: 'Yellowing skin/eyes', icon: '💛' },
  { id: 'back_pain', label: 'Back pain', icon: '🔙' },
  { id: 'bloating', label: 'Bloating', icon: '🎈' },
  { id: 'indigestion', label: 'Indigestion', icon: '😖' },
  { id: 'dark_urine', label: 'Dark urine', icon: '🟫' },
  { id: 'light_stools', label: 'Light-colored stools', icon: '⚪' },
  { id: 'shortness_breath', label: 'Shortness of breath', icon: '😮‍💨' },
];

const MEDICAL_CARE_OPTIONS = [
  { value: 'none', label: 'No medical care' },
  { value: 'home_treatment', label: 'Treated at home' },
  { value: 'doctor_call', label: 'Called doctor' },
  { value: 'primary_care', label: 'Primary care visit' },
  { value: 'urgent_care', label: 'Urgent care visit' },
  { value: 'emergency_room', label: 'Emergency room visit' },
  { value: 'hospital_admission', label: 'Hospital admission' },
  { value: 'hospitalization', label: 'Hospitalization' },
];

export default function LogAttackScreen() {
  const router = useRouter();

  // Attack data
  const [onsetDate, setOnsetDate] = useState<Date>(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [painIntensity, setPainIntensity] = useState<number>(5);
  const [durationMinutes, setDurationMinutes] = useState<string>('');
  const [painLocation, setPainLocation] = useState<PainLocation | null>(null);
  const [selectedSymptoms, setSelectedSymptoms] = useState<string[]>([]);
  const [medicalCareType, setMedicalCareType] = useState<string>('none');
  const [diagnosisReceived, setDiagnosisReceived] = useState<string>('');
  const [treatmentReceived, setTreatmentReceived] = useState<string>('');
  const [notes, setNotes] = useState<string>('');

  // UI state
  const [loading, setLoading] = useState(false);
  const [loadingScans, setLoadingScans] = useState(true);
  const [recentScans, setRecentScans] = useState<ScanResponse[]>([]);

  // Load recent scans based on attack onset time (T114)
  useEffect(() => {
    loadRecentScans();
  }, [onsetDate]);

  const loadRecentScans = async () => {
    setLoadingScans(true);
    try {
      // Calculate 8 hours before the attack onset time
      const hoursBeforeAttack = 8;
      const scansSinceDate = new Date(onsetDate);
      scansSinceDate.setHours(scansSinceDate.getHours() - hoursBeforeAttack);

      // Fetch scans from 8 hours before the attack onset
      const response = await attackService.getRecentScans(8);

      // Filter scans that are actually before the attack and within the 8-hour window
      const relevantScans = response.filter((scan) => {
        const scanDate = new Date(scan.created_at);
        return scanDate >= scansSinceDate && scanDate <= onsetDate;
      });

      setRecentScans(relevantScans);
    } catch (error) {
      console.error('Failed to load recent scans:', error);
      // Non-blocking error - continue without recent scans
    } finally {
      setLoadingScans(false);
    }
  };

  const toggleSymptom = (symptomId: string) => {
    if (selectedSymptoms.includes(symptomId)) {
      setSelectedSymptoms(selectedSymptoms.filter((s) => s !== symptomId));
    } else {
      setSelectedSymptoms([...selectedSymptoms, symptomId]);
    }
  };

  const handleSubmit = async () => {
    // Validation
    if (onsetDate > new Date()) {
      Alert.alert('Invalid Date', 'Attack onset time cannot be in the future.');
      return;
    }

    setLoading(true);

    try {
      const attackData: AttackData = {
        onset_at: onsetDate.toISOString(),
        pain_intensity: painIntensity,
        duration_minutes: durationMinutes ? parseInt(durationMinutes, 10) : undefined,
        pain_location: painLocation || undefined,
        symptoms: selectedSymptoms.length > 0 ? selectedSymptoms : undefined,
        medical_care_type: medicalCareType !== 'none' ? (medicalCareType as any) : undefined,
        diagnosis_received: diagnosisReceived.trim() || undefined,
        treatment_received: treatmentReceived.trim() || undefined,
        notes: notes.trim() || undefined,
      };

      await attackService.logAttack(attackData);

      Alert.alert(
        'Attack Logged',
        "We're analyzing your scan history to identify potential gallstone triggers. You'll see pattern suggestions soon.",
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Failed to log attack:', error);
      Alert.alert('Error', error?.message || 'Failed to log attack. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Log Gallstone Attack</Text>
        <Text style={styles.subtitle}>
          Recording your attacks helps us identify which foods trigger YOUR gallstone symptoms
        </Text>
      </View>

      {/* When did it start? */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>When did the attack start?</Text>
        <View style={styles.dateTimeRow}>
          <TouchableOpacity
            style={[styles.dateTimeButton, { flex: 1, marginRight: 8 }]}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateTimeLabel}>Date</Text>
            <Text style={styles.dateTimeValue}>{formatDate(onsetDate)}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.dateTimeButton, { flex: 1 }]}
            onPress={() => setShowTimePicker(true)}
          >
            <Text style={styles.dateTimeLabel}>Time</Text>
            <Text style={styles.dateTimeValue}>{formatTime(onsetDate)}</Text>
          </TouchableOpacity>
        </View>

        {showDatePicker && (
          <DateTimePicker
            value={onsetDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, date) => {
              setShowDatePicker(Platform.OS === 'ios');
              if (date) setOnsetDate(date);
            }}
            maximumDate={new Date()}
          />
        )}

        {showTimePicker && (
          <DateTimePicker
            value={onsetDate}
            mode="time"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, date) => {
              setShowTimePicker(Platform.OS === 'ios');
              if (date) setOnsetDate(date);
            }}
          />
        )}
      </View>

      {/* Pain intensity */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Pain Intensity</Text>
        <Text style={styles.sectionSubtitle}>1 = Mild discomfort, 10 = Severe pain</Text>
        <View style={styles.sliderContainer}>
          <Text style={styles.painValue}>{painIntensity}</Text>
          <Slider
            style={styles.slider}
            minimumValue={1}
            maximumValue={10}
            step={1}
            value={painIntensity}
            onValueChange={setPainIntensity}
            minimumTrackTintColor="#3b82f6"
            maximumTrackTintColor="#cbd5e1"
            thumbTintColor="#3b82f6"
          />
          <View style={styles.sliderLabels}>
            <Text style={styles.sliderLabel}>Mild</Text>
            <Text style={styles.sliderLabel}>Severe</Text>
          </View>
        </View>
      </View>

      {/* Duration */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Duration (optional)</Text>
        <TextInput
          style={styles.textInput}
          placeholder="How many minutes did it last?"
          keyboardType="numeric"
          value={durationMinutes}
          onChangeText={setDurationMinutes}
          placeholderTextColor="#94a3b8"
        />
      </View>

      {/* Pain location (T113) */}
      <View style={styles.section}>
        <BodyDiagram onLocationSelect={setPainLocation} selectedLocation={painLocation} />
      </View>

      {/* Symptoms */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Symptoms</Text>
        <Text style={styles.sectionSubtitle}>Select all that apply</Text>
        <View style={styles.symptomGrid}>
          {COMMON_SYMPTOMS.map((symptom) => (
            <TouchableOpacity
              key={symptom.id}
              style={[
                styles.symptomChip,
                selectedSymptoms.includes(symptom.id) && styles.symptomChipSelected,
              ]}
              onPress={() => toggleSymptom(symptom.id)}
            >
              <Text style={styles.symptomIcon}>{symptom.icon}</Text>
              <Text
                style={[
                  styles.symptomChipText,
                  selectedSymptoms.includes(symptom.id) && styles.symptomChipTextSelected,
                ]}
              >
                {symptom.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Medical care */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Medical Care</Text>
        <Text style={styles.sectionSubtitle}>Did you seek medical attention?</Text>
        {MEDICAL_CARE_OPTIONS.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.radioOption,
              medicalCareType === option.value && styles.radioOptionSelected,
            ]}
            onPress={() => setMedicalCareType(option.value)}
          >
            <View style={[styles.radio, medicalCareType === option.value && styles.radioSelected]}>
              {medicalCareType === option.value && <View style={styles.radioInner} />}
            </View>
            <Text style={styles.radioLabel}>{option.label}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Diagnosis and treatment */}
      {medicalCareType !== 'none' && (
        <>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Diagnosis Received (optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              placeholder="What did the doctor say?"
              multiline
              numberOfLines={3}
              value={diagnosisReceived}
              onChangeText={setDiagnosisReceived}
              placeholderTextColor="#94a3b8"
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Treatment Received (optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              placeholder="What treatment did you receive?"
              multiline
              numberOfLines={3}
              value={treatmentReceived}
              onChangeText={setTreatmentReceived}
              placeholderTextColor="#94a3b8"
            />
          </View>
        </>
      )}

      {/* Additional notes */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Additional Notes (optional)</Text>
        <TextInput
          style={[styles.textInput, styles.textArea]}
          placeholder="Any other details you'd like to record?"
          multiline
          numberOfLines={4}
          value={notes}
          onChangeText={setNotes}
          placeholderTextColor="#94a3b8"
        />
      </View>

      {/* Recent scans (T114) */}
      {loadingScans ? (
        <View style={styles.section}>
          <ActivityIndicator size="small" color="#3b82f6" />
          <Text style={styles.loadingText}>Loading meals before attack...</Text>
        </View>
      ) : recentScans.length > 0 ? (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Meals Before Attack (8 hours prior)</Text>
          <Text style={styles.sectionSubtitle}>
            These meals were logged within 8 hours before your attack and will be analyzed for
            gallstone trigger patterns
          </Text>
          {recentScans.map((scan) => (
            <View key={scan.id} style={styles.recentScanCard}>
              <Text style={styles.recentScanName}>{scan.meal_name || 'Unnamed meal'}</Text>
              <Text style={styles.recentScanTime}>
                {new Date(scan.created_at).toLocaleString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  hour: 'numeric',
                  minute: '2-digit',
                })}
              </Text>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Meals Before Attack</Text>
          <Text style={styles.sectionSubtitle}>
            No scans found within 8 hours before this attack. Logging your meals helps us identify
            gallstone trigger patterns.
          </Text>
        </View>
      )}

      {/* Submit button */}
      <TouchableOpacity
        style={[styles.submitButton, loading && styles.submitButtonDisabled]}
        onPress={handleSubmit}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.submitButtonText}>Log Attack</Text>
        )}
      </TouchableOpacity>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Your attack data helps us identify patterns and learn which foods trigger YOUR gallstone
          symptoms
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 12,
  },
  dateTimeRow: {
    flexDirection: 'row',
  },
  dateTimeButton: {
    backgroundColor: '#f1f5f9',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#cbd5e1',
  },
  dateTimeLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  dateTimeValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  sliderContainer: {
    paddingVertical: 16,
  },
  painValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#3b82f6',
    textAlign: 'center',
    marginBottom: 8,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  sliderLabel: {
    fontSize: 12,
    color: '#64748b',
  },
  textInput: {
    backgroundColor: '#f1f5f9',
    borderWidth: 1,
    borderColor: '#cbd5e1',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1a1a1a',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  symptomGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  symptomChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f5f9',
    borderWidth: 1,
    borderColor: '#cbd5e1',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    margin: 4,
    gap: 6,
  },
  symptomChipSelected: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  symptomIcon: {
    fontSize: 16,
  },
  symptomChipText: {
    fontSize: 14,
    color: '#1a1a1a',
  },
  symptomChipTextSelected: {
    color: '#fff',
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f8fafc',
  },
  radioOptionSelected: {
    backgroundColor: '#eff6ff',
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#cbd5e1',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: '#3b82f6',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#3b82f6',
  },
  radioLabel: {
    fontSize: 16,
    color: '#1a1a1a',
  },
  loadingText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 8,
  },
  recentScanCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  recentScanName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    flex: 1,
  },
  recentScanTime: {
    fontSize: 14,
    color: '#64748b',
  },
  submitButton: {
    backgroundColor: '#3b82f6',
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    paddingTop: 0,
  },
  footerText: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 18,
  },
});
