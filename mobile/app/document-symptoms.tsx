import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Platform,
  Share,
  KeyboardAvoidingView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import DateTimePicker from '@react-native-community/datetimepicker';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import BodyDiagram, { PainLocation } from '@/components/BodyDiagram';
import attackService from '@/services/attack-service';
import { ScanResponse } from '@/services/scan-service';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '@/constants/Design';
import Button from '@/components/Button';
import Card from '@/components/Card';

/**
 * Symptom Documentation Screen (FR-060 to FR-062)
 *
 * PURPOSE: Document symptoms DURING an attack for sharing with medical staff.
 * Different from attack logging (which is done AFTER the attack for pattern detection).
 *
 * This creates a shareable report with:
 * - Current symptoms and pain level
 * - Recent meals (last 8 hours) from scan history
 * - Timing information
 *
 * Can be shared via text, email, or AirDrop to medical staff at ER.
 */

const COMMON_SYMPTOMS = [
  { id: 'nausea', label: 'Nausea', icon: '🤢' },
  { id: 'vomiting', label: 'Vomiting', icon: '🤮' },
  { id: 'fever', label: 'Fever', icon: '🌡️' },
  { id: 'chills', label: 'Chills', icon: '🥶' },
  { id: 'sweating', label: 'Sweating', icon: '💦' },
  { id: 'jaundice', label: 'Yellowing skin/eyes', icon: '💛' },
  { id: 'back_pain', label: 'Back pain', icon: '🔙' },
  { id: 'bloating', label: 'Bloating', icon: '🎈' },
  { id: 'indigestion', label: 'Indigestion', icon: '😖' },
  { id: 'dark_urine', label: 'Dark urine', icon: '🟫' },
  { id: 'light_stools', label: 'Light-colored stools', icon: '⚪' },
  { id: 'shortness_breath', label: 'Shortness of breath', icon: '😮‍💨' },
];

export default function DocumentSymptomsScreen() {
  const router = useRouter();

  // Symptom data
  const [painStartTime, setPainStartTime] = useState<Date>(new Date());
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [painIntensity, setPainIntensity] = useState<number>(7);
  const [painLocation, setPainLocation] = useState<PainLocation | null>(null);
  const [selectedSymptoms, setSelectedSymptoms] = useState<Set<string>>(new Set());
  const [additionalNotes, setAdditionalNotes] = useState<string>('');

  // Recent scans (T198 - Auto-pull last 8 hours)
  const [loadingScans, setLoadingScans] = useState(true);
  const [recentScans, setRecentScans] = useState<ScanResponse[]>([]);

  // UI state
  const [generatingReport, setGeneratingReport] = useState(false);

  // Load recent scans (last 8 hours) - T198
  useEffect(() => {
    loadRecentScans();
  }, [painStartTime]);

  const loadRecentScans = async () => {
    setLoadingScans(true);
    try {
      const hoursBeforeAttack = 8;
      const response = await attackService.getRecentScans(hoursBeforeAttack);

      // Filter scans within 8 hours before pain start
      const scansSinceDate = new Date(painStartTime);
      scansSinceDate.setHours(scansSinceDate.getHours() - hoursBeforeAttack);

      const relevantScans = response.filter((scan) => {
        const scanDate = new Date(scan.created_at);
        return scanDate >= scansSinceDate && scanDate <= painStartTime;
      });

      setRecentScans(relevantScans);
    } catch (error) {
      console.error('Failed to load recent scans:', error);
      // Non-blocking error
    } finally {
      setLoadingScans(false);
    }
  };

  const toggleSymptom = (symptomId: string) => {
    const newSymptoms = new Set(selectedSymptoms);
    if (newSymptoms.has(symptomId)) {
      newSymptoms.delete(symptomId);
    } else {
      newSymptoms.add(symptomId);
    }
    setSelectedSymptoms(newSymptoms);
  };

  // Generate shareable report - T199
  const generateSymptomReport = (): string => {
    const formatTime = (date: Date) => {
      return date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    };

    const formatHoursAgo = (scanTime: Date) => {
      const diffMs = painStartTime.getTime() - new Date(scanTime).getTime();
      const hours = Math.floor(diffMs / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      if (hours > 0) {
        return `${hours}h ${minutes}m before pain started`;
      }
      return `${minutes}m before pain started`;
    };

    let report = `GALLSTONE ATTACK SYMPTOM REPORT\n`;
    report += `Generated: ${formatTime(new Date())}\n`;
    report += `\n========================================\n\n`;

    // Pain Information
    report += `PAIN INFORMATION\n`;
    report += `Started: ${formatTime(painStartTime)}\n`;
    report += `Pain Intensity: ${painIntensity}/10\n`;
    if (painLocation) {
      report += `Pain Location: ${painLocation.region || 'Upper abdomen'}\n`;
    }
    report += `\n`;

    // Symptoms
    report += `SYMPTOMS\n`;
    if (selectedSymptoms.size > 0) {
      const symptomLabels = COMMON_SYMPTOMS
        .filter((s) => selectedSymptoms.has(s.id))
        .map((s) => `- ${s.label}`)
        .join('\n');
      report += `${symptomLabels}\n`;
    } else {
      report += `No additional symptoms selected\n`;
    }
    report += `\n`;

    // Additional Notes
    if (additionalNotes.trim()) {
      report += `ADDITIONAL NOTES\n`;
      report += `${additionalNotes}\n`;
      report += `\n`;
    }

    // Recent Meals (T199 - include scan history)
    report += `RECENT MEALS (Last 8 hours)\n`;
    if (recentScans.length > 0) {
      recentScans.forEach((scan, index) => {
        report += `${index + 1}. ${scan.food_name || 'Unknown meal'}\n`;
        report += `   Eaten: ${formatHoursAgo(scan.created_at)}\n`;
        if (scan.safety_score !== null && scan.safety_score !== undefined) {
          report += `   Safety Score: ${scan.safety_score}/100\n`;
        }
        if (scan.trigger_warnings && scan.trigger_warnings.length > 0) {
          report += `   ⚠️ Triggers: ${scan.trigger_warnings.join(', ')}\n`;
        }
        report += `\n`;
      });
    } else if (loadingScans) {
      report += `(Loading recent meals...)\n`;
    } else {
      report += `No recent meals recorded in app\n`;
    }
    report += `\n`;

    // Medical Disclaimer
    report += `========================================\n`;
    report += `\nThis report was generated by GallDiet app\n`;
    report += `and is intended to help communicate with\n`;
    report += `medical professionals. It is not a\n`;
    report += `substitute for medical examination.\n`;

    return report;
  };

  // Share symptom report - T200
  const handleShareReport = async () => {
    setGeneratingReport(true);

    try {
      const report = generateSymptomReport();

      const result = await Share.share({
        message: report,
        title: 'Gallstone Attack Symptom Report',
      });

      if (result.action === Share.sharedAction) {
        Alert.alert(
          'Report Shared',
          'Your symptom report has been shared successfully. Please seek medical attention if you haven\'t already.',
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error sharing report:', error);
      Alert.alert('Error', 'Failed to share symptom report. Please try again.');
    } finally {
      setGeneratingReport(false);
    }
  };

  const handlePreviewReport = () => {
    const report = generateSymptomReport();
    Alert.alert('Symptom Report Preview', report, [
      { text: 'Close', style: 'cancel' },
      { text: 'Share', onPress: handleShareReport },
    ]);
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['bottom']}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Header Info */}
      <Card style={styles.headerCard}>
        <Text style={styles.headerTitle}>Document Your Symptoms</Text>
        <Text style={styles.headerDescription}>
          Create a shareable report for your doctor or ER staff. This helps medical professionals
          understand your symptoms and recent diet.
        </Text>
      </Card>

      {/* Pain Start Time */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>When did the pain start?</Text>
        <TouchableOpacity
          style={styles.timeButton}
          onPress={() => setShowTimePicker(true)}
          activeOpacity={0.7}
        >
          <Ionicons name="time-outline" size={24} color={Colors.primary[500]} />
          <Text style={styles.timeButtonText}>
            {painStartTime.toLocaleString('en-US', {
              month: 'short',
              day: 'numeric',
              hour: 'numeric',
              minute: '2-digit',
              hour12: true,
            })}
          </Text>
          <Ionicons name="chevron-forward" size={20} color={Colors.gray[400]} />
        </TouchableOpacity>

        {showTimePicker && (
          <DateTimePicker
            value={painStartTime}
            mode="datetime"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, selectedDate) => {
              setShowTimePicker(Platform.OS === 'ios');
              if (selectedDate) {
                setPainStartTime(selectedDate);
              }
            }}
            maximumDate={new Date()}
          />
        )}
      </Card>

      {/* Pain Intensity */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Pain Intensity</Text>
        <View style={styles.intensityContainer}>
          <Text style={styles.intensityValue}>{painIntensity}/10</Text>
          <Slider
            style={styles.slider}
            minimumValue={1}
            maximumValue={10}
            step={1}
            value={painIntensity}
            onValueChange={setPainIntensity}
            minimumTrackTintColor={Colors.error}
            maximumTrackTintColor={Colors.gray[300]}
            thumbTintColor={Colors.error}
          />
          <View style={styles.intensityLabels}>
            <Text style={styles.intensityLabel}>Mild</Text>
            <Text style={styles.intensityLabel}>Moderate</Text>
            <Text style={styles.intensityLabel}>Severe</Text>
          </View>
        </View>
      </Card>

      {/* Pain Location - T197 (Body Diagram already exists) */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Pain Location</Text>
        <Text style={styles.sectionDescription}>Tap on the body diagram to mark pain location</Text>
        <BodyDiagram onLocationSelect={setPainLocation} selectedLocation={painLocation} />
        {painLocation && (
          <Text style={styles.locationText}>
            Location: {painLocation.region || 'Marked'}
          </Text>
        )}
      </Card>

      {/* Symptoms Checklist */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Symptoms</Text>
        <Text style={styles.sectionDescription}>Select all symptoms you're experiencing</Text>
        <View style={styles.symptomsGrid}>
          {COMMON_SYMPTOMS.map((symptom) => (
            <TouchableOpacity
              key={symptom.id}
              style={[
                styles.symptomChip,
                selectedSymptoms.has(symptom.id) && styles.symptomChipSelected,
              ]}
              onPress={() => toggleSymptom(symptom.id)}
              activeOpacity={0.7}
            >
              <Text style={styles.symptomIcon}>{symptom.icon}</Text>
              <Text
                style={[
                  styles.symptomLabel,
                  selectedSymptoms.has(symptom.id) && styles.symptomLabelSelected,
                ]}
              >
                {symptom.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </Card>

      {/* Additional Notes */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Additional Notes</Text>
        <TextInput
          style={styles.notesInput}
          placeholder="Any other information for your doctor..."
          placeholderTextColor={Colors.text.disabled}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
          value={additionalNotes}
          onChangeText={setAdditionalNotes}
        />
      </Card>

      {/* Recent Meals Summary - T198 */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Recent Meals (Last 8 Hours)</Text>
        {loadingScans ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={Colors.primary[500]} />
            <Text style={styles.loadingText}>Loading recent meals...</Text>
          </View>
        ) : recentScans.length > 0 ? (
          <View>
            {recentScans.map((scan, index) => (
              <View key={scan.id} style={styles.mealItem}>
                <View style={styles.mealHeader}>
                  <Text style={styles.mealName}>{scan.food_name || 'Unknown meal'}</Text>
                  {scan.safety_score !== null && scan.safety_score !== undefined && (
                    <View
                      style={[
                        styles.scoreBadge,
                        {
                          backgroundColor:
                            scan.safety_score >= 80
                              ? Colors.safety.safe
                              : scan.safety_score >= 50
                              ? Colors.safety.caution
                              : Colors.safety.danger,
                        },
                      ]}
                    >
                      <Text style={styles.scoreBadgeText}>{scan.safety_score}</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.mealTime}>
                  {new Date(scan.created_at).toLocaleString()}
                </Text>
                {scan.trigger_warnings && scan.trigger_warnings.length > 0 && (
                  <Text style={styles.triggerWarning}>
                    ⚠️ {scan.trigger_warnings.join(', ')}
                  </Text>
                )}
              </View>
            ))}
          </View>
        ) : (
          <Text style={styles.emptyText}>No recent meals recorded in the app</Text>
        )}
      </Card>

      {/* Action Buttons */}
      <View style={styles.actions}>
        <Button
          variant="secondary"
          size="large"
          fullWidth
          onPress={handlePreviewReport}
          style={styles.actionButton}
        >
          Preview Report
        </Button>
        <Button
          variant="primary"
          size="large"
          fullWidth
          onPress={handleShareReport}
          loading={generatingReport}
          style={styles.actionButton}
        >
          Share with Medical Staff
        </Button>
      </View>

      {/* Reminder */}
      <View style={styles.reminder}>
        <Ionicons name="warning-outline" size={20} color={Colors.warning} />
        <Text style={styles.reminderText}>
          This report is for sharing with medical professionals. If you are experiencing severe
          symptoms, please go to the ER or call 911 immediately.
        </Text>
      </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  keyboardAvoid: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  contentContainer: {
    padding: Spacing.base,
    paddingBottom: 100, // Large bottom padding to ensure buttons are visible
  },
  headerCard: {
    marginBottom: Spacing.xl,
    backgroundColor: Colors.primary[50],
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  headerDescription: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  section: {
    marginBottom: Spacing.base,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  sectionDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.base,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  timeButtonText: {
    flex: 1,
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    marginLeft: Spacing.md,
  },
  intensityContainer: {
    paddingVertical: Spacing.md,
  },
  intensityValue: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.error,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  intensityLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.sm,
  },
  intensityLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  locationText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    marginTop: Spacing.md,
    fontWeight: Typography.fontWeight.medium,
  },
  symptomsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  symptomChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.full,
    borderWidth: 1,
    borderColor: Colors.border.light,
    gap: Spacing.xs,
  },
  symptomChipSelected: {
    backgroundColor: Colors.primary[100],
    borderColor: Colors.primary[500],
  },
  symptomIcon: {
    fontSize: 16,
  },
  symptomLabel: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  symptomLabelSelected: {
    color: Colors.primary[700],
    fontWeight: Typography.fontWeight.medium,
  },
  notesInput: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border.light,
    padding: Spacing.md,
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    minHeight: 100,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    paddingVertical: Spacing.lg,
  },
  loadingText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
  },
  mealItem: {
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  mealName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.primary,
    flex: 1,
  },
  scoreBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  scoreBadgeText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
  },
  mealTime: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  triggerWarning: {
    fontSize: Typography.fontSize.sm,
    color: Colors.error,
  },
  emptyText: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    fontStyle: 'italic',
    paddingVertical: Spacing.lg,
    textAlign: 'center',
  },
  actions: {
    marginTop: Spacing.xl,
    marginBottom: Spacing['2xl'],
    gap: Spacing.md,
  },
  actionButton: {
    marginBottom: 0,
  },
  reminder: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
    padding: Spacing.md,
    backgroundColor: Colors.warning + '20',
    borderLeftWidth: 4,
    borderLeftColor: Colors.warning,
    borderRadius: BorderRadius.sm,
    marginTop: Spacing.lg,
    marginBottom: Spacing['3xl'],
  },
  reminderText: {
    flex: 1,
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.sm,
  },
});
