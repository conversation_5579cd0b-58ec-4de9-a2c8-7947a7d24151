import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useOnboarding } from '@/contexts';
import ProgressBar from '@/components/ProgressBar';
import TriggerAutocomplete from '@/components/TriggerAutocomplete';

export default function TriggersScreen() {
  const router = useRouter();
  const { data: onboardingData, updateTriggers } = useOnboarding();
  const [triggers, setTriggers] = useState<{ name: string; severity: 'low' | 'moderate' | 'high' }[]>(onboardingData.triggers);
  const [newTriggerName, setNewTriggerName] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<'low' | 'moderate' | 'high'>('moderate');

  const addTrigger = () => {
    if (!newTriggerName.trim()) {
      Alert.alert('Invalid Input', 'Please enter a trigger name.');
      return;
    }

    setTriggers([...triggers, { name: newTriggerName.trim(), severity: selectedSeverity }]);
    setNewTriggerName('');
    setSelectedSeverity('moderate');
  };

  const removeTrigger = (index: number) => {
    setTriggers(triggers.filter((_, i) => i !== index));
  };

  const handleContinue = () => {
    // Save triggers to context for persistence
    updateTriggers(triggers);

    // Save triggers to backend here (TODO: API call)
    // For now, just navigate
    router.push('/(onboarding)/allergens');
  };

  const handleSkip = () => {
    // Save empty triggers to context
    updateTriggers([]);
    router.push('/(onboarding)/allergens');
  };

  const handleBack = () => {
    // Save current triggers before going back
    updateTriggers(triggers);
    router.back();
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <ProgressBar
        currentStep={2}
        totalSteps={3}
        labels={['Basic Info', 'Triggers', 'Complete']}
      />

      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <Text style={styles.title}>Known Triggers</Text>
      <Text style={styles.subtitle}>
        Add any foods you know trigger your gallstone attacks
      </Text>

      {/* "I don't know yet" option */}
      <TouchableOpacity style={styles.skipBox} onPress={handleSkip}>
        <Text style={styles.skipTitle}>I don&apos;t know my triggers yet</Text>
        <Text style={styles.skipText}>
          That&apos;s okay! GallDiet will help you discover them through pattern detection.
        </Text>
      </TouchableOpacity>

      {/* Add Trigger Form */}
      <View style={styles.addSection}>
        <Text style={styles.label}>Add a trigger</Text>
        <TriggerAutocomplete
          value={newTriggerName}
          onChangeText={setNewTriggerName}
          onSelect={(trigger) => {
            setNewTriggerName(trigger);
          }}
        />

        <Text style={styles.label}>Severity</Text>
        <View style={styles.severityGroup}>
          <TouchableOpacity
            style={[
              styles.severityButton,
              selectedSeverity === 'low' && styles.severityButtonSelected,
            ]}
            onPress={() => setSelectedSeverity('low')}
          >
            <Text
              style={[
                styles.severityText,
                selectedSeverity === 'low' && styles.severityTextSelected,
              ]}
            >
              Mild
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.severityButton,
              selectedSeverity === 'moderate' && styles.severityButtonSelected,
            ]}
            onPress={() => setSelectedSeverity('moderate')}
          >
            <Text
              style={[
                styles.severityText,
                selectedSeverity === 'moderate' && styles.severityTextSelected,
              ]}
            >
              Moderate
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.severityButton,
              selectedSeverity === 'high' && styles.severityButtonSelected,
            ]}
            onPress={() => setSelectedSeverity('high')}
          >
            <Text
              style={[
                styles.severityText,
                selectedSeverity === 'high' && styles.severityTextSelected,
              ]}
            >
              Severe
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.addButton} onPress={addTrigger}>
          <Text style={styles.addButtonText}>+ Add Trigger</Text>
        </TouchableOpacity>
      </View>

      {/* Triggers List */}
      {triggers.length > 0 && (
        <View style={styles.listSection}>
          <Text style={styles.listTitle}>Your Triggers ({triggers.length})</Text>
          {triggers.map((trigger, index) => (
            <View key={index} style={styles.triggerCard}>
              <View style={styles.triggerInfo}>
                <Text style={styles.triggerName}>{trigger.name}</Text>
                <Text style={styles.triggerSeverity}>{trigger.severity}</Text>
              </View>
              <TouchableOpacity onPress={() => removeTrigger(index)}>
                <Text style={styles.removeButton}>Remove</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}

      <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
        <Text style={styles.continueButtonText}>
          {triggers.length > 0 ? 'Continue' : 'Skip for Now'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 24,
    paddingTop: 60,
  },
  backButton: {
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  backButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '600',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  skipBox: {
    backgroundColor: '#f0f9ff',
    borderWidth: 2,
    borderColor: '#3b82f6',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  skipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3b82f6',
    marginBottom: 8,
  },
  skipText: {
    fontSize: 14,
    color: '#64748b',
  },
  addSection: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
    color: '#333',
  },
  severityGroup: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  severityButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    alignItems: 'center',
  },
  severityButtonSelected: {
    borderColor: '#3b82f6',
    backgroundColor: '#eff6ff',
  },
  severityText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  severityTextSelected: {
    color: '#3b82f6',
  },
  addButton: {
    backgroundColor: '#f1f5f9',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#cbd5e1',
  },
  addButtonText: {
    color: '#3b82f6',
    fontSize: 16,
    fontWeight: '600',
  },
  listSection: {
    marginBottom: 24,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  triggerCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  triggerInfo: {
    flex: 1,
  },
  triggerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  triggerSeverity: {
    fontSize: 14,
    color: '#64748b',
    textTransform: 'capitalize',
  },
  removeButton: {
    color: '#ef4444',
    fontSize: 14,
    fontWeight: '600',
  },
  continueButton: {
    backgroundColor: '#3b82f6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
