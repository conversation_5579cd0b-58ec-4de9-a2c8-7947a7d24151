import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useOnboarding } from '@/contexts';
import ProgressBar from '@/components/ProgressBar';

const COMMON_ALLERGENS = [
  'Peanuts',
  'Tree Nuts',
  'Milk',
  'Eggs',
  'Wheat',
  'Soy',
  'Fish',
  'Shellfish',
];

export default function AllergensScreen() {
  const router = useRouter();
  const { data: onboardingData, updateAllergens } = useOnboarding();
  const [allergens, setAllergens] = useState<string[]>(onboardingData.allergens);
  const [customAllergen, setCustomAllergen] = useState('');

  const toggleAllergen = (allergen: string) => {
    if (allergens.includes(allergen)) {
      setAllergens(allergens.filter((a) => a !== allergen));
    } else {
      setAllergens([...allergens, allergen]);
    }
  };

  const addCustomAllergen = () => {
    if (!customAllergen.trim()) {
      return;
    }

    if (allergens.includes(customAllergen.trim())) {
      Alert.alert('Already Added', 'This allergen is already in your list.');
      return;
    }

    setAllergens([...allergens, customAllergen.trim()]);
    setCustomAllergen('');
  };

  const handleContinue = () => {
    // Save allergens to context for persistence
    updateAllergens(allergens);

    // Save allergens to backend here (TODO: API call)
    // For now, just navigate
    router.push('/(onboarding)/complete');
  };

  const handleBack = () => {
    // Save current allergens before going back
    updateAllergens(allergens);
    router.back();
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <ProgressBar
        currentStep={3}
        totalSteps={3}
        labels={['Basic Info', 'Triggers', 'Complete']}
      />

      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Text style={styles.backButtonText}>← Back</Text>
      </TouchableOpacity>

      <Text style={styles.title}>Food Allergies</Text>
      <Text style={styles.subtitle}>
        Let us know about any food allergies so we can flag them in scan results
      </Text>

      {/* Common Allergens */}
      <Text style={styles.sectionTitle}>Common Allergens</Text>
      <View style={styles.allergenGrid}>
        {COMMON_ALLERGENS.map((allergen) => (
          <TouchableOpacity
            key={allergen}
            style={[
              styles.allergenChip,
              allergens.includes(allergen) && styles.allergenChipSelected,
            ]}
            onPress={() => toggleAllergen(allergen)}
          >
            <Text
              style={[
                styles.allergenText,
                allergens.includes(allergen) && styles.allergenTextSelected,
              ]}
            >
              {allergen}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Custom Allergen Input */}
      <Text style={styles.sectionTitle}>Add Other Allergen</Text>
      <View style={styles.customSection}>
        <TextInput
          style={styles.input}
          placeholder="e.g., Sesame, Mustard"
          value={customAllergen}
          onChangeText={setCustomAllergen}
        />
        <TouchableOpacity style={styles.addButton} onPress={addCustomAllergen}>
          <Text style={styles.addButtonText}>+ Add</Text>
        </TouchableOpacity>
      </View>

      {/* Selected Allergens */}
      {allergens.length > 0 && (
        <View style={styles.selectedSection}>
          <Text style={styles.selectedTitle}>Your Allergens ({allergens.length})</Text>
          <View style={styles.selectedList}>
            {allergens.map((allergen) => (
              <View key={allergen} style={styles.selectedChip}>
                <Text style={styles.selectedText}>{allergen}</Text>
                <TouchableOpacity onPress={() => toggleAllergen(allergen)}>
                  <Text style={styles.removeText}>×</Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}

      <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
        <Text style={styles.continueButtonText}>
          {allergens.length > 0 ? 'Continue' : 'Skip for Now'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.skipButton} onPress={handleContinue}>
        <Text style={styles.skipButtonText}>I don&apos;t have allergies</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 24,
    paddingTop: 60,
  },
  backButton: {
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  backButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '600',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  allergenGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
  },
  allergenChip: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
  allergenChipSelected: {
    borderColor: '#ef4444',
    backgroundColor: '#fef2f2',
  },
  allergenText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  allergenTextSelected: {
    color: '#ef4444',
  },
  customSection: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 24,
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  addButton: {
    backgroundColor: '#f1f5f9',
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#cbd5e1',
  },
  addButtonText: {
    color: '#3b82f6',
    fontSize: 14,
    fontWeight: '600',
  },
  selectedSection: {
    marginBottom: 24,
  },
  selectedTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  selectedList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  selectedChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingLeft: 12,
    paddingRight: 8,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#ef4444',
  },
  selectedText: {
    fontSize: 14,
    color: '#ef4444',
    fontWeight: '500',
  },
  removeText: {
    fontSize: 20,
    color: '#ef4444',
    fontWeight: '600',
  },
  continueButton: {
    backgroundColor: '#3b82f6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    padding: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  skipButtonText: {
    color: '#64748b',
    fontSize: 14,
    fontWeight: '500',
  },
});
