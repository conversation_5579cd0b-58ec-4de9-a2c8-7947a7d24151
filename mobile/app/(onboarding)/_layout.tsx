import { Stack } from 'expo-router';
import { OnboardingProvider } from '@/contexts';

export default function OnboardingLayout() {
  return (
    <OnboardingProvider>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="profile-basic" />
        <Stack.Screen name="triggers" />
        <Stack.Screen name="allergens" />
        <Stack.Screen name="complete" />
      </Stack>
    </OnboardingProvider>
  );
}
