import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useProfile, useOnboarding } from '@/contexts';
import ProgressBar from '@/components/ProgressBar';

const ONBOARDING_START_TIME_KEY = '@galldiet_onboarding_start_time';

export default function ProfileBasicScreen() {
  const router = useRouter();
  const { updateProfile } = useProfile();
  const { data: onboardingData, updateProfileBasic } = useOnboarding();
  const [age, setAge] = useState(onboardingData.age);
  const [sex, setSex] = useState<'male' | 'female' | 'other' | null>(onboardingData.sex);
  const [hasGallbladder, setHasGallbladder] = useState<boolean | null>(onboardingData.hasGallbladder);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Track onboarding start time
  useEffect(() => {
    const trackStartTime = async () => {
      try {
        const existingTime = await AsyncStorage.getItem(ONBOARDING_START_TIME_KEY);
        if (!existingTime) {
          await AsyncStorage.setItem(ONBOARDING_START_TIME_KEY, Date.now().toString());
        }
      } catch (error) {
        console.error('Failed to track onboarding start time:', error);
      }
    };
    trackStartTime();
  }, []);

  const handleContinue = async () => {
    // Validation
    if (!age || !sex) {
      Alert.alert('Required Fields', 'Please complete all fields to continue.');
      return;
    }

    const ageNum = parseInt(age, 10);
    if (isNaN(ageNum) || ageNum < 18 || ageNum > 120) {
      Alert.alert('Invalid Age', 'Please enter a valid age between 18 and 120.');
      return;
    }

    setIsSubmitting(true);
    try {
      // Save to context for persistence during onboarding
      updateProfileBasic(age, sex, hasGallbladder);

      // Also update profile API
      await updateProfile({
        age: ageNum,
        sex,
      });

      router.push('/(onboarding)/triggers');
    } catch (error) {
      console.error('Profile update failed:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <ProgressBar
        currentStep={1}
        totalSteps={3}
        labels={['Basic Info', 'Triggers', 'Complete']}
      />

      <Text style={styles.title}>Let&apos;s Get Started</Text>
      <Text style={styles.subtitle}>Tell us a bit about yourself to personalize your experience</Text>

      {/* Age Input */}
      <View style={styles.section}>
        <Text style={styles.label}>Age</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your age"
          value={age}
          onChangeText={setAge}
          keyboardType="number-pad"
          editable={!isSubmitting}
        />
      </View>

      {/* Sex Selection */}
      <View style={styles.section}>
        <Text style={styles.label}>Sex</Text>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.optionButton, sex === 'male' && styles.optionButtonSelected]}
            onPress={() => setSex('male')}
            disabled={isSubmitting}
          >
            <Text style={[styles.optionText, sex === 'male' && styles.optionTextSelected]}>
              Male
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.optionButton, sex === 'female' && styles.optionButtonSelected]}
            onPress={() => setSex('female')}
            disabled={isSubmitting}
          >
            <Text style={[styles.optionText, sex === 'female' && styles.optionTextSelected]}>
              Female
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.optionButton, sex === 'other' && styles.optionButtonSelected]}
            onPress={() => setSex('other')}
            disabled={isSubmitting}
          >
            <Text style={[styles.optionText, sex === 'other' && styles.optionTextSelected]}>
              Other
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Gallbladder Status */}
      <View style={styles.section}>
        <Text style={styles.label}>Do you still have your gallbladder?</Text>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.optionButton, hasGallbladder === true && styles.optionButtonSelected]}
            onPress={() => setHasGallbladder(true)}
            disabled={isSubmitting}
          >
            <Text
              style={[styles.optionText, hasGallbladder === true && styles.optionTextSelected]}
            >
              Yes
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.optionButton, hasGallbladder === false && styles.optionButtonSelected]}
            onPress={() => setHasGallbladder(false)}
            disabled={isSubmitting}
          >
            <Text
              style={[styles.optionText, hasGallbladder === false && styles.optionTextSelected]}
            >
              No (Removed)
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.continueButton, isSubmitting && styles.continueButtonDisabled]}
        onPress={handleContinue}
        disabled={isSubmitting}
      >
        <Text style={styles.continueButtonText}>
          {isSubmitting ? 'Saving...' : 'Continue'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 24,
    paddingTop: 60,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 32,
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
  },
  optionButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  optionButtonSelected: {
    borderColor: '#3b82f6',
    backgroundColor: '#eff6ff',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  optionTextSelected: {
    color: '#3b82f6',
  },
  continueButton: {
    backgroundColor: '#3b82f6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  continueButtonDisabled: {
    opacity: 0.6,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
