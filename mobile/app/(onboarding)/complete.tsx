import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useProfile } from '@/contexts';

const ONBOARDING_START_TIME_KEY = '@galldiet_onboarding_start_time';

export default function CompleteScreen() {
  const router = useRouter();
  const { refreshProfile, updateProfile } = useProfile();
  const [elapsedTime, setElapsedTime] = useState<number | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    const calculateElapsedTime = async () => {
      try {
        const startTime = await AsyncStorage.getItem(ONBOARDING_START_TIME_KEY);
        if (startTime) {
          const elapsed = Date.now() - parseInt(startTime, 10);
          setElapsedTime(Math.floor(elapsed / 1000)); // Convert to seconds
        }
      } catch (error) {
        console.error('Failed to calculate elapsed time:', error);
      }
    };
    calculateElapsedTime();
  }, []);

  const handleGetStarted = async () => {
    console.log('[Onboarding] Get Started clicked');
    setIsNavigating(true);
    try {
      // Clear onboarding start time
      await AsyncStorage.removeItem(ONBOARDING_START_TIME_KEY);

      console.log('[Onboarding] Marking onboarding as complete...');
      // Mark onboarding as complete in the backend
      const result = await updateProfile({ onboarding_completed: true });
      console.log('[Onboarding] Onboarding marked complete, result:', result);

      console.log('[Onboarding] Refreshing profile...');
      // Refresh profile to ensure navigation logic sees the updated profile
      await refreshProfile();
      console.log('[Onboarding] Profile refreshed');

      // Wait a moment for state to propagate
      await new Promise(resolve => setTimeout(resolve, 100));

      console.log('[Onboarding] Navigating to tabs');
      // Navigate to main app
      router.replace('/(tabs)');
    } catch (error) {
      console.error('[Onboarding] Failed to complete onboarding:', error);
      setIsNavigating(false);
    }
  };

  const handleEditProfile = () => {
    // Navigate back to the first onboarding screen to review/edit
    router.push('/(onboarding)/profile-basic');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>✓</Text>
        </View>

        <Text style={styles.title}>You&apos;re All Set!</Text>

        <Text style={styles.subtitle}>
          Your profile is ready. GallDiet will now personalize every scan result based on YOUR
          triggers and preferences.
        </Text>

        <View style={styles.featureList}>
          <View style={styles.feature}>
            <Text style={styles.featureDot}>•</Text>
            <Text style={styles.featureText}>
              Scan meals to get instant safety scores
            </Text>
          </View>

          <View style={styles.feature}>
            <Text style={styles.featureDot}>•</Text>
            <Text style={styles.featureText}>
              Log attacks to discover new triggers
            </Text>
          </View>

          <View style={styles.feature}>
            <Text style={styles.featureDot}>•</Text>
            <Text style={styles.featureText}>
              Get AI-powered pattern detection
            </Text>
          </View>
        </View>

        <Text style={styles.disclaimer}>
          Remember: GallDiet provides dietary guidance only and is not a substitute for
          professional medical advice.
        </Text>

        <TouchableOpacity
          style={[styles.button, isNavigating && styles.buttonDisabled]}
          onPress={handleGetStarted}
          disabled={isNavigating}
        >
          {isNavigating ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Get Started</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
          <Text style={styles.editButtonText}>← Edit My Profile</Text>
        </TouchableOpacity>

        {/* Development: Show elapsed time */}
        {elapsedTime !== null && (
          <Text style={styles.devTimer}>
            Onboarding completed in {Math.floor(elapsedTime / 60)}m {elapsedTime % 60}s
            {elapsedTime < 180 ? ' ✓' : ' ⚠️ (> 3 min)'}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#10b981',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  icon: {
    fontSize: 40,
    color: '#fff',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#1a1a1a',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  featureList: {
    alignSelf: 'stretch',
    marginBottom: 32,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureDot: {
    fontSize: 24,
    color: '#3b82f6',
    marginRight: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  disclaimer: {
    fontSize: 12,
    color: '#94a3b8',
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
    lineHeight: 18,
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    paddingHorizontal: 48,
    borderRadius: 8,
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#94a3b8',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  editButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 16,
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  editButtonText: {
    color: '#3b82f6',
    fontSize: 16,
    fontWeight: '600',
  },
  devTimer: {
    marginTop: 16,
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    fontFamily: 'monospace',
  },
});
