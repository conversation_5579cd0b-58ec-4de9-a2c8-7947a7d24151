import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '@/constants/Design';
import Button from '@/components/Button';
import Card from '@/components/Card';

/**
 * Emergency Support Screen (FR-056 to FR-063)
 *
 * CRITICAL SAFETY REQUIREMENTS:
 * - ALWAYS display "SEEK MEDICAL CARE NOW" message (FR-057)
 * - NEVER discourage ER visits (FR-059)
 * - Breathing exercise marked "comfort only, not treatment" (FR-063)
 * - One-tap emergency actions (FR-058)
 *
 * This screen is NEVER gated behind subscription - emergency support is always free.
 */
export default function EmergencySupportScreen() {
  const router = useRouter();
  const [showBreathingExercise, setShowBreathingExercise] = useState(false);

  // Handle Call 911
  const handleCall911 = () => {
    const emergencyNumber = Platform.OS === 'ios' ? 'telprompt:911' : 'tel:911';

    Alert.alert(
      'Call Emergency Services',
      'This will call 911. Are you experiencing a medical emergency?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Call 911',
          onPress: () => Linking.openURL(emergencyNumber),
          style: 'destructive',
        },
      ]
    );
  };

  // Handle Find Nearest ER
  const handleFindER = () => {
    const query = Platform.select({
      ios: 'maps://app?q=emergency+room+near+me',
      android: 'geo:0,0?q=emergency+room+near+me',
    });

    if (query) {
      Linking.canOpenURL(query).then((supported) => {
        if (supported) {
          Linking.openURL(query);
        } else {
          // Fallback to Google Maps web
          Linking.openURL('https://www.google.com/maps/search/emergency+room+near+me');
        }
      });
    }
  };

  // Handle Call Doctor (placeholder - user should configure in profile)
  const handleCallDoctor = () => {
    Alert.alert(
      'Call Your Doctor',
      'You can add your doctor\'s contact information in your profile settings.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Go to Profile',
          onPress: () => router.push('/settings'),
        },
      ]
    );
  };

  // Handle Text Emergency Contact (placeholder)
  const handleTextEmergencyContact = () => {
    Alert.alert(
      'Text Emergency Contact',
      'You can add an emergency contact in your profile settings.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Go to Profile',
          onPress: () => router.push('/settings'),
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['bottom']}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Critical Warning Banner (FR-057) */}
      <View style={styles.warningBanner}>
        <Ionicons name="warning" size={48} color={Colors.text.inverse} />
        <Text style={styles.warningTitle}>SEEK MEDICAL CARE NOW</Text>
        <Text style={styles.warningSubtitle}>
          If you are experiencing severe pain or symptoms, go to the emergency room or call 911 immediately.
        </Text>
      </View>

      {/* Emergency Actions (FR-058) */}
      <Card style={styles.actionsCard}>
        <Text style={styles.sectionTitle}>Emergency Actions</Text>

        {/* Call 911 */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonEmergency]}
          onPress={handleCall911}
          activeOpacity={0.7}
        >
          <View style={styles.actionIconContainer}>
            <Ionicons name="call" size={28} color={Colors.text.inverse} />
          </View>
          <View style={styles.actionTextContainer}>
            <Text style={styles.actionButtonTitle}>Call 911</Text>
            <Text style={styles.actionButtonSubtitle}>For immediate emergency assistance</Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color={Colors.text.inverse} />
        </TouchableOpacity>

        {/* Find Nearest ER */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonUrgent]}
          onPress={handleFindER}
          activeOpacity={0.7}
        >
          <View style={styles.actionIconContainer}>
            <Ionicons name="location" size={28} color={Colors.text.inverse} />
          </View>
          <View style={styles.actionTextContainer}>
            <Text style={styles.actionButtonTitle}>Find Nearest ER</Text>
            <Text style={styles.actionButtonSubtitle}>Open maps to nearby emergency rooms</Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color={Colors.text.inverse} />
        </TouchableOpacity>

        {/* Call Your Doctor */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonNormal]}
          onPress={handleCallDoctor}
          activeOpacity={0.7}
        >
          <View style={styles.actionIconContainer}>
            <Ionicons name="medkit" size={28} color={Colors.primary[500]} />
          </View>
          <View style={styles.actionTextContainer}>
            <Text style={[styles.actionButtonTitle, styles.actionButtonTitleNormal]}>Call My Doctor</Text>
            <Text style={[styles.actionButtonSubtitle, styles.actionButtonSubtitleNormal]}>
              Contact your physician
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color={Colors.gray[600]} />
        </TouchableOpacity>

        {/* Text Emergency Contact */}
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonNormal]}
          onPress={handleTextEmergencyContact}
          activeOpacity={0.7}
        >
          <View style={styles.actionIconContainer}>
            <Ionicons name="chatbubble-ellipses" size={28} color={Colors.primary[500]} />
          </View>
          <View style={styles.actionTextContainer}>
            <Text style={[styles.actionButtonTitle, styles.actionButtonTitleNormal]}>Text Emergency Contact</Text>
            <Text style={[styles.actionButtonSubtitle, styles.actionButtonSubtitleNormal]}>
              Notify your emergency contact
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color={Colors.gray[600]} />
        </TouchableOpacity>
      </Card>

      {/* Additional Support (FR-063) */}
      <Card style={styles.supportCard}>
        <Text style={styles.sectionTitle}>Additional Support</Text>

        {/* Breathing Exercise Toggle */}
        <TouchableOpacity
          style={styles.breathingToggle}
          onPress={() => setShowBreathingExercise(!showBreathingExercise)}
          activeOpacity={0.7}
        >
          <View style={styles.breathingHeader}>
            <Ionicons name="fitness" size={24} color={Colors.primary[500]} />
            <Text style={styles.breathingTitle}>Calming Breathing Exercise</Text>
          </View>
          <Ionicons
            name={showBreathingExercise ? 'chevron-up' : 'chevron-down'}
            size={24}
            color={Colors.gray[600]}
          />
        </TouchableOpacity>

        {showBreathingExercise && (
          <View style={styles.breathingContent}>
            {/* CRITICAL: Disclaimer per FR-063 */}
            <View style={styles.disclaimer}>
              <Ionicons name="information-circle" size={20} color={Colors.warning} />
              <Text style={styles.disclaimerText}>
                FOR COMFORT ONLY - NOT MEDICAL TREATMENT
              </Text>
            </View>

            <Text style={styles.breathingInstructions}>
              This breathing exercise may help you feel calmer while you seek medical care.
              It is NOT a substitute for emergency medical treatment.
            </Text>

            <View style={styles.breathingSteps}>
              <Text style={styles.breathingStep}>1. Breathe in slowly through your nose for 4 seconds</Text>
              <Text style={styles.breathingStep}>2. Hold your breath for 4 seconds</Text>
              <Text style={styles.breathingStep}>3. Breathe out slowly through your mouth for 4 seconds</Text>
              <Text style={styles.breathingStep}>4. Rest for 4 seconds</Text>
              <Text style={styles.breathingStep}>5. Repeat 3-5 times</Text>
            </View>

            {/* Reinforcing message (FR-059 - never discourage ER) */}
            <View style={styles.reminderBox}>
              <Text style={styles.reminderText}>
                ⚠️ If your symptoms worsen, stop and seek immediate medical attention.
              </Text>
            </View>
          </View>
        )}
      </Card>

      {/* Document Symptoms (During Attack) */}
      <Card style={styles.logAttackCard}>
        <Text style={styles.sectionTitle}>Document Symptoms for Medical Staff</Text>
        <Text style={styles.logAttackDescription}>
          Create a shareable report of your current symptoms and recent meals to share with
          doctors or ER staff.
        </Text>
        <Button
          variant="primary"
          size="large"
          fullWidth
          onPress={() => router.push('/document-symptoms')}
          style={{ marginBottom: Spacing.md }}
        >
          Document Symptoms Now
        </Button>
      </Card>

      {/* Log Attack Button (After Attack) */}
      <Card style={styles.logAttackCard}>
        <Text style={styles.sectionTitle}>After Your Attack</Text>
        <Text style={styles.logAttackDescription}>
          Once you've received medical care and are feeling better, logging your attack helps
          us identify patterns and prevent future episodes.
        </Text>
        <Button
          variant="secondary"
          size="large"
          fullWidth
          onPress={() => router.push('/log-attack')}
        >
          Log Attack Details Later
        </Button>
      </Card>

      {/* Medical Disclaimer (FR-059 - reinforcing safety) */}
      <View style={styles.disclaimer}>
        <Text style={styles.disclaimerText}>
          This app provides dietary guidance only. It is NOT a substitute for professional medical advice,
          diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider
          with any questions you may have regarding a medical condition.
        </Text>
      </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  contentContainer: {
    padding: Spacing.base,
    paddingBottom: 100, // Large bottom padding to ensure buttons are visible
  },

  // Warning Banner
  warningBanner: {
    backgroundColor: Colors.error,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    alignItems: 'center',
    marginBottom: Spacing.xl,
    ...Shadows.lg,
  },
  warningTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    marginTop: Spacing.md,
    textAlign: 'center',
    letterSpacing: 1,
  },
  warningSubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.inverse,
    marginTop: Spacing.sm,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },

  // Actions Card
  actionsCard: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.base,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.base,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.md,
    ...Shadows.sm,
  },
  actionButtonEmergency: {
    backgroundColor: Colors.error,
  },
  actionButtonUrgent: {
    backgroundColor: Colors.warning,
  },
  actionButtonNormal: {
    backgroundColor: Colors.background.primary,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: BorderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionButtonTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.inverse,
    marginBottom: Spacing.xs,
  },
  actionButtonTitleNormal: {
    color: Colors.text.primary,
  },
  actionButtonSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.inverse,
    opacity: 0.9,
  },
  actionButtonSubtitleNormal: {
    color: Colors.text.secondary,
    opacity: 1,
  },

  // Support Card
  supportCard: {
    marginBottom: Spacing.xl,
  },
  breathingToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.md,
  },
  breathingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  breathingTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
  },
  breathingContent: {
    marginTop: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  breathingInstructions: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
    marginBottom: Spacing.base,
  },
  breathingSteps: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.md,
    padding: Spacing.base,
    marginBottom: Spacing.base,
  },
  breathingStep: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  reminderBox: {
    backgroundColor: Colors.warning + '20',
    borderLeftWidth: 4,
    borderLeftColor: Colors.warning,
    padding: Spacing.md,
    borderRadius: BorderRadius.sm,
  },
  reminderText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.primary,
    fontWeight: Typography.fontWeight.medium,
  },

  // Log Attack Card
  logAttackCard: {
    marginBottom: Spacing['2xl'],
  },
  logAttackDescription: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
    marginBottom: Spacing.base,
  },

  // Disclaimer
  disclaimer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
    padding: Spacing.md,
    backgroundColor: Colors.background.tertiary,
    borderRadius: BorderRadius.md,
    marginTop: Spacing.base,
    marginBottom: Spacing['3xl'],
  },
  disclaimerText: {
    flex: 1,
    fontSize: Typography.fontSize.xs,
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xs,
  },
});
