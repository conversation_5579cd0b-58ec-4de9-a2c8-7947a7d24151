import { StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useRouter } from 'expo-router';

import ParallaxScrollView from '@/components/parallax-scroll-view';
import { ThemedText } from '@/components/themed-text';
import { ThemedView } from '@/components/themed-view';
import { IconSymbol } from '@/components/ui/icon-symbol';
import { Fonts, Colors } from '@/constants/theme';
import { useAuth } from '@/contexts/auth-context';
import { useColorScheme } from '@/hooks/use-color-scheme';

export default function SettingsScreen() {
  const { user, logout, isLoading } = useAuth();
  const router = useRouter();
  const colorScheme = useColorScheme();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              router.replace('/auth/login');
            } catch (error) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#808080"
          name="gearshape.fill"
          style={styles.headerImage}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText
          type="title"
          style={{
            fontFamily: Fonts.rounded,
          }}>
          Settings
        </ThemedText>
      </ThemedView>

      {/* User Profile Section */}
      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Account
        </ThemedText>
        {user && (
          <ThemedView style={styles.userInfo}>
            <ThemedView style={styles.userRow}>
              <IconSymbol name="person.circle.fill" size={20} color={Colors[colorScheme ?? 'light'].icon} />
              <ThemedText style={styles.userLabel}>Name:</ThemedText>
              <ThemedText style={styles.userValue}>{user.name}</ThemedText>
            </ThemedView>
            <ThemedView style={styles.userRow}>
              <IconSymbol name="envelope.fill" size={20} color={Colors[colorScheme ?? 'light'].icon} />
              <ThemedText style={styles.userLabel}>Email:</ThemedText>
              <ThemedText style={styles.userValue}>{user.email}</ThemedText>
            </ThemedView>
            <ThemedView style={styles.userRow}>
              <IconSymbol name="calendar" size={20} color={Colors[colorScheme ?? 'light'].icon} />
              <ThemedText style={styles.userLabel}>Member since:</ThemedText>
              <ThemedText style={styles.userValue}>
                {new Date(user.created_at).toLocaleDateString()}
              </ThemedText>
            </ThemedView>
          </ThemedView>
        )}
      </ThemedView>

      {/* Settings Options */}
      <ThemedView style={styles.section}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Preferences
        </ThemedText>
        <ThemedText style={styles.placeholder}>Profile settings coming soon...</ThemedText>
      </ThemedView>

      {/* Logout Button */}
      <ThemedView style={styles.section}>
        <TouchableOpacity
          style={[
            styles.logoutButton,
            { backgroundColor: Colors[colorScheme ?? 'light'].tint },
          ]}
          onPress={handleLogout}
          disabled={isLoading}>
          <IconSymbol name="rectangle.portrait.and.arrow.right" size={20} color="#FFFFFF" />
          <ThemedText style={styles.logoutButtonText}>
            {isLoading ? 'Logging out...' : 'Logout'}
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#808080',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  userInfo: {
    gap: 12,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  userLabel: {
    fontWeight: '500',
    minWidth: 100,
  },
  userValue: {
    flex: 1,
    opacity: 0.8,
  },
  placeholder: {
    opacity: 0.6,
    fontStyle: 'italic',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  logoutButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
});
