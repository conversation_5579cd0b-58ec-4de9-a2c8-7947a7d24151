import { Platform, StyleSheet, TouchableOpacity, ScrollView, View, Text, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

import { HelloWave } from '@/components/hello-wave';
import { ThemedText } from '@/components/themed-text';
import { ThemedView } from '@/components/themed-view';
import { Link, useRouter } from 'expo-router';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { MilestoneCelebration } from '@/components/MilestoneCelebration';
import { useAttackFreeDays } from '@/hooks/useAttackFreeDays';

export default function HomeScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const attackFreeDays = useAttackFreeDays();
  const [showMilestone, setShowMilestone] = useState(false);

  return (
    <ScrollView style={styles.container}>
      {/* Welcome Banner */}
      <LinearGradient
        colors={
          colorScheme === 'dark'
            ? ['#1D3D47', '#2A5A6A']
            : ['#A1CEDC', '#7AB8CC']
        }
        style={styles.banner}>
        <ThemedView style={[styles.bannerContent, { backgroundColor: 'transparent' }]}>
          <ThemedText type="title" style={styles.bannerTitle}>Welcome!</ThemedText>
          <HelloWave />
        </ThemedView>
      </LinearGradient>

      {/* Main Content */}
      <ThemedView style={styles.content}>
        <ThemedView style={styles.titleContainer}>
          <ThemedText type="title">GallDiet</ThemedText>
        </ThemedView>

        {/* Attack-Free Days Tracker (T205-T206) */}
        {/* Only show if we have attack data (days > 0 means user had an attack in the past) */}
        {!attackFreeDays.loading && !attackFreeDays.error && attackFreeDays.days > 0 && (
          <View style={styles.attackFreeCard}>
            <View style={styles.attackFreeHeader}>
              <Ionicons name="shield-checkmark" size={32} color="#10B981" />
              <View style={styles.attackFreeTextContainer}>
                <Text style={styles.attackFreeDays}>{attackFreeDays.days}</Text>
                <Text style={styles.attackFreeLabel}>
                  Day{attackFreeDays.days !== 1 ? 's' : ''} Attack-Free
                </Text>
              </View>
              {attackFreeDays.isMilestone && (
                <TouchableOpacity
                  style={styles.celebrateButton}
                  onPress={() => setShowMilestone(true)}
                  activeOpacity={0.7}
                >
                  <Ionicons name="trophy" size={24} color="#F59E0B" />
                  <Text style={styles.celebrateText}>Celebrate!</Text>
                </TouchableOpacity>
              )}
            </View>
            <Text style={styles.attackFreeMessage}>
              Keep avoiding your triggers! You're doing great! 💪
            </Text>
          </View>
        )}

        {/* Milestone Celebration Modal */}
        {attackFreeDays.milestoneDay && (
          <MilestoneCelebration
            visible={showMilestone}
            days={attackFreeDays.milestoneDay}
            onClose={() => setShowMilestone(false)}
          />
        )}

        {/* EMERGENCY SUPPORT - Always visible, never gated (FR-056) */}
        <ThemedView style={styles.emergencyContainer}>
          <TouchableOpacity
            style={styles.emergencyButton}
            onPress={() => router.push('/emergency-support')}
            activeOpacity={0.7}>
            <ThemedText style={styles.emergencyIcon}>🚨</ThemedText>
            <ThemedView style={[styles.emergencyTextContainer, { backgroundColor: 'transparent' }]}>
              <ThemedText style={styles.emergencyButtonText}>Emergency Support</ThemedText>
              <ThemedText style={styles.emergencyButtonSubtext}>
                Call 911, Find ER, Medical Help
              </ThemedText>
            </ThemedView>
          </TouchableOpacity>
          <ThemedText style={styles.emergencyDescription}>
            24/7 access to emergency contacts and support resources. Never hesitate to seek medical care.
          </ThemedText>
        </ThemedView>

        {/* Quick access to features */}
        <ThemedView style={styles.stepContainer}>
          <ThemedText type="subtitle">Track Your Health</ThemedText>
          <TouchableOpacity
            style={styles.featureButton}
            onPress={() => router.push('/log-attack')}>
            <ThemedText style={styles.featureButtonText}>📝 Log Gallstone Attack</ThemedText>
          </TouchableOpacity>
          <ThemedText style={styles.featureDescription}>
            Record gallstone attacks with pain intensity, location, symptoms, and more. We'll analyze
            your scan history to identify potential triggers.
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.stepContainer}>
          <ThemedText type="subtitle">Step 1: Try it</ThemedText>
          <ThemedText>
            Edit <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText> to see
            changes. Press{' '}
            <ThemedText type="defaultSemiBold">
              {Platform.select({
                ios: 'cmd + d',
                android: 'cmd + m',
                web: 'F12',
              })}
            </ThemedText>{' '}
            to open developer tools.
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.stepContainer}>
          <Link href="/modal">
            <Link.Trigger>
              <ThemedText type="subtitle">Step 2: Explore</ThemedText>
            </Link.Trigger>
            <Link.Preview />
            <Link.Menu>
              <Link.MenuAction
                title="Action"
                icon="cube"
                onPress={() => alert('Action pressed')}
              />
              <Link.MenuAction
                title="Share"
                icon="square.and.arrow.up"
                onPress={() => alert('Share pressed')}
              />
              <Link.Menu title="More" icon="ellipsis">
                <Link.MenuAction
                  title="Delete"
                  icon="trash"
                  destructive
                  onPress={() => alert('Delete pressed')}
                />
              </Link.Menu>
            </Link.Menu>
          </Link>

          <ThemedText>
            Tap the History tab to view your scan history and track your progress.
          </ThemedText>
        </ThemedView>

        <ThemedView style={styles.stepContainer}>
          <ThemedText type="subtitle">Step 3: Get a fresh start</ThemedText>
          <ThemedText>
            When you're ready, run{' '}
            <ThemedText type="defaultSemiBold">npm run reset-project</ThemedText> to get a fresh{' '}
            <ThemedText type="defaultSemiBold">app</ThemedText> directory. This will move the
            current <ThemedText type="defaultSemiBold">app</ThemedText> to{' '}
            <ThemedText type="defaultSemiBold">app-example</ThemedText>.
          </ThemedText>
        </ThemedView>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  banner: {
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  bannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  bannerTitle: {
    color: '#FFFFFF',
  },
  content: {
    padding: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 20,
  },
  stepContainer: {
    gap: 8,
    marginBottom: 16,
  },
  emergencyContainer: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#FEE2E2',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#EF4444',
  },
  emergencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EF4444',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  emergencyIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  emergencyTextContainer: {
    flex: 1,
  },
  emergencyButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  emergencyButtonSubtext: {
    color: '#fff',
    fontSize: 13,
    opacity: 0.9,
  },
  emergencyDescription: {
    fontSize: 13,
    color: '#7F1D1D',
    lineHeight: 18,
    paddingHorizontal: 4,
  },
  featureButton: {
    backgroundColor: '#3b82f6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 8,
  },
  featureButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  featureDescription: {
    fontSize: 14,
    opacity: 0.7,
    lineHeight: 20,
  },
  attackFreeCard: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#ECFDF5',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#10B981',
  },
  attackFreeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  attackFreeTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  attackFreeDays: {
    fontSize: 36,
    fontWeight: '800',
    color: '#10B981',
    lineHeight: 40,
  },
  attackFreeLabel: {
    fontSize: 14,
    color: '#065F46',
    fontWeight: '600',
  },
  attackFreeMessage: {
    fontSize: 13,
    color: '#065F46',
    lineHeight: 18,
  },
  celebrateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 4,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  celebrateText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#92400E',
  },
});
