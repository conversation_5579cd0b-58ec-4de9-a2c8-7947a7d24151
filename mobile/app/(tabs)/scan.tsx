import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { CameraView, CameraType, useCameraPermissions, BarcodeScanningResult } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import scanService, { ScanStatusResponse } from '@/services/scan-service';
import { useAuth } from '@/contexts/auth-context';
import UpgradePromptModal from '@/components/UpgradePromptModal';

type ScanStatus = 'idle' | 'uploading' | 'analyzing' | 'completed' | 'error';
type ScanMode = 'photo' | 'barcode';

export default function ScanScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const cameraRef = useRef<CameraView>(null);
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [scanStatus, setScanStatus] = useState<ScanStatus>('idle');
  const [statusMessage, setStatusMessage] = useState<string>('');
  const [scanProgress, setScanProgress] = useState<number>(0);
  const [scanMode, setScanMode] = useState<ScanMode>('photo'); // T152: Mode toggle state
  const [isProcessingBarcode, setIsProcessingBarcode] = useState(false); // Prevent duplicate scans
  const [showUpgradeModal, setShowUpgradeModal] = useState(false); // T163: Upgrade modal state
  const [todayPhotoScans, setTodayPhotoScans] = useState(0); // T162: Track photo scans today

  // T147: Check if user is premium (simplified - actual check would use subscription_tier)
  const isPremium = false; // TODO: Get from user.subscription_tier === 'premium'
  const dailyPhotoLimit = 3; // FR-029: Free users get 3 photo scans/day

  // Handle camera permissions
  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <View style={styles.permissionContainer}>
          <Text style={styles.permissionTitle}>Camera Access Required</Text>
          <Text style={styles.permissionMessage}>
            GallDiet needs camera access to scan your meals and help you avoid gallstone
            attacks by identifying foods that trigger YOUR symptoms.
          </Text>
          <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const toggleCameraFacing = () => {
    setFacing((current) => (current === 'back' ? 'front' : 'back'));
  };

  // T152: Toggle between photo and barcode mode
  const toggleScanMode = () => {
    setScanMode((current) => (current === 'photo' ? 'barcode' : 'photo'));
    setIsProcessingBarcode(false); // Reset barcode processing state
  };

  // T153: Handle barcode scanned
  const handleBarcodeScanned = async ({ type, data }: BarcodeScanningResult) => {
    if (isProcessingBarcode || scanStatus !== 'idle') return;

    setIsProcessingBarcode(true);

    try {
      setScanStatus('uploading');
      setStatusMessage('Looking up product...');
      setScanProgress(50);

      // T153: Call barcode API immediately (synchronous, < 2 seconds)
      const scan = await scanService.scanBarcode(data, getMealType());

      setScanStatus('completed');
      setStatusMessage('Complete!');
      setScanProgress(100);

      // T154: Navigate to results instantly
      setTimeout(() => {
        router.push(`/scan-result/${scan.id}` as any);
        setScanStatus('idle');
        setStatusMessage('');
        setScanProgress(0);
        setIsProcessingBarcode(false);
      }, 300);
    } catch (error: any) {
      console.error('Barcode scan failed:', error);
      setScanStatus('error');
      setStatusMessage('');
      setScanProgress(0);

      // T155: Handle product not found
      if (error?.response?.status === 404) {
        Alert.alert(
          'Product Not Found',
          'Product not in database. Try photo scan instead!',
          [
            { text: 'Try Photo Scan', onPress: () => setScanMode('photo') },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
      } else {
        Alert.alert(
          'Barcode Scan Failed',
          error?.message || 'Failed to scan barcode. Please try again.'
        );
      }

      setTimeout(() => {
        setScanStatus('idle');
        setIsProcessingBarcode(false);
      }, 2000);
    }
  };

  const handleTakePhoto = async () => {
    if (!cameraRef.current) return;

    // T163: Check scan limit before taking photo
    if (!isPremium && todayPhotoScans >= dailyPhotoLimit) {
      setShowUpgradeModal(true);
      return;
    }

    try {
      // T218: Reduce quality to 0.6 for faster upload (< 1 second target)
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.6,
        base64: false,
      });

      if (photo) {
        await uploadPhoto(photo.uri);
      }
    } catch (error) {
      console.error('Failed to take photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const handlePickImage = async () => {
    // T163: Check scan limit before picking image
    if (!isPremium && todayPhotoScans >= dailyPhotoLimit) {
      setShowUpgradeModal(true);
      return;
    }

    try {
      // T218: Reduce quality to 0.6 for faster upload
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        quality: 0.6,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadPhoto(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Failed to pick image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const getMealType = (): 'breakfast' | 'lunch' | 'dinner' | 'snack' => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 11) return 'breakfast';
    if (hour >= 11 && hour < 15) return 'lunch';
    if (hour >= 15 && hour < 22) return 'dinner';
    return 'snack';
  };

  const uploadPhoto = async (uri: string) => {
    try {
      // T220: Simplified loading message - just "Analyzing..."
      setScanStatus('uploading');
      setStatusMessage('Analyzing...');
      setScanProgress(0);

      // Upload photo
      const scan = await scanService.uploadPhoto({
        photo: {
          uri,
          type: 'image/jpeg',
          name: 'meal-photo.jpg',
        },
        meal_type: getMealType(),
      });

      console.log('Upload response:', scan);

      if (!scan || !scan.id) {
        throw new Error('Invalid response from server - missing scan ID');
      }

      // T162: Increment photo scan count on successful upload
      setTodayPhotoScans((prev) => prev + 1);

      setScanStatus('analyzing');
      setStatusMessage('Analyzing...');
      setScanProgress(50);

      // Poll for completion
      await scanService.pollForCompletion(
        scan.id,
        (status: ScanStatusResponse) => {
          if (status.status === 'analyzing') {
            setScanProgress(75);
          }
        }
      );

      setScanStatus('completed');
      setStatusMessage('Analyzing...');
      setScanProgress(100);

      // Navigate to result screen
      setTimeout(() => {
        router.push(`/scan-result/${scan.id}` as any);
        setScanStatus('idle');
        setStatusMessage('');
        setScanProgress(0);
      }, 500);
    } catch (error: any) {
      console.error('Failed to upload photo:', error);
      setScanStatus('error');
      setStatusMessage('');
      setScanProgress(0);

      // T148: Handle 403 Forbidden for limit exceeded
      if (error?.response?.status === 403) {
        setShowUpgradeModal(true);
      } else {
        Alert.alert(
          'Scan Failed',
          error?.message || 'Failed to analyze photo. Please try again.'
        );
      }

      setTimeout(() => {
        setScanStatus('idle');
      }, 2000);
    }
  };

  const isScanning = scanStatus !== 'idle' && scanStatus !== 'error';

  // T164: Calculate remaining scans
  const remainingScans = isPremium ? Infinity : Math.max(0, dailyPhotoLimit - todayPhotoScans);
  const shouldShowScanCount = !isPremium && scanMode === 'photo';

  // T164: Handle upgrade navigation
  const handleUpgrade = () => {
    setShowUpgradeModal(false);
    // TODO: Navigate to subscription/upgrade screen
    router.push('/subscription' as any);
  };

  return (
    <View style={styles.container}>
      {/* T161: Upgrade prompt modal */}
      <UpgradePromptModal
        visible={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        onUpgrade={handleUpgrade}
        message="You're discovering YOUR triggers quickly. Premium gives unlimited scans to help you stay safe."
      />
      {isScanning ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingTitle}>{statusMessage}</Text>
          <View style={styles.progressContainer}>
            <View style={styles.progressTrack}>
              <View style={[styles.progressFill, { width: `${scanProgress}%` }]} />
            </View>
            <Text style={styles.progressText}>{scanProgress}%</Text>
          </View>
          {scanStatus === 'analyzing' && (
            <Text style={styles.loadingSubtitle}>
              Analyzing ingredients that may trigger gallstone symptoms based on YOUR history
            </Text>
          )}
        </View>
      ) : (
        <>
          <CameraView
            ref={cameraRef}
            style={styles.camera}
            facing={facing}
            barcodeScannerSettings={
              scanMode === 'barcode'
                ? {
                    barcodeTypes: ['ean13', 'ean8', 'upc_a', 'upc_e', 'code128', 'code39'],
                  }
                : undefined
            }
            onBarcodeScanned={scanMode === 'barcode' ? handleBarcodeScanned : undefined}
          />

          <View style={styles.overlay}>
            <View style={styles.topBar}>
              <View style={styles.infoContainerTop}>
                <Text style={styles.infoText}>
                  {scanMode === 'photo'
                    ? "Scan meals to avoid gallstone attacks - we'll check against YOUR triggers"
                    : "Point camera at barcode for instant product lookup"}
                </Text>
              </View>
              <TouchableOpacity style={styles.flipButton} onPress={toggleCameraFacing}>
                <Text style={styles.flipButtonText}>Flip</Text>
              </TouchableOpacity>
            </View>

            {/* T152: Mode toggle */}
            <View style={styles.modeToggleContainer}>
              <TouchableOpacity
                style={[styles.modeButton, scanMode === 'photo' && styles.modeButtonActive]}
                onPress={() => setScanMode('photo')}
              >
                <Text style={[styles.modeButtonText, scanMode === 'photo' && styles.modeButtonTextActive]}>
                  Photo
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modeButton, scanMode === 'barcode' && styles.modeButtonActive]}
                onPress={() => setScanMode('barcode')}
              >
                <Text style={[styles.modeButtonText, scanMode === 'barcode' && styles.modeButtonTextActive]}>
                  Barcode
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.cornerTopLeft]} />
              <View style={[styles.corner, styles.cornerTopRight]} />
              <View style={[styles.corner, styles.cornerBottomLeft]} />
              <View style={[styles.corner, styles.cornerBottomRight]} />
              <Text style={styles.scanInstruction}>
                {scanMode === 'photo' ? 'Center your meal in the frame' : 'Point at barcode to scan automatically'}
              </Text>
            </View>

            {/* Only show capture controls in photo mode */}
            {scanMode === 'photo' && (
              <>
                {/* T162: Scan count progress indicator */}
                {shouldShowScanCount && (
                  <View style={styles.scanCountContainer}>
                    <Text style={styles.scanCountText}>
                      {todayPhotoScans}/{dailyPhotoLimit} scans used today
                    </Text>
                    {remainingScans === 0 && (
                      <TouchableOpacity
                        onPress={() => setShowUpgradeModal(true)}
                        style={styles.upgradeLink}
                      >
                        <Text style={styles.upgradeLinkText}>Upgrade for unlimited</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}

                <View style={styles.bottomBar}>
                  <TouchableOpacity style={styles.galleryButton} onPress={handlePickImage}>
                    <Text style={styles.galleryButtonText}>Gallery</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.captureButton} onPress={handleTakePhoto}>
                    <View style={styles.captureButtonInner} />
                  </TouchableOpacity>

                  <View style={styles.galleryButton} />
                </View>
              </>
            )}
          </View>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
  },
  infoContainerTop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 12,
    borderRadius: 8,
    marginRight: 12,
  },
  flipButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  flipButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  scanFrame: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 40,
  },
  corner: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderColor: '#3b82f6',
  },
  cornerTopLeft: {
    top: 0,
    left: 0,
    borderTopWidth: 4,
    borderLeftWidth: 4,
  },
  cornerTopRight: {
    top: 0,
    right: 0,
    borderTopWidth: 4,
    borderRightWidth: 4,
  },
  cornerBottomLeft: {
    bottom: 0,
    left: 0,
    borderBottomWidth: 4,
    borderLeftWidth: 4,
  },
  cornerBottomRight: {
    bottom: 0,
    right: 0,
    borderBottomWidth: 4,
    borderRightWidth: 4,
  },
  scanInstruction: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
  },
  galleryButton: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  galleryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#3b82f6',
  },
  captureButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#3b82f6',
  },
  infoText: {
    color: '#fff',
    fontSize: 12,
    lineHeight: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#fff',
  },
  permissionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 16,
    textAlign: 'center',
  },
  permissionMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#fff',
  },
  loadingTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginTop: 24,
    marginBottom: 16,
  },
  loadingSubtitle: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 20,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressTrack: {
    width: '100%',
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3b82f6',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#94a3b8',
    marginTop: 8,
    fontFamily: 'monospace',
  },
  modeToggleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    padding: 4,
    alignSelf: 'center',
  },
  modeButton: {
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 6,
    minWidth: 100,
    alignItems: 'center',
  },
  modeButtonActive: {
    backgroundColor: '#3b82f6',
  },
  modeButtonText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '600',
  },
  modeButtonTextActive: {
    color: '#fff',
  },
  scanCountContainer: {
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    alignSelf: 'center',
  },
  scanCountText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  upgradeLink: {
    marginTop: 4,
  },
  upgradeLinkText: {
    color: '#3b82f6',
    fontSize: 13,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
});
