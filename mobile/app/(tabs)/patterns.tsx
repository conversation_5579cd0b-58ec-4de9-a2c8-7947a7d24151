import { ThemedText } from '@/components/themed-text';
import { ThemedView } from '@/components/themed-view';
import patternService, { PatternSuggestion } from '@/services/pattern-service';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { useRouter } from 'expo-router';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withSpring,
  withTiming,
} from 'react-native-reanimated';

export default function PatternsScreen() {
  const router = useRouter();
  const [suggestions, setSuggestions] = useState<PatternSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [confirmingId, setConfirmingId] = useState<number | null>(null);
  const [rejectingId, setRejectingId] = useState<number | null>(null);
  const [celebratingId, setCelebratingId] = useState<number | null>(null);

  useEffect(() => {
    loadSuggestions();
  }, []);

  const loadSuggestions = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const data = await patternService.getSuggestions();
      setSuggestions(data);
    } catch (error) {
      console.error('Failed to load pattern suggestions:', error);
      Alert.alert('Error', 'Failed to load pattern suggestions. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleConfirm = async (suggestion: PatternSuggestion) => {
    setConfirmingId(suggestion.id);

    try {
      const response = await patternService.confirmPattern(suggestion.id);

      // Show celebration animation (T119)
      setCelebratingId(suggestion.id);

      // Remove from list after animation
      setTimeout(() => {
        setSuggestions((prev) => prev.filter((s) => s.id !== suggestion.id));
        setCelebratingId(null);

        // Show success message
        Alert.alert(
          '🎉 Trigger Confirmed!',
          `${suggestion.suspected_trigger_name} has been added to your known triggers. We'll use this to provide better recommendations.`,
          [{ text: 'Great!', style: 'default' }]
        );
      }, 2000);
    } catch (error) {
      console.error('Failed to confirm pattern:', error);
      Alert.alert('Error', 'Failed to confirm pattern. Please try again.');
    } finally {
      setConfirmingId(null);
    }
  };

  const handleReject = async (suggestion: PatternSuggestion) => {
    Alert.alert(
      'Reject Pattern',
      `Are you sure you want to reject "${suggestion.suspected_trigger_name}" as a trigger? This will help us improve our detection.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            setRejectingId(suggestion.id);

            try {
              await patternService.rejectPattern(suggestion.id);
              setSuggestions((prev) => prev.filter((s) => s.id !== suggestion.id));
            } catch (error) {
              console.error('Failed to reject pattern:', error);
              Alert.alert('Error', 'Failed to reject pattern. Please try again.');
            } finally {
              setRejectingId(null);
            }
          },
        },
      ]
    );
  };

  const getConfidenceColor = (score: number): string => {
    if (score >= 0.8) return '#22c55e'; // High confidence - green
    if (score >= 0.6) return '#f59e0b'; // Medium confidence - orange
    return '#ef4444'; // Low confidence - red
  };

  const getConfidenceLabel = (score: number): string => {
    if (score >= 0.8) return 'High Confidence';
    if (score >= 0.6) return 'Medium Confidence';
    return 'Low Confidence';
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <ThemedText style={styles.loadingText}>Loading pattern suggestions...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={() => loadSuggestions(true)} />
        }
      >
        <ThemedView style={styles.header}>
          <ThemedText type="title">Pattern Suggestions</ThemedText>
          <ThemedText style={styles.subtitle}>
            Based on your attack history and scans, we've detected potential dietary triggers.
          </ThemedText>
        </ThemedView>

        {suggestions.length === 0 ? (
          <ThemedView style={styles.emptyState}>
            <ThemedText style={styles.emptyIcon}>🔍</ThemedText>
            <ThemedText type="subtitle">No Patterns Yet</ThemedText>
            <ThemedText style={styles.emptyText}>
              Keep logging attacks and scanning meals. We'll analyze your data to detect patterns
              and suggest potential triggers.
            </ThemedText>
          </ThemedView>
        ) : (
          <>
            <ThemedView style={styles.suggestionsIntro}>
              <ThemedText style={styles.suggestionsHint}>
                💡 Log attacks to help confirm or deny these patterns
              </ThemedText>
            </ThemedView>
            {suggestions.map((suggestion) => (
              <PatternCard
                key={suggestion.id}
                suggestion={suggestion}
                onConfirm={handleConfirm}
                onReject={handleReject}
                isConfirming={confirmingId === suggestion.id}
                isRejecting={rejectingId === suggestion.id}
                isCelebrating={celebratingId === suggestion.id}
                getConfidenceColor={getConfidenceColor}
                getConfidenceLabel={getConfidenceLabel}
              />
            ))}
          </>
        )}

        {/* Consistent "Log Attack" button - always visible */}
        <TouchableOpacity
          style={styles.logAttackButton}
          onPress={() => router.push('/log-attack')}
        >
          <ThemedText style={styles.logAttackButtonText}>
            {suggestions.length > 0 ? '📝 Log Another Attack' : '📝 Log an Attack'}
          </ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

// Separate component for each pattern card with animation (T119)
function PatternCard({
  suggestion,
  onConfirm,
  onReject,
  isConfirming,
  isRejecting,
  isCelebrating,
  getConfidenceColor,
  getConfidenceLabel,
}: {
  suggestion: PatternSuggestion;
  onConfirm: (s: PatternSuggestion) => void;
  onReject: (s: PatternSuggestion) => void;
  isConfirming: boolean;
  isRejecting: boolean;
  isCelebrating: boolean;
  getConfidenceColor: (score: number) => string;
  getConfidenceLabel: (score: number) => string;
}) {
  // Celebration animation (T119)
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);

  useEffect(() => {
    if (isCelebrating) {
      scale.value = withSequence(
        withSpring(1.05),
        withSpring(1),
        withSpring(1.05),
        withSpring(1)
      );
      rotation.value = withRepeat(
        withSequence(withTiming(-3, { duration: 100 }), withTiming(3, { duration: 100 })),
        3,
        true
      );
    }
  }, [isCelebrating]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { rotate: `${rotation.value}deg` }],
  }));

  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      style={[styles.card, isCelebrating && animatedStyle]}
    >
      {isCelebrating && (
        <View style={styles.celebrationOverlay}>
          <ThemedText style={styles.celebrationText}>🎉</ThemedText>
        </View>
      )}

      {/* T116: Trigger name and confidence score */}
      <View style={styles.cardHeader}>
        <ThemedText type="subtitle" style={styles.triggerName} lightColor="#111827" darkColor="#f9fafb">
          {suggestion.suspected_trigger_name}
        </ThemedText>
        <View
          style={[
            styles.confidenceBadge,
            { backgroundColor: getConfidenceColor(suggestion.confidence_score) },
          ]}
        >
          <ThemedText style={styles.confidenceText}>
            {Math.round(suggestion.confidence_score * 100)}%
          </ThemedText>
        </View>
      </View>

      <ThemedText style={styles.confidenceLabel} lightColor="#6b7280" darkColor="#9ca3af">
        {getConfidenceLabel(suggestion.confidence_score)}
      </ThemedText>

      {/* T117: Evidence display with timeline */}
      <View style={styles.evidenceSection}>
        <ThemedText type="defaultSemiBold" style={styles.evidenceTitle} lightColor="#374151" darkColor="#d1d5db">
          Evidence:
        </ThemedText>
        <ThemedText style={styles.evidenceSummary} lightColor="#4b5563" darkColor="#9ca3af">{suggestion.evidence_summary}</ThemedText>

        {/* Detailed timeline */}
        {suggestion.evidence.scans && suggestion.evidence.scans.length > 0 && (
          <View style={styles.timeline}>
            <ThemedText style={styles.timelineTitle} lightColor="#374151" darkColor="#d1d5db">Pattern Timeline:</ThemedText>
            {suggestion.evidence.scans.map((scan, index) => {
              const attack = suggestion.evidence.attacks[index];
              if (!attack) return null;

              const scanTime = new Date(scan.created_at);
              const attackTime = new Date(attack.onset_at);
              const hoursGap = Math.round(
                (attackTime.getTime() - scanTime.getTime()) / (1000 * 60 * 60)
              );

              return (
                <View key={scan.id} style={styles.timelineItem}>
                  <View style={styles.timelineDot} />
                  <View style={styles.timelineContent}>
                    <ThemedText style={styles.timelineText} lightColor="#374151" darkColor="#d1d5db">
                      📸 Scanned meal with {suggestion.suspected_trigger_name}
                    </ThemedText>
                    <ThemedText style={styles.timelineTime} lightColor="#9ca3af" darkColor="#6b7280">
                      {scanTime.toLocaleDateString()} {scanTime.toLocaleTimeString()}
                    </ThemedText>
                    <View style={styles.timelineGap}>
                      <ThemedText style={styles.timelineGapText} lightColor="#6b7280" darkColor="#9ca3af">↓ {hoursGap} hours later</ThemedText>
                    </View>
                    <ThemedText style={styles.timelineText} lightColor="#374151" darkColor="#d1d5db">
                      ⚠️ Attack (Pain: {attack.pain_intensity}/10)
                    </ThemedText>
                    <ThemedText style={styles.timelineTime} lightColor="#9ca3af" darkColor="#6b7280">
                      {attackTime.toLocaleDateString()} {attackTime.toLocaleTimeString()}
                    </ThemedText>
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </View>

      {/* T118: Confirm/Reject buttons */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={[styles.rejectButton, isRejecting && styles.buttonDisabled]}
          onPress={() => onReject(suggestion)}
          disabled={isRejecting || isConfirming || isCelebrating}
        >
          {isRejecting ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <ThemedText style={styles.rejectButtonText}>Not a Trigger</ThemedText>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.confirmButton, isConfirming && styles.buttonDisabled]}
          onPress={() => onConfirm(suggestion)}
          disabled={isConfirming || isRejecting || isCelebrating}
        >
          {isConfirming ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <ThemedText style={styles.confirmButtonText}>✓ Confirm Trigger</ThemedText>
          )}
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    opacity: 0.7,
  },
  header: {
    marginBottom: 24,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 8,
    lineHeight: 20,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    gap: 16,
  },
  emptyIcon: {
    fontSize: 64,
  },
  emptyText: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 20,
  },
  suggestionsIntro: {
    backgroundColor: '#eff6ff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 3,
    borderLeftColor: '#3b82f6',
  },
  suggestionsHint: {
    fontSize: 13,
    color: '#1e40af',
    lineHeight: 18,
  },
  logAttackButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 32,
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  logAttackButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  celebrationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  celebrationText: {
    fontSize: 80,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  triggerName: {
    flex: 1,
    marginRight: 12,
    fontSize: 20,
    textTransform: 'capitalize',
  },
  confidenceBadge: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  confidenceText: {
    color: '#ffffff',
    fontSize: 13,
    fontWeight: '700',
  },
  confidenceLabel: {
    fontSize: 13,
    opacity: 0.6,
    marginBottom: 16,
    fontWeight: '500',
  },
  evidenceSection: {
    marginBottom: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  evidenceTitle: {
    marginBottom: 4,
  },
  evidenceSummary: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  timeline: {
    marginTop: 8,
  },
  timelineTitle: {
    fontSize: 13,
    fontWeight: '600',
    marginBottom: 12,
    opacity: 0.8,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3b82f6',
    marginTop: 6,
    marginRight: 12,
  },
  timelineContent: {
    flex: 1,
  },
  timelineText: {
    fontSize: 13,
    lineHeight: 18,
  },
  timelineTime: {
    fontSize: 11,
    opacity: 0.5,
    marginTop: 2,
  },
  timelineGap: {
    paddingVertical: 8,
  },
  timelineGapText: {
    fontSize: 12,
    opacity: 0.6,
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  rejectButtonText: {
    color: '#374151',
    fontSize: 15,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#10b981',
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#10b981',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  confirmButtonText: {
    color: '#ffffff',
    fontSize: 15,
    fontWeight: '600',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
});
