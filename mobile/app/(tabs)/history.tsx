import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import historyService, { HistoryFilters } from '@/services/history-service';
import { ScanResponse } from '@/services/scan-service';
import { format } from 'date-fns';

export default function HistoryScreen() {
  const router = useRouter();
  const [scans, setScans] = useState<ScanResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [filters, setFilters] = useState<HistoryFilters>({});
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<string>('all'); // T225: Quick filters state

  // T156: Load initial history
  const loadHistory = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const response = await historyService.getHistory(undefined, filters);
      setScans(response.data || []);
      setNextCursor(response.next_cursor || null);
    } catch (err: any) {
      console.error('Failed to load history:', err);
      setError(err?.message || 'Failed to load history');
      setScans([]); // Ensure scans is always an array even on error
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // T156: Load more for infinite scroll
  const loadMore = async () => {
    if (!nextCursor || isLoadingMore) return;

    try {
      setIsLoadingMore(true);
      const response = await historyService.getHistory(nextCursor, filters);
      setScans((prev) => [...prev, ...(response.data || [])]);
      setNextCursor(response.next_cursor || null);
    } catch (err: any) {
      console.error('Failed to load more:', err);
    } finally {
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, [filters]);

  const handleRefresh = () => {
    loadHistory(true);
  };

  // T225: Quick filter handlers
  const applyQuickFilter = (filterType: string) => {
    setActiveFilter(filterType);
    const now = new Date();
    let newFilters: HistoryFilters = {};

    switch (filterType) {
      case 'today':
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        newFilters = {
          date_from: startOfToday.toISOString(),
        };
        break;
      case 'week':
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - 7);
        newFilters = {
          date_from: startOfWeek.toISOString(),
        };
        break;
      case 'safe':
        newFilters = {
          safety_score_min: 80,
        };
        break;
      case 'risky':
        newFilters = {
          safety_score_max: 59,
        };
        break;
      default:
        newFilters = {};
    }

    setFilters(newFilters);
  };

  // T157: Get color based on safety score
  const getScoreColor = (score: number | null): string => {
    if (score === null) return '#94a3b8';
    if (score >= 80) return '#10b981'; // Green
    if (score >= 50) return '#f59e0b'; // Yellow/Orange
    return '#ef4444'; // Red
  };

  // T157: Render scan item
  const renderScanItem = ({ item }: { item: ScanResponse }) => (
    <TouchableOpacity
      style={styles.scanCard}
      onPress={() => router.push(`/scan-result/${item.id}` as any)}
    >
      <View style={styles.scanCardHeader}>
        <View style={styles.scanCardInfo}>
          <Text style={styles.scanCardMealName} numberOfLines={1}>
            {item.meal_name || 'Analyzing...'}
          </Text>
          <Text style={styles.scanCardDate}>
            {format(new Date(item.created_at), 'MMM d, yyyy • h:mm a')}
          </Text>
          {item.type === 'barcode' && (
            <View style={styles.barcodeBadge}>
              <Text style={styles.barcodeBadgeText}>Barcode</Text>
            </View>
          )}
        </View>

        <View style={styles.scanCardScore}>
          <View
            style={[
              styles.scoreCircle,
              { borderColor: getScoreColor(item.safety_score) },
            ]}
          >
            <Text
              style={[
                styles.scoreText,
                { color: getScoreColor(item.safety_score) },
              ]}
            >
              {item.safety_score !== null ? item.safety_score : '—'}
            </Text>
          </View>
          <Text style={styles.scoreLabel}>Score</Text>
        </View>
      </View>

      {item.trigger_warnings && item.trigger_warnings.length > 0 && (
        <View style={styles.triggerWarningsContainer}>
          {item.trigger_warnings.slice(0, 2).map((warning, index) => (
            <View key={index} style={styles.triggerWarning}>
              <Text style={styles.triggerWarningText} numberOfLines={1}>
                ⚠️ {warning.trigger_name}
              </Text>
            </View>
          ))}
          {item.trigger_warnings.length > 2 && (
            <Text style={styles.moreTriggersText}>
              +{item.trigger_warnings.length - 2} more
            </Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Loading history...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => loadHistory()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scan History</Text>
        <Text style={styles.headerSubtitle}>
          {scans.length} {scans.length === 1 ? 'scan' : 'scans'}
        </Text>

        {/* T225: Quick filters */}
        <View style={styles.filtersContainer}>
          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'all' && styles.filterButtonActive]}
            onPress={() => applyQuickFilter('all')}
          >
            <Text style={[styles.filterButtonText, activeFilter === 'all' && styles.filterButtonTextActive]}>
              All
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'today' && styles.filterButtonActive]}
            onPress={() => applyQuickFilter('today')}
          >
            <Text style={[styles.filterButtonText, activeFilter === 'today' && styles.filterButtonTextActive]}>
              Today
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'week' && styles.filterButtonActive]}
            onPress={() => applyQuickFilter('week')}
          >
            <Text style={[styles.filterButtonText, activeFilter === 'week' && styles.filterButtonTextActive]}>
              This Week
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'safe' && styles.filterButtonActive]}
            onPress={() => applyQuickFilter('safe')}
          >
            <Text style={[styles.filterButtonText, activeFilter === 'safe' && styles.filterButtonTextActive]}>
              Safe Only
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.filterButton, activeFilter === 'risky' && styles.filterButtonActive]}
            onPress={() => applyQuickFilter('risky')}
          >
            <Text style={[styles.filterButtonText, activeFilter === 'risky' && styles.filterButtonTextActive]}>
              Risky Only
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {scans.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>📋</Text>
          <Text style={styles.emptyTitle}>No scans yet</Text>
          <Text style={styles.emptyMessage}>
            Start scanning meals to build your personalized history
          </Text>
        </View>
      ) : (
        <FlatList
          data={scans}
          renderItem={renderScanItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          refreshControl={
            <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
          }
          ListFooterComponent={
            isLoadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size="small" color="#3b82f6" />
              </View>
            ) : null
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  // T225: Quick filter styles
  filtersContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748b',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  listContent: {
    padding: 16,
  },
  scanCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  scanCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  scanCardInfo: {
    flex: 1,
    marginRight: 12,
  },
  scanCardMealName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  scanCardDate: {
    fontSize: 13,
    color: '#64748b',
    marginBottom: 4,
  },
  barcodeBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#e0f2fe',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginTop: 4,
  },
  barcodeBadgeText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#0369a1',
  },
  scanCardScore: {
    alignItems: 'center',
  },
  scoreCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  scoreText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 11,
    color: '#94a3b8',
    marginTop: 4,
  },
  triggerWarningsContainer: {
    marginTop: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  triggerWarning: {
    backgroundColor: '#fef2f2',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  triggerWarningText: {
    fontSize: 12,
    color: '#991b1b',
    fontWeight: '500',
  },
  moreTriggersText: {
    fontSize: 12,
    color: '#64748b',
    alignSelf: 'center',
    fontStyle: 'italic',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
    marginTop: 16,
  },
  loadingMoreContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
