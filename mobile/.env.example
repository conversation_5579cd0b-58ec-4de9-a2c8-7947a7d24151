# API Configuration
# Default: Works for iOS Simulator and Expo Web (most common for development)
EXPO_PUBLIC_API_URL=http://localhost/api

# For Android Emulator (special IP that routes to host machine):
# EXPO_PUBLIC_API_URL=http://********/api

# For physical devices (iPhone/Android on same WiFi network):
# Find your machine's IP: ipconfig getifaddr en0 (Mac) or ipconfig (Windows)
# EXPO_PUBLIC_API_URL=http://*************/api
