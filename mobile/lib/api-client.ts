import axios, { AxiosInstance, AxiosError, InternalAxiosRequestConfig } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

const AUTH_TOKEN_KEY = '@galldiet_auth_token';

// API base URL - Expo's environment variables are prefixed with EXPO_PUBLIC_
// @ts-ignore - process.env is available at runtime
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || Constants.expoConfig?.extra?.apiUrl || 'http://localhost/api';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 15000,
});

// Request interceptor - Add auth token to requests
apiClient.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const token = await AsyncStorage.getItem(AUTH_TOKEN_KEY);

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor - Handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    if (error.response?.status === 401) {
      // Token expired or invalid - clear stored auth data
      await AsyncStorage.multiRemove([AUTH_TOKEN_KEY, '@galldiet_auth_user']);

      // Optionally trigger logout or redirect to login
      // This could emit an event that the AuthContext listens to
    }

    return Promise.reject(error);
  }
);

export default apiClient;

// Type-safe API error handler
export interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
  status?: number;
}

export function handleApiError(error: unknown): ApiError {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<{
      message?: string;
      errors?: Record<string, string[]>;
    }>;

    return {
      message: axiosError.response?.data?.message || axiosError.message || 'An error occurred',
      errors: axiosError.response?.data?.errors,
      status: axiosError.response?.status,
    };
  }

  return {
    message: error instanceof Error ? error.message : 'An unknown error occurred',
  };
}
