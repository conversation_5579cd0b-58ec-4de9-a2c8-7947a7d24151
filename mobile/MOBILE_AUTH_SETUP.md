# Mobile Authentication Setup

This document describes the authentication implementation for the GallDiet mobile app.

## Architecture

The mobile app uses:
- **React Context API** for state management (AuthContext, ProfileContext)
- **Axios** for API calls with interceptors
- **AsyncStorage** for persistent token storage
- **Expo Router** for file-based routing and navigation

## Completed Tasks (T094-T098)

### T094: AuthContext ✅
**Location:** `/mobile/contexts/auth-context.tsx`

Features:
- User state management (user, token, isAuthenticated, isLoading)
- Login/register/logout methods
- Automatic token persistence with AsyncStorage
- Type-safe User interface matching backend

### T095: ProfileContext ✅
**Location:** `/mobile/contexts/profile-context.tsx`

Features:
- Profile data caching with 5-minute TTL
- Automatic cache invalidation
- Profile refresh and update methods
- Integrates with AuthContext for authentication state

### T098: API Client ✅
**Location:** `/mobile/lib/api-client.ts`

Features:
- Axios instance configured for Laravel API
- Request interceptor: Auto-adds Bearer token from AsyncStorage
- Response interceptor: Handles 401 errors and clears auth data
- Type-safe error handling with `handleApiError()` utility

**Services:**
- `/mobile/services/auth-service.ts` - Login, register, logout endpoints
- `/mobile/services/profile-service.ts` - Profile CRUD operations

### T096: Login Screen ✅
**Location:** `/mobile/app/auth/login.tsx`

Features:
- Email/password form with validation
- Loading states and error handling
- Navigation to registration
- Responsive keyboard-avoiding layout
- Clean, modern UI design

### T097: Registration Screen ✅
**Location:** `/mobile/app/auth/register.tsx`

Features:
- Full registration form (name, email, password, confirmation)
- Client-side validation (email format, password length, matching)
- Server-side error display
- Navigation to login
- ScrollView for smaller devices

## Authentication Flow

1. **App Launch:**
   - `AuthProvider` loads stored token/user from AsyncStorage
   - `RootLayoutNav` checks auth state
   - Redirects to `/auth/login` if not authenticated
   - Redirects to `/(tabs)` if authenticated

2. **Login:**
   - User enters credentials
   - `AuthContext.login()` calls API via `AuthService`
   - On success: Token + user saved to AsyncStorage and state
   - Navigation automatically redirects to `/(tabs)`

3. **Registration:**
   - User fills registration form
   - `AuthContext.register()` calls API via `AuthService`
   - On success: Token + user saved, auto-login
   - Navigation automatically redirects to `/(tabs)`

4. **API Requests:**
   - All requests automatically include `Authorization: Bearer {token}`
   - 401 responses trigger automatic logout and redirect to login

5. **Logout:**
   - `AuthContext.logout()` clears AsyncStorage
   - Auth state update triggers redirect to `/auth/login`

## Environment Configuration

Copy `.env.example` to `.env` in the `/mobile` directory:

```bash
cp .env.example .env
```

**Default configuration (works out of the box):**
```env
EXPO_PUBLIC_API_URL=http://localhost/api
```

This works for:
- ✅ **iOS Simulator** (most common for development)
- ✅ **Expo Web** (runs in browser)

**For other environments, update `.env`:**

**Android Emulator:**
```env
EXPO_PUBLIC_API_URL=http://********/api
```
(******** is a special IP that routes to your host machine)

**Physical Devices (iPhone/Android):**
```env
EXPO_PUBLIC_API_URL=http://*************/api  # YOUR machine's IP
```

**Finding your machine's IP:**
- **Mac:** `ipconfig getifaddr en0` or System Preferences → Network
- **Windows:** `ipconfig` and look for IPv4 Address
- **Linux:** `ip addr show` or `hostname -I`

**Important:** Both your computer and mobile device must be on the same WiFi network!

## File Structure

```
mobile/
├── contexts/
│   ├── auth-context.tsx      # Auth state & methods
│   ├── profile-context.tsx   # Profile caching
│   └── index.ts               # Context exports
├── services/
│   ├── auth-service.ts        # Auth API calls
│   └── profile-service.ts     # Profile API calls
├── lib/
│   └── api-client.ts          # Axios setup & interceptors
├── app/
│   ├── _layout.tsx            # Root + auth routing
│   └── auth/
│       ├── _layout.tsx        # Auth stack
│       ├── login.tsx          # Login screen
│       └── register.tsx       # Register screen
├── .env.example               # Environment template
└── app.config.ts              # Expo config with API URL

## Type Safety

All contexts and services are fully typed:
- `User` interface matches Laravel backend
- `UserProfile` interface matches backend
- `LoginResponse`, `RegisterResponse` for API responses
- `ApiError` interface for standardized error handling

## Error Handling

API errors are handled consistently:
- Validation errors (422) show field-specific messages
- Auth errors (401) trigger automatic logout
- Network errors show user-friendly alerts
- TypeScript ensures type-safe error objects

## Security

- Tokens stored in AsyncStorage (encrypted on device)
- Automatic token injection via interceptors
- Automatic logout on 401 responses
- No sensitive data logged to console in production

## Testing the Implementation

1. Start Laravel backend: `composer run dev`
2. Start mobile app: `cd mobile && npx expo start`
3. Test login with existing user
4. Test registration flow
5. Verify token persistence (close/reopen app)
6. Verify automatic logout on invalid token

## Next Steps (Phase 4)

With authentication complete (T094-T098), the next mobile tasks are:
- T099: Profile creation/edit screen
- T100: Food scanning interface
- T101: Camera integration
- T102+: Feature implementation

## Notes

- Authentication uses Laravel Sanctum tokens
- Protected routes automatically redirect unauthenticated users
- Profile data cached for 5 minutes to reduce API calls
- All TypeScript types align with backend models
