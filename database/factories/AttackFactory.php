<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Attack>
 */
class AttackFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $onsetAt = fake()->dateTimeBetween('-90 days', 'now');
        $durationMinutes = fake()->numberBetween(30, 360); // 30 min to 6 hours

        $painLocations = [
            ['right upper abdomen'],
            ['right upper abdomen', 'back'],
            ['right upper abdomen', 'right shoulder'],
            ['center abdomen', 'chest'],
        ];

        $symptomsOptions = [
            ['severe pain', 'nausea'],
            ['severe pain', 'nausea', 'vomiting'],
            ['severe pain', 'sweating'],
            ['moderate pain', 'nausea', 'bloating'],
            ['severe pain', 'nausea', 'vomiting', 'fever'],
        ];

        return [
            'user_id' => User::factory(),
            'onset_at' => $onsetAt,
            'duration_minutes' => $durationMinutes,
            'pain_intensity' => fake()->numberBetween(4, 10),
            'pain_location' => fake()->randomElement($painLocations),
            'symptoms' => fake()->randomElement($symptomsOptions),
            'medical_care_type' => fake()->randomElement(['none', 'urgent_care', 'er', 'hospital_admission']),
            'diagnosis_received' => fake()->boolean(60),
            'treatment_received' => fake()->boolean(50) ? fake()->randomElement(['pain_medication', 'anti_nausea', 'iv_fluids', 'surgery']) : null,
            'correlated_scans_analyzed' => fake()->boolean(70),
            'suspected_trigger_id' => null, // Will be set via relationship
            'correlation_confidence' => fake()->boolean(50) ? fake()->randomFloat(2, 0.50, 0.95) : null,
            'notes' => fake()->boolean(60) ? fake()->sentence(20) : null,
        ];
    }

    /**
     * Indicate this was a severe attack.
     */
    public function severe(): static
    {
        return $this->state(fn (array $attributes) => [
            'pain_intensity' => fake()->numberBetween(8, 10),
            'duration_minutes' => fake()->numberBetween(180, 480),
            'symptoms' => ['severe pain', 'nausea', 'vomiting', 'sweating'],
            'medical_care_type' => fake()->randomElement(['er', 'hospital_admission']),
        ]);
    }

    /**
     * Indicate this was a mild attack.
     */
    public function mild(): static
    {
        return $this->state(fn (array $attributes) => [
            'pain_intensity' => fake()->numberBetween(4, 6),
            'duration_minutes' => fake()->numberBetween(30, 120),
            'symptoms' => ['moderate pain', 'discomfort'],
            'medical_care_type' => 'none',
        ]);
    }

    /**
     * Indicate this attack led to medical care.
     */
    public function withMedicalCare(): static
    {
        return $this->state(fn (array $attributes) => [
            'medical_care_type' => fake()->randomElement(['urgent_care', 'er', 'hospital_admission']),
            'diagnosis_received' => true,
            'treatment_received' => fake()->randomElement(['pain_medication', 'anti_nausea', 'iv_fluids', 'surgery']),
        ]);
    }
}
