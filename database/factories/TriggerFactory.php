<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Trigger>
 */
class TriggerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $triggers = [
            ['name' => 'Fried Foods', 'category' => 'preparation', 'severity' => 'high'],
            ['name' => 'High-Fat Dairy', 'category' => 'dairy', 'severity' => 'high'],
            ['name' => 'Eggs', 'category' => 'protein', 'severity' => 'moderate'],
            ['name' => 'Fatty Red Meat', 'category' => 'protein', 'severity' => 'high'],
            ['name' => 'Processed Meats', 'category' => 'protein', 'severity' => 'high'],
            ['name' => 'Butter', 'category' => 'dairy', 'severity' => 'moderate'],
            ['name' => 'Cheese', 'category' => 'dairy', 'severity' => 'moderate'],
            ['name' => 'Chocolate', 'category' => 'sweets', 'severity' => 'low'],
            ['name' => 'Spicy Foods', 'category' => 'spices', 'severity' => 'moderate'],
            ['name' => 'Onions', 'category' => 'vegetables', 'severity' => 'low'],
            ['name' => 'Garlic', 'category' => 'vegetables', 'severity' => 'low'],
            ['name' => 'Tomatoes', 'category' => 'vegetables', 'severity' => 'low'],
            ['name' => 'Citrus Fruits', 'category' => 'fruits', 'severity' => 'low'],
            ['name' => 'Avocado', 'category' => 'fruits', 'severity' => 'moderate'],
            ['name' => 'Nuts', 'category' => 'snacks', 'severity' => 'moderate'],
        ];

        $trigger = fake()->randomElement($triggers);

        return [
            'name' => $trigger['name'],
            'category' => $trigger['category'],
            'description' => fake()->sentence(),
            'severity' => $trigger['severity'],
        ];
    }

    /**
     * Create a high severity trigger.
     */
    public function highSeverity(): static
    {
        return $this->state(fn (array $attributes) => [
            'severity' => 'high',
        ]);
    }

    /**
     * Create a low severity trigger.
     */
    public function lowSeverity(): static
    {
        return $this->state(fn (array $attributes) => [
            'severity' => 'low',
        ]);
    }
}
