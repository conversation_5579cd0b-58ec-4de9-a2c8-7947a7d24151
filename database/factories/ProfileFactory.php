<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Profile>
 */
class ProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'age' => fake()->numberBetween(18, 80),
            'sex' => fake()->randomElement(['male', 'female', 'other']),
            'weight_kg' => fake()->randomFloat(2, 45.00, 150.00),
            'health_conditions' => fake()->randomElement([
                ['gallstones', 'high cholesterol'],
                ['gallstones'],
                ['gallstones', 'diabetes'],
                ['gallstones', 'IBS'],
            ]),
            'dietary_preferences' => fake()->randomElement([
                ['low-fat', 'no-fried-foods'],
                ['vegetarian', 'low-fat'],
                ['pescatarian'],
                [],
            ]),
            'allergens' => fake()->randomElement([
                ['dairy', 'eggs'],
                ['nuts'],
                ['shellfish'],
                [],
            ]),
            'completion_percentage' => fake()->numberBetween(0, 100),
            'onboarding_completed' => fake()->boolean(80), // 80% completed
            'onboarding_step' => fake()->numberBetween(0, 5),
        ];
    }

    /**
     * Indicate that the profile is fully completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'completion_percentage' => 100,
            'onboarding_completed' => true,
            'onboarding_step' => 5,
        ]);
    }

    /**
     * Indicate that the profile is incomplete (new user).
     */
    public function incomplete(): static
    {
        return $this->state(fn (array $attributes) => [
            'completion_percentage' => fake()->numberBetween(0, 40),
            'onboarding_completed' => false,
            'onboarding_step' => fake()->numberBetween(0, 2),
        ]);
    }
}
