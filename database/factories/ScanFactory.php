<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Scan>
 */
class ScanFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $mealNames = [
            'Grilled Chicken Salad',
            'Fried Chicken Sandwich',
            'Salmon with Vegetables',
            'Cheeseburger with Fries',
            'Vegetable Stir Fry',
            'Eggs Benedict',
            'Greek Yogurt with Berries',
            'Avocado Toast',
            'Caesar Salad',
            'Margherita Pizza',
        ];

        $ingredients = [
            ['chicken', 'lettuce', 'tomatoes'],
            ['fried chicken', 'bread', 'mayo'],
            ['salmon', 'broccoli', 'carrots'],
            ['beef patty', 'cheese', 'fried potatoes'],
            ['mixed vegetables', 'tofu', 'soy sauce'],
        ];

        $baseScore = fake()->numberBetween(30, 95);
        $adjustedScore = $baseScore + fake()->numberBetween(-10, 10);

        return [
            'user_id' => User::factory(),
            'type' => fake()->randomElement(['photo', 'barcode']),
            'status' => 'completed',
            'meal_name' => fake()->randomElement($mealNames),
            'detected_ingredients' => fake()->randomElement($ingredients),
            'image_path' => 'scans/'.fake()->uuid().'.jpg',
            'barcode' => fake()->boolean(30) ? fake()->ean13() : null,
            'adjusted_score' => $adjustedScore,
            'base_score' => $baseScore,
            'confidence_score' => fake()->randomFloat(2, 0.60, 0.99),
            'personalized_reasoning' => fake()->sentence(15),
            'outcome' => fake()->randomElement(['ate', 'avoided', null]),
            'reaction_severity' => fake()->boolean(30) ? fake()->randomElement(['none', 'mild', 'moderate', 'severe']) : null,
            'analyzed_at' => fake()->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Indicate the scan is still being analyzed.
     */
    public function analyzing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'analyzing',
            'analyzed_at' => null,
            'adjusted_score' => null,
            'base_score' => null,
            'confidence_score' => null,
            'personalized_reasoning' => null,
        ]);
    }

    /**
     * Indicate the scan has been completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'analyzed_at' => now(),
        ]);
    }

    /**
     * Indicate the scan resulted in a safe meal.
     */
    public function safe(): static
    {
        return $this->state(fn (array $attributes) => [
            'adjusted_score' => fake()->numberBetween(80, 100),
            'base_score' => fake()->numberBetween(80, 100),
        ]);
    }

    /**
     * Indicate the scan resulted in a dangerous meal.
     */
    public function dangerous(): static
    {
        return $this->state(fn (array $attributes) => [
            'adjusted_score' => fake()->numberBetween(10, 40),
            'base_score' => fake()->numberBetween(10, 40),
        ]);
    }

    /**
     * Indicate the user ate this meal.
     */
    public function consumed(): static
    {
        return $this->state(fn (array $attributes) => [
            'outcome' => 'ate',
        ]);
    }
}
