<?php

namespace Database\Factories;

use App\Models\Trigger;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PatternSuggestion>
 */
class PatternSuggestionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $triggerNames = [
            'Fried Foods',
            'High-Fat Dairy',
            'Fatty Meats',
            'Eggs',
            'Chocolate',
            'Spicy Foods',
            'Caffeine',
            'Alcohol',
        ];

        $correlationCount = fake()->numberBetween(3, 8);
        $matchWeight = fake()->randomFloat(2, 60, 95);
        $consistency = fake()->randomFloat(2, 65, 95);
        $recency = fake()->randomFloat(2, 50, 90);

        // Confidence formula from clarifications.md Q1
        $confidence = ($correlationCount * 0.25) + ($matchWeight * 0.20) + ($consistency * 0.30) + ($recency * 0.25);

        return [
            'user_id' => User::factory(),
            'trigger_id' => null, // Will be set when confirmed
            'suspected_trigger_name' => fake()->randomElement($triggerNames),
            'detected_at' => fake()->dateTimeBetween('-7 days', 'now'),
            'correlation_count' => $correlationCount,
            'match_weight' => $matchWeight,
            'consistency_score' => $consistency,
            'recency_score' => $recency,
            'confidence_score' => round($confidence, 2),
            'evidence' => [
                'scans' => [],
                'attacks' => [],
                'avg_time_gap' => fake()->numberBetween(3, 8),
            ],
            'status' => 'pending',
        ];
    }

    /**
     * Indicate the suggestion is pending (default state).
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate the suggestion has been confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'confirmed',
            'trigger_id' => Trigger::factory(),
        ]);
    }

    /**
     * Indicate the suggestion has been rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
        ]);
    }
}
