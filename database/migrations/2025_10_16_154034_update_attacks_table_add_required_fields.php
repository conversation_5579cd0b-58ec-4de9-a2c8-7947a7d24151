<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attacks', function (Blueprint $table) {
            // Rename occurred_at to onset_at
            $table->renameColumn('occurred_at', 'onset_at');

            // Remove old severity column
            $table->dropColumn('severity');
            $table->dropColumn('resolved_at');

            // Add required Phase 3 fields
            $table->integer('duration_minutes')->nullable()->after('onset_at');
            $table->integer('pain_intensity')->nullable()->after('duration_minutes');
            $table->json('pain_location')->nullable()->after('pain_intensity');

            // medical_care_type as string (not enum for flexibility)
            $table->string('medical_care_type', 30)->nullable()->after('symptoms');
            $table->text('diagnosis_received')->nullable()->after('medical_care_type');
            $table->text('treatment_received')->nullable()->after('diagnosis_received');

            // Pattern detection fields
            $table->boolean('correlated_scans_analyzed')->default(false)->after('treatment_received');
            $table->foreignId('suspected_trigger_id')->nullable()->constrained('triggers')->nullOnDelete()->after('correlated_scans_analyzed');
            $table->decimal('correlation_confidence', 5, 2)->nullable()->after('suspected_trigger_id');

            // Add indexes
            $table->index('onset_at');
            $table->index('suspected_trigger_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attacks', function (Blueprint $table) {
            $table->renameColumn('onset_at', 'occurred_at');

            $table->dropColumn([
                'duration_minutes',
                'pain_intensity',
                'pain_location',
                'medical_care_type',
                'diagnosis_received',
                'treatment_received',
                'correlated_scans_analyzed',
                'suspected_trigger_id',
                'correlation_confidence',
            ]);

            $table->enum('severity', ['mild', 'moderate', 'severe'])->after('occurred_at');
            $table->timestamp('resolved_at')->nullable()->after('notes');
        });
    }
};
