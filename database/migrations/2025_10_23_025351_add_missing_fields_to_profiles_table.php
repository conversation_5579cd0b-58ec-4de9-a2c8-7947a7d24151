<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Add missing fields to profiles table that are expected during onboarding.
     */
    public function up(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            // Add gallbladder_status field (mentioned in data-model.md lines 91-98)
            $table->enum('gallbladder_status', ['has_gallbladder', 'post_cholecystectomy'])
                ->nullable()
                ->after('sex');

            // Add severity_level field for overall condition severity
            $table->enum('severity_level', ['mild', 'moderate', 'severe'])
                ->default('moderate')
                ->after('gallbladder_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            $table->dropColumn(['gallbladder_status', 'severity_level']);
        });
    }
};
