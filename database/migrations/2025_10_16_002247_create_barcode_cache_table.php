<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('barcode_cache', function (Blueprint $table) {
            $table->id();
            $table->string('barcode')->unique();
            $table->string('product_name')->nullable();
            $table->text('ingredients_text')->nullable();
            $table->json('ingredients_list')->nullable();
            $table->json('allergens')->nullable();
            $table->decimal('fat_100g', 8, 2)->nullable();
            $table->decimal('saturated_fat_100g', 8, 2)->nullable();
            $table->string('image_url', 500)->nullable();
            $table->timestamps();

            $table->index('barcode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('barcode_cache');
    }
};
