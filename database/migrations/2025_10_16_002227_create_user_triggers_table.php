<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_triggers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('trigger_id')->constrained()->onDelete('cascade');
            $table->decimal('confidence_score', 5, 2)->default(0.00);
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('denied_at')->nullable();
            $table->timestamp('last_occurred_at')->nullable();
            $table->integer('occurrences_count')->default(0);
            $table->timestamps();

            $table->unique(['user_id', 'trigger_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_triggers');
    }
};
