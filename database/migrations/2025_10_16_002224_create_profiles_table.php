<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->cascadeOnDelete();

            // Demographics
            $table->unsignedTinyInteger('age')->nullable();
            $table->enum('sex', ['male', 'female', 'other'])->nullable();
            $table->decimal('weight_kg', 5, 2)->nullable();

            // Health conditions (JSON array of strings)
            $table->json('health_conditions')->nullable();

            // Dietary preferences (JSON array of strings)
            $table->json('dietary_preferences')->nullable();

            // Known allergens (JSON array of strings)
            $table->json('allergens')->nullable();

            // Profile completion percentage (0-100)
            $table->unsignedTinyInteger('completion_percentage')->default(0);

            // Onboarding state
            $table->boolean('onboarding_completed')->default(false);
            $table->unsignedTinyInteger('onboarding_step')->default(0);

            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('user_id');
            $table->index('onboarding_completed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('profiles');
    }
};
