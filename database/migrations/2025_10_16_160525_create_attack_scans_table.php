<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attack_scans', function (Blueprint $table) {
            $table->foreignId('attack_id')->constrained()->onDelete('cascade');
            $table->foreignId('scan_id')->constrained()->onDelete('cascade');
            $table->integer('hours_before_attack')->nullable();
            $table->decimal('match_weight', 5, 2)->default(0.00);
            $table->timestamps();

            $table->primary(['attack_id', 'scan_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attack_scans');
    }
};
