<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Subscription fields (denormalized for fast access)
            $table->string('subscription_tier')->default('free')->after('email_verified_at');
            $table->boolean('trial_used')->default(false)->after('subscription_tier');
            $table->timestamp('subscription_expires_at')->nullable()->after('trial_used');

            // Stripe fields (Laravel Cashier)
            $table->string('stripe_id')->nullable()->unique()->after('subscription_expires_at');
            $table->string('pm_type')->nullable()->after('stripe_id');
            $table->string('pm_last_four', 4)->nullable()->after('pm_type');
            $table->timestamp('trial_ends_at')->nullable()->after('pm_last_four');

            // Sync metadata
            $table->timestamp('last_synced_at')->nullable()->after('trial_ends_at');

            // Soft deletes
            $table->softDeletes();

            // Indexes
            $table->index('subscription_tier');
            $table->index('stripe_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'subscription_tier',
                'trial_used',
                'subscription_expires_at',
                'stripe_id',
                'pm_type',
                'pm_last_four',
                'trial_ends_at',
                'last_synced_at',
                'deleted_at',
            ]);
        });
    }
};
