<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scans', function (Blueprint $table) {
            // Change base_score and adjusted_score from integer to decimal
            // to match confidence_score and allow decimal values from AI
            $table->decimal('base_score', 5, 2)->nullable()->change();
            $table->decimal('adjusted_score', 5, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scans', function (Blueprint $table) {
            // Revert back to integer
            $table->integer('base_score')->nullable()->change();
            $table->integer('adjusted_score')->nullable()->change();
        });
    }
};
