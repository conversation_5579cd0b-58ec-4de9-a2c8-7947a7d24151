<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attack_scans', function (Blueprint $table) {
            // Change hours_before_attack from integer to decimal to support fractional hours
            $table->decimal('hours_before_attack', 5, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attack_scans', function (Blueprint $table) {
            // Revert back to integer
            $table->integer('hours_before_attack')->nullable()->change();
        });
    }
};
