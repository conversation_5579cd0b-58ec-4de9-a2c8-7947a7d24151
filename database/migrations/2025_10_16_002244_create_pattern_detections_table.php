<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pattern_detections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('trigger_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamp('detected_at');
            $table->integer('correlation_count')->default(0);
            $table->decimal('match_weight', 5, 2)->default(0.00);
            $table->decimal('consistency_score', 5, 2)->default(0.00);
            $table->decimal('recency_score', 5, 2)->default(0.00);
            $table->decimal('confidence_score', 5, 2)->default(0.00);
            $table->enum('status', ['pending', 'confirmed', 'denied'])->default('pending');
            $table->timestamps();

            $table->index('user_id');
            $table->index('trigger_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pattern_detections');
    }
};
