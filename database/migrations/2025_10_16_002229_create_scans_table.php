<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['photo', 'barcode']);
            $table->string('image_path', 500)->nullable();
            $table->string('barcode', 50)->nullable();
            $table->string('meal_name')->nullable();
            $table->json('detected_ingredients')->nullable();
            $table->integer('base_score')->nullable();
            $table->integer('adjusted_score')->nullable();
            $table->decimal('confidence_score', 5, 2)->nullable();
            $table->text('personalized_reasoning')->nullable();
            $table->enum('status', ['pending', 'analyzing', 'completed', 'failed'])->default('pending');
            $table->timestamp('analyzed_at')->nullable();
            $table->enum('outcome', ['ate', 'avoided'])->nullable();
            $table->enum('reaction_severity', ['none', 'mild', 'moderate', 'severe'])->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index('user_id');
            $table->index('status');
            $table->index('outcome');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scans');
    }
};
