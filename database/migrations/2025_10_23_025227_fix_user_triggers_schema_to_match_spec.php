<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration restructures user_triggers from a pivot table to a standalone table
     * matching the data-model.md specification (lines 120-158).
     */
    public function up(): void
    {
        // Drop the old pivot table structure
        Schema::dropIfExists('user_triggers');

        // Create new user_triggers table as standalone (not pivot)
        Schema::create('user_triggers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();

            // Trigger information (stored per user, not referenced from triggers table)
            $table->string('trigger_name');
            $table->enum('severity', ['low', 'moderate', 'high'])->default('moderate');

            // Identification source
            $table->enum('identification_source', ['user_input', 'pattern_detected', 'attack_correlated'])
                ->default('user_input');
            $table->decimal('confidence_score', 5, 2)->default(0.00);

            // Attack correlation statistics
            $table->integer('attack_correlation_count')->default(0);
            $table->integer('total_exposures')->default(0);
            $table->timestamp('last_attack_date')->nullable();

            // User notes
            $table->text('notes')->nullable();

            // Status tracking
            $table->enum('status', ['active', 'rejected', 'pending_confirmation'])->default('active');
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('denied_at')->nullable();
            $table->integer('rejection_count')->default(0);

            $table->timestamps();

            // Indexes
            $table->unique(['user_id', 'trigger_name']);
            $table->index(['user_id', 'severity']);
            $table->index(['user_id', 'status']);
            $table->index('identification_source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_triggers');

        // Recreate the old pivot structure
        Schema::create('user_triggers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('trigger_id')->constrained()->onDelete('cascade');
            $table->decimal('confidence_score', 5, 2)->default(0.00);
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('denied_at')->nullable();
            $table->timestamp('last_occurred_at')->nullable();
            $table->integer('occurrences_count')->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'trigger_id']);
        });
    }
};
