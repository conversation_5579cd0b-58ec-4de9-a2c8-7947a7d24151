<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pattern_detections', function (Blueprint $table) {
            // Add suspected_trigger_name (nullable for when trigger_id is set)
            $table->string('suspected_trigger_name')->nullable()->after('trigger_id');

            // Add evidence JSON field to store scan/attack correlations
            $table->json('evidence')->nullable()->after('confidence_score');

            // Update status enum to include 'rejected' instead of 'denied'
            $table->dropColumn('status');
        });

        Schema::table('pattern_detections', function (Blueprint $table) {
            $table->enum('status', ['pending', 'confirmed', 'rejected'])->default('pending')->after('evidence');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pattern_detections', function (Blueprint $table) {
            $table->dropColumn(['suspected_trigger_name', 'evidence']);
            $table->dropColumn('status');
        });

        Schema::table('pattern_detections', function (Blueprint $table) {
            $table->enum('status', ['pending', 'confirmed', 'denied'])->default('pending');
        });
    }
};
