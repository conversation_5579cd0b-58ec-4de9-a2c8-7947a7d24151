<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scan_ingredients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('scan_id')->constrained()->onDelete('cascade');
            $table->string('ingredient_name');
            $table->foreignId('trigger_id')->nullable()->constrained()->onDelete('set null');
            $table->string('category')->nullable();
            $table->enum('risk_level', ['safe', 'low', 'moderate', 'high'])->nullable();
            $table->enum('matched_by', ['exact', 'category', 'fuzzy'])->nullable();
            $table->timestamps();

            $table->index('scan_id');
            $table->index('trigger_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scan_ingredients');
    }
};
