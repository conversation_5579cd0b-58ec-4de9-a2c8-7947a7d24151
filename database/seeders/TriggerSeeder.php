<?php

namespace Database\Seeders;

use App\Models\Trigger;
use Illuminate\Database\Seeder;

class TriggerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $triggers = [
            // Fats (High severity - most common triggers)
            ['name' => 'Fried Foods', 'category' => 'fats', 'severity' => 'high', 'description' => 'Deep fried or pan fried foods'],
            ['name' => 'Fatty Meats', 'category' => 'fats', 'severity' => 'high', 'description' => 'Bacon, sausage, ribs, fatty cuts'],
            ['name' => 'High-Fat Dairy', 'category' => 'dairy', 'severity' => 'high', 'description' => 'Whole milk, cream, butter, cheese'],
            ['name' => 'Oils & Butter', 'category' => 'fats', 'severity' => 'high', 'description' => 'Cooking oils, butter, lard, shortening'],

            // Dairy (Moderate to high)
            ['name' => 'Full-Fat Cheese', 'category' => 'dairy', 'severity' => 'high', 'description' => 'Cheddar, brie, cream cheese'],
            ['name' => 'Ice Cream', 'category' => 'dairy', 'severity' => 'moderate', 'description' => 'Full-fat ice cream and frozen desserts'],
            ['name' => 'Cream', 'category' => 'dairy', 'severity' => 'high', 'description' => 'Heavy cream, sour cream, half and half'],

            // Proteins
            ['name' => 'Red Meat', 'category' => 'protein', 'severity' => 'moderate', 'description' => 'Beef, lamb, pork'],
            ['name' => 'Processed Meats', 'category' => 'protein', 'severity' => 'high', 'description' => 'Deli meats, hot dogs, sausages'],
            ['name' => 'Eggs', 'category' => 'protein', 'severity' => 'low', 'description' => 'Whole eggs, especially yolks'],

            // Sweets & Desserts
            ['name' => 'Chocolate', 'category' => 'sweets', 'severity' => 'moderate', 'description' => 'Chocolate bars, cocoa products'],
            ['name' => 'Pastries', 'category' => 'sweets', 'severity' => 'moderate', 'description' => 'Donuts, croissants, danish'],
            ['name' => 'Rich Desserts', 'category' => 'sweets', 'severity' => 'moderate', 'description' => 'Cakes, pies, cookies with high fat'],

            // Vegetables (potential triggers)
            ['name' => 'Onions', 'category' => 'vegetables', 'severity' => 'low', 'description' => 'Raw or cooked onions'],
            ['name' => 'Tomatoes', 'category' => 'vegetables', 'severity' => 'low', 'description' => 'Fresh tomatoes, tomato sauce'],
            ['name' => 'Peppers', 'category' => 'vegetables', 'severity' => 'low', 'description' => 'Bell peppers, hot peppers'],
            ['name' => 'Cabbage Family', 'category' => 'vegetables', 'severity' => 'low', 'description' => 'Broccoli, cauliflower, brussels sprouts'],

            // Grains & Carbs
            ['name' => 'Refined Carbs', 'category' => 'grains', 'severity' => 'low', 'description' => 'White bread, white rice, pasta'],

            // Beverages
            ['name' => 'Alcohol', 'category' => 'beverages', 'severity' => 'moderate', 'description' => 'Beer, wine, liquor'],
            ['name' => 'Coffee', 'category' => 'beverages', 'severity' => 'low', 'description' => 'Caffeinated coffee'],
            ['name' => 'Carbonated Drinks', 'category' => 'beverages', 'severity' => 'low', 'description' => 'Soda, sparkling water'],

            // Nuts & Seeds
            ['name' => 'Nuts', 'category' => 'nuts_seeds', 'severity' => 'moderate', 'description' => 'Peanuts, almonds, cashews'],
            ['name' => 'Nut Butters', 'category' => 'nuts_seeds', 'severity' => 'moderate', 'description' => 'Peanut butter, almond butter'],

            // Spicy Foods
            ['name' => 'Spicy Foods', 'category' => 'spices', 'severity' => 'moderate', 'description' => 'Hot peppers, spicy sauces, curry'],

            // Fast Food
            ['name' => 'Fast Food', 'category' => 'processed', 'severity' => 'high', 'description' => 'Burgers, fries, fried chicken'],
            ['name' => 'Pizza', 'category' => 'processed', 'severity' => 'moderate', 'description' => 'Especially with fatty toppings'],

            // Condiments
            ['name' => 'Mayonnaise', 'category' => 'condiments', 'severity' => 'moderate', 'description' => 'Full-fat mayonnaise'],
            ['name' => 'Salad Dressings', 'category' => 'condiments', 'severity' => 'moderate', 'description' => 'Creamy, high-fat dressings'],
        ];

        foreach ($triggers as $trigger) {
            Trigger::firstOrCreate(
                ['name' => $trigger['name'], 'category' => $trigger['category']],
                $trigger
            );
        }
    }
}
