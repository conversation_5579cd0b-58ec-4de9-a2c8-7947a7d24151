<?php

use App\Models\Scan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

use function Pest\Laravel\getJson;

uses(RefreshDatabase::class);

// T126: Scan History Tests

test('authenticated users can list their scan history with pagination', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 15 scans
    Scan::factory()->count(15)->create([
        'user_id' => $user->id,
        'status' => 'completed',
    ]);

    $response = getJson('/api/history?per_page=10', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk()
        ->assertJsonStructure([
            'scans' => [
                'data' => [
                    '*' => [
                        'id',
                        'meal_name',
                        'safety_score',
                        'meal_type',
                        'created_at',
                        'image_url',
                    ],
                ],
                'next_cursor',
            ],
        ]);

    // Verify pagination
    $data = $response->json('scans.data');
    expect(count($data))->toBe(10);
    expect($response->json('scans.next_cursor'))->not->toBeNull();
});

test('scan history can be filtered by date range', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create scans from different dates
    Scan::factory()->create([
        'user_id' => $user->id,
        'created_at' => Carbon::now()->subDays(10),
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'created_at' => Carbon::now()->subDays(5),
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'created_at' => Carbon::now()->subDays(2),
        'status' => 'completed',
    ]);

    // Filter for last 7 days
    $response = getJson('/api/history?date_from='.Carbon::now()->subDays(7)->toDateString(), [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // Should only return 2 scans (5 days and 2 days ago)
    expect(count($scans))->toBe(2);

    // All scans should be within the date range
    foreach ($scans as $scan) {
        $createdAt = Carbon::parse($scan['created_at']);
        expect($createdAt->isAfter(Carbon::now()->subDays(7)))->toBeTrue();
    }
});

test('scan history can be filtered by safety score range', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create scans with different safety scores
    Scan::factory()->create([
        'user_id' => $user->id,
        'adjusted_score' => 25,
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'adjusted_score' => 65,
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'adjusted_score' => 85,
        'status' => 'completed',
    ]);

    // Filter for moderate-high safety scores (50-100)
    $response = getJson('/api/history?safety_score_min=50&safety_score_max=100', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // Should return 2 scans (65 and 85)
    expect(count($scans))->toBe(2);

    // All scans should be within safety score range
    foreach ($scans as $scan) {
        expect($scan['safety_score'])->toBeGreaterThanOrEqual(50);
        expect($scan['safety_score'])->toBeLessThanOrEqual(100);
    }
});

test('scan history can be filtered by meal type', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create scans with different meal types
    Scan::factory()->create([
        'user_id' => $user->id,
        'meal_type' => 'breakfast',
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'meal_type' => 'lunch',
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'meal_type' => 'dinner',
        'status' => 'completed',
    ]);

    // Filter for lunch meals only
    $response = getJson('/api/history?meal_type=lunch', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // Should return 1 scan
    expect(count($scans))->toBe(1);
    expect($scans[0]['meal_type'])->toBe('lunch');
});

test('scan history can be searched by meal name', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create scans with different meal names
    Scan::factory()->create([
        'user_id' => $user->id,
        'meal_name' => 'Grilled Chicken Salad',
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'meal_name' => 'Pasta Carbonara',
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'meal_name' => 'Chicken Soup',
        'status' => 'completed',
    ]);

    // Search for "chicken"
    $response = getJson('/api/history?search=chicken', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // Should return 2 scans (Grilled Chicken Salad and Chicken Soup)
    expect(count($scans))->toBe(2);

    foreach ($scans as $scan) {
        expect(strtolower($scan['meal_name']))->toContain('chicken');
    }
});

test('scan history only returns completed scans', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create scans with different statuses
    Scan::factory()->create([
        'user_id' => $user->id,
        'status' => 'analyzing',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'status' => 'completed',
    ]);

    Scan::factory()->create([
        'user_id' => $user->id,
        'status' => 'failed',
    ]);

    $response = getJson('/api/history', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // Should only return completed scans
    expect(count($scans))->toBe(1);
    expect($scans[0]['status'])->toBe('completed');
});

test('users can only see their own scan history', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    $token1 = $user1->createToken('mobile-app')->plainTextToken;

    // Create scans for both users
    Scan::factory()->count(3)->create(['user_id' => $user1->id]);
    Scan::factory()->count(5)->create(['user_id' => $user2->id]);

    $response = getJson('/api/history', [
        'Authorization' => "Bearer {$token1}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // User 1 should only see their 3 scans
    expect(count($scans))->toBe(3);

    // Verify isolation by checking scan IDs match what we created for user1
    $user1ScanIds = Scan::where('user_id', $user1->id)->pluck('id')->toArray();
    foreach ($scans as $scan) {
        expect($user1ScanIds)->toContain($scan['id']);
    }
});

test('scan history returns results in descending chronological order', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create scans with different timestamps
    $scan1 = Scan::factory()->create([
        'user_id' => $user->id,
        'created_at' => Carbon::now()->subDays(5),
        'status' => 'completed',
    ]);

    $scan2 = Scan::factory()->create([
        'user_id' => $user->id,
        'created_at' => Carbon::now()->subDays(1),
        'status' => 'completed',
    ]);

    $scan3 = Scan::factory()->create([
        'user_id' => $user->id,
        'created_at' => Carbon::now(),
        'status' => 'completed',
    ]);

    $response = getJson('/api/history', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $scans = $response->json('scans.data');

    // Most recent scan should be first
    expect($scans[0]['id'])->toBe($scan3->id);
    expect($scans[1]['id'])->toBe($scan2->id);
    expect($scans[2]['id'])->toBe($scan1->id);
});

test('unauthenticated users cannot access scan history', function () {
    $response = getJson('/api/history');

    $response->assertUnauthorized();
});

test('scan history supports cursor-based pagination for infinite scroll', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 25 scans with different timestamps to ensure proper cursor pagination
    for ($i = 0; $i < 25; $i++) {
        Scan::factory()->create([
            'user_id' => $user->id,
            'status' => 'completed',
            'created_at' => now()->subMinutes(24 - $i), // Oldest to newest
        ]);
    }

    // Get first page
    $response1 = getJson('/api/history?per_page=10', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response1->assertOk();

    $page1 = $response1->json('scans.data');
    $cursor1 = $response1->json('scans.next_cursor');

    expect(count($page1))->toBe(10);
    expect($cursor1)->not->toBeNull();

    // Get second page using cursor
    $response2 = getJson("/api/history?per_page=10&cursor={$cursor1}", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response2->assertOk();

    $page2 = $response2->json('scans.data');

    expect(count($page2))->toBe(10);

    // Verify no duplicates between pages
    $page1Ids = array_column($page1, 'id');
    $page2Ids = array_column($page2, 'id');

    expect(array_intersect($page1Ids, $page2Ids))->toBeEmpty();
});
