<?php

use App\Models\Scan;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\postJson;

// T211: Quick Scan Tests - Multiple scans in quick succession (< 30 seconds between), all complete < 4 seconds per FR-023

test('users can perform multiple scans in quick succession', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'premium',
    ]);
    $token = $user->createToken('mobile-app')->plainTextToken;

    $scanTimes = [];

    // Perform 5 scans in rapid succession
    for ($i = 0; $i < 5; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg", 800, 600);

        $startTime = microtime(true);

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $endTime = microtime(true);
        $scanTimes[] = $endTime - $startTime;

        $response->assertCreated()
            ->assertJsonStructure([
                'scan' => [
                    'id',
                    'status',
                    'meal_type',
                ],
            ]);

        // Small delay to simulate realistic user behavior (< 30 seconds requirement)
        if ($i < 4) {
            usleep(100000); // 0.1 seconds between scans
        }
    }

    // Verify all 5 scans were created
    expect(Scan::where('user_id', $user->id)->count())->toBe(5);

    // Verify all scan uploads completed quickly (< 4 seconds per FR-023)
    // Note: This tests upload time, not full AI analysis time
    foreach ($scanTimes as $index => $time) {
        expect($time)->toBeLessThan(4.0, "Scan {$index} took {$time}s, should be < 4s");
    }

    // Verify average time is significantly faster
    $averageTime = array_sum($scanTimes) / count($scanTimes);
    expect($averageTime)->toBeLessThan(2.0, "Average scan time {$averageTime}s should be < 2s");
});

test('rapid scans do not interfere with each other', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $scanIds = [];

    // Perform 3 scans rapidly
    for ($i = 0; $i < 3; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg", 800, 600);

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => ['breakfast', 'lunch', 'dinner'][$i],
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $scanIds[] = $response->json('scan.id');
    }

    // Verify each scan has unique ID and correct meal type
    expect(count(array_unique($scanIds)))->toBe(3, 'All scan IDs should be unique');

    $scans = Scan::whereIn('id', $scanIds)->get();
    expect($scans->pluck('meal_type')->toArray())->toMatchArray(['breakfast', 'lunch', 'dinner']);
});

test('quick succession scans respect daily limit for free tier', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'free',
    ]);
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Perform 3 scans (free tier limit)
    for ($i = 0; $i < 3; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // 4th scan should be rejected
    $photo = UploadedFile::fake()->image('meal4.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden()
        ->assertJson([
            'message' => 'Daily limit reached. Upgrade to Premium for unlimited scans.',
        ]);
});

test('premium users can perform unlimited quick scans', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'premium',
    ]);
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Perform 10 scans (more than free tier limit)
    for ($i = 0; $i < 10; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // Verify all 10 scans were created
    expect(Scan::where('user_id', $user->id)->count())->toBe(10);
});

test('scan response time is consistently fast', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $responseTimes = [];

    // Measure response time for 10 scans
    for ($i = 0; $i < 10; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $startTime = microtime(true);

        postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $endTime = microtime(true);
        $responseTimes[] = $endTime - $startTime;
    }

    // Calculate p95 (95th percentile)
    sort($responseTimes);
    $p95Index = (int) ceil(0.95 * count($responseTimes)) - 1;
    $p95Time = $responseTimes[$p95Index];

    // Verify p95 is < 4 seconds per FR-023
    expect($p95Time)->toBeLessThan(4.0, "p95 response time {$p95Time}s should be < 4s");
});
