<?php

use App\Models\User;

use function Pest\Laravel\getJson;

// T172: Emergency Support Tests

test('emergency support displays SEEK MEDICAL CARE NOW message prominently', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/emergency/support', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk()
        ->assertJsonStructure([
            'message',
            'actions' => [
                '*' => ['type', 'label', 'action'],
            ],
        ])
        ->assertJsonFragment([
            'message' => 'SEEK MEDICAL CARE NOW',
        ]);

    // Verify message contains critical guidance
    $json = $response->json();
    expect($json['message'])->toContain('SEEK MEDICAL CARE');
});

test('emergency support provides ER and 911 action buttons', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/emergency/support', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk()
        ->assertJsonStructure([
            'actions' => [
                '*' => ['type', 'label', 'action'],
            ],
        ]);

    $actions = $response->json('actions');

    // Should have Call 911 button
    $call911 = collect($actions)->firstWhere('type', 'call_911');
    expect($call911)->not->toBeNull();
    expect($call911['label'])->toContain('911');

    // Should have Find ER button
    $findER = collect($actions)->firstWhere('type', 'find_er');
    expect($findER)->not->toBeNull();
    expect($findER['label'])->toContain('ER');
});

test('emergency support never discourages ER visits', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/emergency/support', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    $json = $response->json();
    $message = strtolower($json['message']);

    // Should not contain any discouraging language
    $discouragingPhrases = [
        'wait',
        'avoid',
        'try this first',
        'instead of going',
        'before calling',
        'might not need',
    ];

    foreach ($discouragingPhrases as $phrase) {
        expect($message)->not->toContain($phrase);
    }
});

test('emergency support requires authentication', function () {
    $response = getJson('/api/emergency/support');

    $response->assertUnauthorized();
});
