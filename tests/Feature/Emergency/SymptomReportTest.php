<?php

use App\Models\Attack;
use App\Models\Scan;
use App\Models\User;

use function Pest\Laravel\postJson;

// T173: Symptom Report Tests

test('symptom report generates PDF with symptoms and recent meals', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create an attack with symptoms
    $attack = Attack::factory()->for($user)->create([
        'onset_at' => now()->subHours(1),
        'pain_intensity' => 8,
        'symptoms' => ['severe pain', 'nausea', 'vomiting'],
        'medical_care_type' => 'emergency_room',
    ]);

    // Create recent scans (within 8 hours)
    Scan::factory()->for($user)->count(3)->create([
        'created_at' => now()->subHours(4),
        'status' => 'completed',
        'meal_name' => 'Test Meal',
    ]);

    $response = postJson('/api/emergency/report', [
        'attack_id' => $attack->id,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk()
        ->assertJsonStructure([
            'report_url',
            'report_format',
        ]);

    // Verify PDF was generated
    expect($response->json('report_format'))->toBe('pdf');
    expect($response->json('report_url'))->toContain('.pdf');
});

test('symptom report includes recent meals from last 8 hours', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attack = Attack::factory()->for($user)->create([
        'onset_at' => now(),
        'pain_intensity' => 7,
    ]);

    // Create scans within 8-hour window
    $recentScan = Scan::factory()->for($user)->create([
        'created_at' => now()->subHours(4),
        'meal_name' => 'Recent Meal',
        'status' => 'completed',
    ]);

    // Create scan outside 8-hour window (should not be included)
    $oldScan = Scan::factory()->for($user)->create([
        'created_at' => now()->subHours(10),
        'meal_name' => 'Old Meal',
        'status' => 'completed',
    ]);

    $response = postJson('/api/emergency/report', [
        'attack_id' => $attack->id,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    // Verify response includes recent scan data reference
    $json = $response->json();
    expect($json)->toHaveKey('report_url');
});

test('symptom report is shareable', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attack = Attack::factory()->for($user)->create([
        'onset_at' => now(),
        'pain_intensity' => 6,
    ]);

    $response = postJson('/api/emergency/report', [
        'attack_id' => $attack->id,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk();

    // Verify report URL is accessible (publicly shareable)
    $reportUrl = $response->json('report_url');
    expect($reportUrl)->toBeString();
    expect($reportUrl)->not->toBeEmpty();
});

test('symptom report requires authentication', function () {
    $user = User::factory()->create();
    $attack = Attack::factory()->for($user)->create();

    $response = postJson('/api/emergency/report', [
        'attack_id' => $attack->id,
    ]);

    $response->assertUnauthorized();
});

test('symptom report requires valid attack_id', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/emergency/report', [
        'attack_id' => 99999, // Non-existent attack
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    // Validation returns 422 for invalid attack_id (exists rule fails)
    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['attack_id']);
});

test('users can only generate reports for their own attacks', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $token1 = $user1->createToken('mobile-app')->plainTextToken;

    // Create attack belonging to user2
    $attack = Attack::factory()->for($user2)->create();

    $response = postJson('/api/emergency/report', [
        'attack_id' => $attack->id,
    ], [
        'Authorization' => "Bearer {$token1}",
    ]);

    $response->assertForbidden();
});
