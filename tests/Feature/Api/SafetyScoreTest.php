<?php

use App\Models\Trigger;
use App\Models\User;
use App\Models\UserTrigger;
use App\Services\PersonalizationEngine;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Helper function to create confirmed user trigger
function confirmTrigger(User $user, string $triggerName, string $severity = 'moderate'): UserTrigger
{
    return UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => $triggerName,
        'severity' => $severity,
        'identification_source' => 'user_input',
        'confidence_score' => 100.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);
}

// Helper function to create denied user trigger
function denyTrigger(User $user, string $triggerName, string $severity = 'moderate'): UserTrigger
{
    return UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => $triggerName,
        'severity' => $severity,
        'identification_source' => 'user_input',
        'confidence_score' => 0.00,
        'status' => 'rejected',
        'denied_at' => now(),
    ]);
}

// T050: Safety Score Calculation Tests

test('base score starts at 100 for meals with no triggers', function () {
    $user = User::factory()->create();
    $engine = new PersonalizationEngine;

    $ingredients = ['grilled chicken', 'lettuce', 'tomato', 'cucumber'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    expect($result['safety_score'])->toBe(100);
    expect($result['trigger_warnings'])->toBeArray()->toBeEmpty();
});

test('low severity trigger deducts 15 points', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'Tomatoes', 'low');

    $engine = new PersonalizationEngine;
    $ingredients = ['grilled chicken', 'tomatoes', 'lettuce'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    expect($result['safety_score'])->toBe(85); // 100 - 15
    expect($result['trigger_warnings'])->toHaveCount(1);
    expect($result['trigger_warnings'][0]['trigger'])->toBe('Tomatoes');
    expect($result['trigger_warnings'][0]['severity'])->toBe('low');
});

test('moderate severity trigger deducts 25 points', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'Chocolate', 'moderate');

    $engine = new PersonalizationEngine;
    $ingredients = ['chocolate chips', 'flour', 'sugar'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    expect($result['safety_score'])->toBe(75); // 100 - 25
    expect($result['trigger_warnings'])->toHaveCount(1);
    expect($result['trigger_warnings'][0]['severity'])->toBe('moderate');
});

test('high severity trigger deducts 40 points', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'Fried Foods', 'high');

    $engine = new PersonalizationEngine;
    $ingredients = ['french fries', 'salt'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    expect($result['safety_score'])->toBe(60); // 100 - 40
    expect($result['trigger_warnings'])->toHaveCount(1);
    expect($result['trigger_warnings'][0]['severity'])->toBe('high');
});

test('multiple triggers compound deductions', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'Fried Foods', 'high');
    confirmTrigger($user, 'High-Fat Dairy', 'high');
    confirmTrigger($user, 'Onions', 'low');

    $engine = new PersonalizationEngine;
    $ingredients = ['fried cheese sticks', 'onions', 'ranch dressing'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    // 100 - 40 (fried) - 40 (dairy) - 15 (onions) = 5
    expect($result['safety_score'])->toBe(5);
    expect($result['trigger_warnings'])->toHaveCount(3);
});

test('safety score cannot go below zero', function () {
    $user = User::factory()->create();

    // Create 5 high severity triggers
    for ($i = 1; $i <= 5; $i++) {
        confirmTrigger($user, "Trigger {$i}", 'high');
    }

    $engine = new PersonalizationEngine;
    $ingredients = ['trigger 1', 'trigger 2', 'trigger 3', 'trigger 4', 'trigger 5'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    // 5 high triggers = 200 points deducted, but score should floor at 0
    expect($result['safety_score'])->toBe(0);
});

test('allergen override sets score to zero', function () {
    $user = User::factory()
        ->has(\App\Models\Profile::factory()->state([
            'allergens' => ['peanuts', 'shellfish'],
        ]))
        ->create();

    $engine = new PersonalizationEngine;
    $ingredients = ['grilled chicken', 'peanut sauce', 'vegetables'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    expect($result['safety_score'])->toBe(0);
    expect($result['allergen_detected'])->toBeTrue();
    expect($result['detected_allergens'])->toContain('peanuts');
});

test('multiple allergens detected', function () {
    $user = User::factory()
        ->has(\App\Models\Profile::factory()->state([
            'allergens' => ['peanuts', 'shellfish', 'tree nuts'],
        ]))
        ->create();

    $engine = new PersonalizationEngine;
    $ingredients = ['shrimp', 'peanut oil', 'almonds'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    expect($result['safety_score'])->toBe(0);
    expect($result['allergen_detected'])->toBeTrue();
    expect($result['detected_allergens'])->toContain('shellfish');
    expect($result['detected_allergens'])->toContain('peanuts');
    expect($result['detected_allergens'])->toContain('tree nuts');
});

test('allergen takes precedence over trigger deductions', function () {
    $user = User::factory()
        ->has(\App\Models\Profile::factory()->state([
            'allergens' => ['peanuts'],
        ]))
        ->create();

    confirmTrigger($user, 'Fried Foods', 'high');

    $engine = new PersonalizationEngine;
    $ingredients = ['fried chicken', 'peanut sauce'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    // Even though there's a high severity trigger, allergen forces score to 0
    expect($result['safety_score'])->toBe(0);
    expect($result['allergen_detected'])->toBeTrue();
});

test('personalized reasoning includes FOR YOU language', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'Fried Foods', 'high');

    $engine = new PersonalizationEngine;
    $ingredients = ['french fries'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    $reasoning = $engine->generatePersonalizedReasoning($user, $result);

    expect($reasoning)->toContain('FOR YOU');
    expect($reasoning)->toContain('Fried Foods');
});

test('personalized reasoning references user triggers specifically', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'High-Fat Dairy', 'high');

    $engine = new PersonalizationEngine;
    $ingredients = ['cheddar cheese', 'milk', 'butter'];

    $result = $engine->calculateSafetyScore($user, $ingredients);
    $reasoning = $engine->generatePersonalizedReasoning($user, $result);

    expect($reasoning)->toContain('High-Fat Dairy');
    expect($reasoning)->toContain('YOUR');
});

test('personalized reasoning for safe meals is encouraging', function () {
    $user = User::factory()->create();

    $engine = new PersonalizationEngine;
    $ingredients = ['grilled chicken', 'steamed vegetables', 'rice'];

    $result = $engine->calculateSafetyScore($user, $ingredients);
    $reasoning = $engine->generatePersonalizedReasoning($user, $result);

    expect($result['safety_score'])->toBe(100);
    expect($reasoning)->toMatch('/safe|good|excellent/i');
});

test('personalized reasoning for allergens is urgent', function () {
    $user = User::factory()
        ->has(\App\Models\Profile::factory()->state([
            'allergens' => ['peanuts'],
        ]))
        ->create();

    $engine = new PersonalizationEngine;
    $ingredients = ['peanut butter', 'bread'];

    $result = $engine->calculateSafetyScore($user, $ingredients);
    $reasoning = $engine->generatePersonalizedReasoning($user, $result);

    expect($result['safety_score'])->toBe(0);
    expect($reasoning)->toMatch('/allergen|avoid|dangerous/i');
});

test('denied triggers are not counted in safety score', function () {
    $user = User::factory()->create();

    denyTrigger($user, 'Chocolate', 'high');

    $engine = new PersonalizationEngine;
    $ingredients = ['chocolate chips', 'flour'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    // Score should remain 100 since trigger was denied
    expect($result['safety_score'])->toBe(100);
    expect($result['trigger_warnings'])->toBeEmpty();
});

test('only confirmed triggers affect safety score', function () {
    $user = User::factory()->create();

    confirmTrigger($user, 'Fried Foods', 'high');

    // Create unconfirmed (pending) trigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Chocolate',
        'severity' => 'moderate',
        'identification_source' => 'pattern_detected',
        'confidence_score' => 75.00,
        'status' => 'pending_confirmation',
        // No confirmed_at or denied_at
    ]);

    $engine = new PersonalizationEngine;
    $ingredients = ['french fries', 'chocolate sauce'];

    $result = $engine->calculateSafetyScore($user, $ingredients);

    // Only fried foods (confirmed) should deduct, not chocolate (unconfirmed)
    expect($result['safety_score'])->toBe(60); // 100 - 40 (only fried foods)
    expect($result['trigger_warnings'])->toHaveCount(1);
    expect($result['trigger_warnings'][0]['trigger'])->toBe('Fried Foods');
});
