<?php

use App\Models\User;

use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;

// T042: User Registration Tests
test('users can register with valid data', function () {
    $response = postJson('/api/register', [
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ]);

    $response->assertCreated()
        ->assertJsonStructure([
            'user' => [
                'id',
                'name',
                'email',
                'subscription_tier',
                'created_at',
            ],
            'token',
        ]);

    $this->assertDatabaseHas('users', [
        'email' => '<EMAIL>',
        'name' => '<PERSON>',
    ]);

    // Verify profile was created
    $user = User::where('email', '<EMAIL>')->first();
    expect($user->profile)->not->toBeNull();
    expect($user->profile->onboarding_completed)->toBeFalse();
});

test('registration requires name', function () {
    $response = postJson('/api/register', [
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['name']);
});

test('registration requires valid email', function () {
    $response = postJson('/api/register', [
        'name' => 'John Doe',
        'email' => 'not-an-email',
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['email']);
});

test('registration requires unique email', function () {
    User::factory()->create(['email' => '<EMAIL>']);

    $response = postJson('/api/register', [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['email']);
});

test('registration requires password confirmation', function () {
    $response = postJson('/api/register', [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'different-password',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['password']);
});

// T043: User Login Tests
test('users can login with valid credentials', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => bcrypt('password123'),
    ]);

    $response = postJson('/api/login', [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'user' => [
                'id',
                'name',
                'email',
                'subscription_tier',
                'trial_used',
                'subscription_expires_at',
            ],
            'token',
        ]);
});

test('users cannot login with invalid password', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => bcrypt('password123'),
    ]);

    $response = postJson('/api/login', [
        'email' => '<EMAIL>',
        'password' => 'wrong-password',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['email']);
});

test('users cannot login with non-existent email', function () {
    $response = postJson('/api/login', [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['email']);
});

test('login revokes previous tokens', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => bcrypt('password123'),
    ]);

    // First login
    $firstToken = $user->createToken('mobile-app')->plainTextToken;
    expect($user->tokens()->count())->toBe(1);

    // Second login should revoke first token
    $response = postJson('/api/login', [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ]);

    $response->assertSuccessful();

    // Refresh user and check tokens
    $user->refresh();
    expect($user->tokens()->count())->toBe(1);
});

// T044: User Logout Tests
test('authenticated users can logout', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/logout', [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Logged out successfully',
        ]);

    // Token should be revoked
    $user->refresh();
    expect($user->tokens()->count())->toBe(0);
});

test('unauthenticated users cannot logout', function () {
    $response = postJson('/api/logout');

    $response->assertUnauthorized();
});

// T045: Get Current User Tests
test('authenticated users can get their profile', function () {
    $user = User::factory()
        ->has(\App\Models\Profile::factory())
        ->create();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/me', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'user' => [
                'id',
                'name',
                'email',
                'email_verified_at',
                'subscription_tier',
                'trial_used',
                'subscription_expires_at',
                'last_synced_at',
                'created_at',
                'profile',
            ],
        ])
        ->assertJson([
            'user' => [
                'id' => $user->id,
                'email' => $user->email,
            ],
        ]);
});

test('unauthenticated users cannot access me endpoint', function () {
    $response = getJson('/api/me');

    $response->assertUnauthorized();
});

test('invalid token cannot access protected routes', function () {
    $response = getJson('/api/me', [
        'Authorization' => 'Bearer invalid-token',
    ]);

    $response->assertUnauthorized();
});
