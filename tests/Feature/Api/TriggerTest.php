<?php

use App\Models\Trigger;
use App\Models\User;
use App\Models\UserTrigger;

use function Pest\Laravel\deleteJson;
use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;

// T053: Trigger Management Tests

test('authenticated users can list all available triggers', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create some triggers in master list
    Trigger::factory()->count(5)->create();

    $response = getJson('/api/triggers', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'category',
                    'severity',
                    'description',
                    'created_at',
                    'updated_at',
                ],
            ],
        ])
        ->assertJsonCount(5, 'data');
});

test('unauthenticated users cannot list triggers', function () {
    $response = getJson('/api/triggers');

    $response->assertUnauthorized();
});

test('authenticated users can list their personal triggers', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create user triggers directly (not pivot)
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Fried Foods',
        'severity' => 'high',
        'identification_source' => 'user_input',
        'confidence_score' => 85.50,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Dairy',
        'severity' => 'moderate',
        'identification_source' => 'user_input',
        'confidence_score' => 92.30,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    $response = getJson('/api/triggers/me', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'trigger_name',
                    'severity',
                    'identification_source',
                    'confidence_score',
                    'attack_correlation_count',
                    'total_exposures',
                    'status',
                    'confirmed_at',
                    'denied_at',
                ],
            ],
        ])
        ->assertJsonCount(2, 'data');

    // Verify triggers are ordered by severity and confidence score
    $triggers = $response->json('data');
    expect($triggers[0]['trigger_name'])->toBe('Dairy'); // higher confidence
});

test('authenticated users can confirm a new trigger', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $trigger = Trigger::factory()->create(['name' => 'Chocolate', 'severity' => 'moderate']);

    $response = postJson("/api/triggers/{$trigger->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Trigger confirmed successfully',
            'trigger' => [
                'id' => $trigger->id,
                'name' => 'Chocolate',
                'pivot' => [
                    'severity' => 'moderate',
                    'confidence_score' => '100.00',
                ],
            ],
        ]);

    // Verify UserTrigger was created in database
    $this->assertDatabaseHas('user_triggers', [
        'user_id' => $user->id,
        'trigger_name' => 'Chocolate',
        'confidence_score' => 100.00,
        'status' => 'active',
    ]);

    expect($user->triggers()->count())->toBe(1);
});

test('authenticated users can confirm an existing trigger', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $trigger = Trigger::factory()->create(['name' => 'Coffee']);

    // Create existing denied UserTrigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Coffee',
        'severity' => 'low',
        'identification_source' => 'pattern_detected',
        'confidence_score' => 50.00,
        'status' => 'rejected',
        'denied_at' => now()->subDays(1),
    ]);

    $response = postJson("/api/triggers/{$trigger->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Trigger confirmed successfully',
        ]);

    // Verify confirmed_at is set and denied_at is null
    $userTrigger = $user->triggers()->where('trigger_name', 'Coffee')->first();
    expect($userTrigger->status)->toBe('active');
    expect($userTrigger->confirmed_at)->not->toBeNull();
    expect($userTrigger->denied_at)->toBeNull();
});

test('authenticated users can deny a new trigger', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $trigger = Trigger::factory()->create(['name' => 'Peanuts']);

    $response = postJson("/api/triggers/{$trigger->id}/deny", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Trigger denied successfully',
            'trigger' => [
                'id' => $trigger->id,
                'name' => 'Peanuts',
                'pivot' => [
                    'rejection_count' => 1,
                ],
            ],
        ]);

    // Verify UserTrigger was created with denied status
    $this->assertDatabaseHas('user_triggers', [
        'user_id' => $user->id,
        'trigger_name' => 'Peanuts',
        'confidence_score' => 0.00,
        'status' => 'rejected',
    ]);

    expect($user->triggers()->count())->toBe(1);
});

test('authenticated users can deny an existing trigger', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $trigger = Trigger::factory()->create(['name' => 'Sugar']);

    // Create existing confirmed UserTrigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Sugar',
        'severity' => 'high',
        'identification_source' => 'user_input',
        'confidence_score' => 75.00,
        'status' => 'active',
        'confirmed_at' => now()->subDays(1),
    ]);

    $response = postJson("/api/triggers/{$trigger->id}/deny", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Trigger denied successfully',
        ]);

    // Verify denied_at is set and confirmed_at is null
    $userTrigger = $user->triggers()->where('trigger_name', 'Sugar')->first();
    expect($userTrigger->status)->toBe('rejected');
    expect($userTrigger->denied_at)->not->toBeNull();
    expect($userTrigger->confirmed_at)->toBeNull();
    expect($userTrigger->rejection_count)->toBe(1);
});

test('authenticated users can remove a trigger association', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create UserTrigger
    $userTrigger = UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Alcohol',
        'severity' => 'high',
        'identification_source' => 'user_input',
        'confidence_score' => 60.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    expect($user->triggers()->count())->toBe(1);

    $response = deleteJson("/api/triggers/{$userTrigger->id}", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Trigger removed successfully',
        ]);

    // Verify UserTrigger is deleted
    $user->refresh();
    expect($user->triggers()->count())->toBe(0);

    $this->assertDatabaseMissing('user_triggers', [
        'id' => $userTrigger->id,
        'user_id' => $user->id,
    ]);
});

test('unauthenticated users cannot confirm triggers', function () {
    $trigger = Trigger::factory()->create();

    $response = postJson("/api/triggers/{$trigger->id}/confirm");

    $response->assertUnauthorized();
});

test('unauthenticated users cannot deny triggers', function () {
    $trigger = Trigger::factory()->create();

    $response = postJson("/api/triggers/{$trigger->id}/deny");

    $response->assertUnauthorized();
});

test('unauthenticated users cannot remove triggers', function () {
    $response = deleteJson('/api/triggers/1');

    $response->assertUnauthorized();
});

test('confirming non-existent trigger returns 404', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/triggers/99999/confirm', [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertNotFound();
});
