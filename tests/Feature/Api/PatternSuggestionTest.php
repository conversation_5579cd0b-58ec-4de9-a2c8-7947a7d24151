<?php

use App\Models\Attack;
use App\Models\PatternSuggestion;
use App\Models\Scan;
use App\Models\Trigger;
use App\Models\User;
use App\Models\UserTrigger;

use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;

// T053: Pattern Suggestion Tests

test('pattern suggestions are generated after 3 or more correlations', function () {
    $user = User::factory()->create();

    // Create 3 attacks all preceded by chocolate
    for ($i = 0; $i < 3; $i++) {
        $scan = Scan::factory()->for($user)->completed()->create([
            'meal_name' => 'Chocolate Dessert',
            'detected_ingredients' => ['chocolate', 'sugar', 'cream'],
            'analyzed_at' => now()->subDays($i * 2)->subHours(5),
        ]);

        $attack = Attack::factory()->for($user)->create([
            'onset_at' => now()->subDays($i * 2),
        ]);

        $attack->scans()->attach($scan->id, [
            'hours_before_attack' => 5,
            'match_weight' => 100,
        ]);
    }

    // Simulate pattern detection job running
    $patternSuggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Chocolate',
        'confidence_score' => 85.50,
        'correlation_count' => 3,
        'status' => 'pending',
        'evidence' => [
            ['scan_id' => 1, 'attack_id' => 1, 'hours_before' => 5],
            ['scan_id' => 2, 'attack_id' => 2, 'hours_before' => 5],
            ['scan_id' => 3, 'attack_id' => 3, 'hours_before' => 5],
        ],
    ]);

    expect($user->patternSuggestions()->count())->toBe(1);
});

test('authenticated users can view their pattern suggestions', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    PatternSuggestion::factory()->for($user)->count(2)->create([
        'status' => 'pending',
    ]);

    $response = getJson('/api/patterns/suggestions', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'suspected_trigger_name',
                    'confidence_score',
                    'correlation_count',
                    'evidence',
                    'status',
                    'created_at',
                ],
            ],
        ])
        ->assertJsonCount(2, 'data');
});

test('pattern suggestions require authentication', function () {
    $response = getJson('/api/patterns/suggestions');

    $response->assertUnauthorized();
});

test('pattern suggestions are ordered by confidence score descending', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    PatternSuggestion::factory()->for($user)->create([
        'confidence_score' => 65.00,
        'status' => 'pending',
    ]);

    PatternSuggestion::factory()->for($user)->create([
        'confidence_score' => 92.50,
        'status' => 'pending',
    ]);

    PatternSuggestion::factory()->for($user)->create([
        'confidence_score' => 78.30,
        'status' => 'pending',
    ]);

    $response = getJson('/api/patterns/suggestions', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    $suggestions = $response->json('data');
    expect($suggestions[0]['confidence_score'])->toBe('92.50');
    expect($suggestions[1]['confidence_score'])->toBe('78.30');
    expect($suggestions[2]['confidence_score'])->toBe('65.00');
});

test('only pending suggestions are shown by default', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    PatternSuggestion::factory()->for($user)->create(['status' => 'pending']);
    PatternSuggestion::factory()->for($user)->create(['status' => 'confirmed']);
    PatternSuggestion::factory()->for($user)->create(['status' => 'rejected']);

    $response = getJson('/api/patterns/suggestions', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonCount(1, 'data');

    expect($response->json('data.0.status'))->toBe('pending');
});

test('authenticated users can confirm a pattern suggestion', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Chocolate',
        'confidence_score' => 82.00,
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Pattern confirmed and added to your triggers',
        ]);

    // Verify suggestion status updated
    $suggestion->refresh();
    expect($suggestion->status)->toBe('confirmed');

    // Verify UserTrigger was created
    $userTrigger = $user->triggers()->where('trigger_name', 'Chocolate')->first();
    expect($userTrigger)->not->toBeNull();
    expect($userTrigger->confidence_score)->toBe('82.00');
    expect($userTrigger->confirmed_at)->not->toBeNull();
    expect($userTrigger->status)->toBe('active');
});

test('confirming pattern creates trigger if it does not exist', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // User doesn't have this trigger yet
    expect($user->triggers()->where('trigger_name', 'Coconut')->exists())->toBeFalse();

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Coconut',
        'confidence_score' => 75.00,
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    // Verify UserTrigger was created
    $userTrigger = $user->triggers()->where('trigger_name', 'Coconut')->first();
    expect($userTrigger)->not->toBeNull();
    expect($userTrigger->severity)->not->toBeNull();
    expect($userTrigger->status)->toBe('active');
});

test('confirming pattern updates existing trigger', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // User already has this trigger but denied it
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Chocolate',
        'severity' => 'moderate',
        'identification_source' => 'pattern_detected',
        'confidence_score' => 0.00,
        'status' => 'rejected',
        'denied_at' => now()->subDays(5),
    ]);

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Chocolate',
        'confidence_score' => 88.00,
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    // Verify trigger was updated (not duplicated)
    $user->refresh();
    $userTrigger = $user->triggers()->where('trigger_name', 'Chocolate')->first();
    expect($userTrigger->confidence_score)->toBe('88.00');
    expect($userTrigger->confirmed_at)->not->toBeNull();
    expect($userTrigger->denied_at)->toBeNull();
    expect($userTrigger->status)->toBe('active');
});

test('authenticated users can reject a pattern suggestion', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Tomatoes',
        'confidence_score' => 68.00,
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/reject", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Pattern suggestion rejected',
        ]);

    // Verify suggestion status updated
    $suggestion->refresh();
    expect($suggestion->status)->toBe('rejected');

    // Verify no trigger was added
    $userTriggers = $user->triggers()->where('trigger_name', 'Tomatoes')->count();
    expect($userTriggers)->toBe(0);
});

test('rejecting pattern does not create trigger', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $initialTriggerCount = $user->triggers()->count();

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Onions',
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/reject", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->triggers()->count())->toBe($initialTriggerCount);
});

test('users cannot confirm other users pattern suggestions', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $token = $user2->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user1)->create([
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden();
});

test('users cannot reject other users pattern suggestions', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $token = $user2->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user1)->create([
        'status' => 'pending',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/reject", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden();
});

test('confirming non-existent pattern returns 404', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/patterns/99999/confirm', [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertNotFound();
});

test('pattern evidence includes scan and attack details', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'suspected_trigger_name' => 'Pizza',
        'confidence_score' => 82.50,
        'correlation_count' => 3,
        'status' => 'pending',
        'evidence' => [
            [
                'scan_id' => 1,
                'attack_id' => 1,
                'hours_before' => 6,
                'match_weight' => 100,
                'meal_name' => 'Pepperoni Pizza',
                'attack_date' => now()->subDays(10)->toISOString(),
            ],
            [
                'scan_id' => 2,
                'attack_id' => 2,
                'hours_before' => 5,
                'match_weight' => 100,
                'meal_name' => 'Cheese Pizza',
                'attack_date' => now()->subDays(5)->toISOString(),
            ],
            [
                'scan_id' => 3,
                'attack_id' => 3,
                'hours_before' => 7,
                'match_weight' => 100,
                'meal_name' => 'Supreme Pizza',
                'attack_date' => now()->subDays(2)->toISOString(),
            ],
        ],
    ]);

    $response = getJson('/api/patterns/suggestions', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    $evidence = $response->json('data.0.evidence');
    expect($evidence)->toBeArray();
    expect($evidence)->toHaveCount(3);
    expect($evidence[0]['hours_before'])->toBe(6);
    expect($evidence[0]['meal_name'])->toBe('Pepperoni Pizza');
});

test('pattern suggestion includes correlation count', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'correlation_count' => 5,
        'status' => 'pending',
    ]);

    $response = getJson('/api/patterns/suggestions', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'data' => [
                [
                    'correlation_count' => 5,
                ],
            ],
        ]);
});

test('cannot confirm already confirmed suggestion', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'status' => 'confirmed',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/confirm", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJson([
            'message' => 'Pattern already confirmed',
        ]);
});

test('cannot reject already rejected suggestion', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $suggestion = PatternSuggestion::factory()->for($user)->create([
        'status' => 'rejected',
    ]);

    $response = postJson("/api/patterns/{$suggestion->id}/reject", [], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJson([
            'message' => 'Pattern already rejected',
        ]);
});
