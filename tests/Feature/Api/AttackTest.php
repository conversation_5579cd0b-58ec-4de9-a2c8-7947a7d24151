<?php

use App\Models\Attack;
use App\Models\User;

use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;

// T051: Attack Logging Tests

test('authenticated users can log an attack with valid data', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'onset_at' => now()->subHours(2)->toISOString(),
        'pain_intensity' => 8,
        'duration_minutes' => 120,
        'symptoms' => ['severe pain', 'nausea', 'vomiting'],
        'medical_care_type' => 'emergency_room',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated()
        ->assertJsonStructure([
            'attack' => [
                'id',
                'onset_at',
                'pain_intensity',
                'duration_minutes',
                'symptoms',
                'medical_care_type',
                'created_at',
            ],
        ]);

    // Verify in database
    $this->assertDatabaseHas('attacks', [
        'user_id' => $user->id,
        'pain_intensity' => 8,
        'duration_minutes' => 120,
        'medical_care_type' => 'emergency_room',
    ]);

    expect($user->attacks()->count())->toBe(1);
});

test('attack logging requires authentication', function () {
    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => 60,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ]);

    $response->assertUnauthorized();
});

test('attack logging requires onset_at datetime', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'pain_intensity' => 7,
        'duration_minutes' => 60,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['onset_at']);
});

test('attack logging requires pain intensity between 1 and 10', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 15, // Invalid: > 10
        'duration_minutes' => 60,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['pain_intensity']);

    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 0, // Invalid: < 1
        'duration_minutes' => 60,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['pain_intensity']);
});

test('attack logging validates duration_minutes', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => -10, // Invalid: negative
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['duration_minutes']);
});

test('attack logging validates symptoms as array', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => 60,
        'symptoms' => 'just a string', // Invalid: should be array
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['symptoms']);
});

test('attack logging validates medical_care_type enum', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => 60,
        'symptoms' => ['pain'],
        'medical_care_type' => 'invalid_type',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['medical_care_type']);
});

test('valid medical care types are accepted', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $validTypes = ['none', 'urgent_care', 'emergency_room', 'primary_care', 'hospitalization'];

    foreach ($validTypes as $type) {
        $response = postJson('/api/attacks', [
            'onset_at' => now()->subHours(rand(1, 10))->toISOString(),
            'pain_intensity' => 7,
            'duration_minutes' => 60,
            'symptoms' => ['pain'],
            'medical_care_type' => $type,
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    expect($user->attacks()->count())->toBe(count($validTypes));
});

test('authenticated users can list their attacks', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create attacks
    Attack::factory()->count(5)->for($user)->create();

    $response = getJson('/api/attacks', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'onset_at',
                    'pain_intensity',
                    'duration_minutes',
                    'symptoms',
                    'medical_care_type',
                    'created_at',
                ],
            ],
        ])
        ->assertJsonCount(5, 'data');
});

test('attacks are ordered by onset_at descending', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create attacks with different onset times
    Attack::factory()->for($user)->create(['onset_at' => now()->subDays(5)]);
    Attack::factory()->for($user)->create(['onset_at' => now()->subDays(1)]);
    Attack::factory()->for($user)->create(['onset_at' => now()->subDays(10)]);

    $response = getJson('/api/attacks', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    $attacks = $response->json('data');

    // Most recent should be first
    expect($attacks[0]['onset_at'])->toBeGreaterThan($attacks[1]['onset_at']);
    expect($attacks[1]['onset_at'])->toBeGreaterThan($attacks[2]['onset_at']);
});

test('attack list supports pagination', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 25 attacks
    Attack::factory()->count(25)->for($user)->create();

    $response = getJson('/api/attacks?per_page=10', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'data',
            'links',
            'meta' => [
                'current_page',
                'total',
                'per_page',
            ],
        ])
        ->assertJsonCount(10, 'data');

    expect($response->json('meta.total'))->toBe(25);
    expect($response->json('meta.per_page'))->toBe(10);
});

test('authenticated users can view a specific attack', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attack = Attack::factory()->for($user)->create([
        'pain_intensity' => 9,
        'symptoms' => ['severe pain', 'nausea', 'vomiting'],
    ]);

    $response = getJson("/api/attacks/{$attack->id}", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'attack' => [
                'id' => $attack->id,
                'pain_intensity' => 9,
                'symptoms' => ['severe pain', 'nausea', 'vomiting'],
            ],
        ]);
});

test('users cannot view other users attacks', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $token = $user2->createToken('mobile-app')->plainTextToken;

    $attack = Attack::factory()->for($user1)->create();

    $response = getJson("/api/attacks/{$attack->id}", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden();
});

test('viewing non-existent attack returns 404', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/attacks/99999', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertNotFound();
});

test('attack with optional fields can be created', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Only required fields
    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 5,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();

    $attack = $response->json('attack');
    expect($attack['pain_intensity'])->toBe(5);
    expect($attack['duration_minutes'])->toBeNull();
    expect($attack['symptoms'])->toBeNull();
    expect($attack['medical_care_type'])->toBeNull();
});

test('attack can include notes field', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/attacks', [
        'onset_at' => now()->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => 90,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
        'notes' => 'Attack occurred 6 hours after eating fried chicken',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();

    $this->assertDatabaseHas('attacks', [
        'user_id' => $user->id,
        'notes' => 'Attack occurred 6 hours after eating fried chicken',
    ]);
});

test('users cannot list other users attacks', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $token = $user2->createToken('mobile-app')->plainTextToken;

    // Create attacks for user1
    Attack::factory()->count(3)->for($user1)->create();
    // Create attacks for user2
    Attack::factory()->count(2)->for($user2)->create();

    $response = getJson('/api/attacks', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonCount(2, 'data'); // Should only see own attacks

    // Verify all returned attacks belong to user2
    $attacks = $response->json('data');
    foreach ($attacks as $attack) {
        expect($attack['user_id'] ?? null)->toBe($user2->id);
    }
});
