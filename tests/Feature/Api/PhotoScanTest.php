<?php

use App\Models\Scan;
use App\Models\User;
use App\Models\UserTrigger;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;

// T049: Photo Scanning Tests

test('authenticated users can upload a photo for scanning', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $photo = UploadedFile::fake()->image('meal.jpg', 800, 600);

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated()
        ->assertJsonStructure([
            'scan' => [
                'id',
                'status',
                'meal_type',
                'created_at',
            ],
        ]);

    // Verify scan was created with 'analyzing' status
    $scan = $response->json('scan');
    expect($scan['status'])->toBe('analyzing');
    expect($scan['meal_type'])->toBe('lunch');

    // Verify job was dispatched
    Queue::assertPushed(\App\Jobs\AnalyzeScanPhotoJob::class);

    // Verify file was stored (using the scan ID from the response)
    $scanId = $scan['id'];
    Storage::disk('public')->assertExists("scans/{$scanId}/".basename($photo->hashName()));
});

test('photo upload requires authentication', function () {
    Storage::fake('local');
    $photo = UploadedFile::fake()->image('meal.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ]);

    $response->assertUnauthorized();
});

test('photo upload requires a photo file', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = postJson('/api/scan/photo', [
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['photo']);
});

test('photo upload requires valid meal type', function () {
    Storage::fake('public');
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $photo = UploadedFile::fake()->image('meal.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'invalid-meal-type',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['meal_type']);
});

test('authenticated users can poll scan status', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $scan = Scan::factory()->for($user)->analyzing()->create();

    $response = getJson("/api/scan/{$scan->id}/status", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'scan' => [
                'id',
                'status',
                'created_at',
            ],
        ])
        ->assertJson([
            'scan' => [
                'id' => $scan->id,
                'status' => 'analyzing',
            ],
        ]);
});

test('authenticated users can retrieve completed scan results', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create user trigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Fried Foods',
        'severity' => 'high',
        'identification_source' => 'user_input',
        'confidence_score' => 100.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create completed scan
    $scan = Scan::factory()->for($user)->completed()->create([
        'meal_name' => 'French Fries',
        'adjusted_score' => 20,
        'personalized_reasoning' => 'This meal contains fried foods, which is a HIGH severity trigger FOR YOU. Your personalized safety score is 20/100.',
    ]);

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'scan' => [
                'id',
                'meal_name',
                'adjusted_score',
                'personalized_reasoning',
                'status',
                'created_at',
            ],
        ])
        ->assertJson([
            'scan' => [
                'id' => $scan->id,
                'meal_name' => 'French Fries',
                'adjusted_score' => 20,
                'status' => 'completed',
            ],
        ]);

    // Verify personalized reasoning includes "FOR YOU" language
    $reasoning = $response->json('scan.personalized_reasoning');
    expect($reasoning)->toContain('FOR YOU');
});

test('scan result shows trigger warnings for user triggers', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create user triggers
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Fried Foods',
        'severity' => 'high',
        'identification_source' => 'user_input',
        'confidence_score' => 100.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'High-Fat Dairy',
        'severity' => 'high',
        'identification_source' => 'user_input',
        'confidence_score' => 100.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create completed scan with personalized reasoning mentioning triggers
    $scan = Scan::factory()->for($user)->completed()->create([
        'meal_name' => 'Cheese Pizza',
        'adjusted_score' => 15,
        'personalized_reasoning' => 'This meal contains High-Fat Dairy, which is a HIGH severity trigger FOR YOU.',
    ]);

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    // Verify personalized reasoning mentions the user's trigger
    $reasoning = $response->json('scan.personalized_reasoning');
    expect($reasoning)->toContain('High-Fat Dairy');
    expect($reasoning)->toContain('FOR YOU');
});

test('users cannot access other users scan results', function () {
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $token = $user2->createToken('mobile-app')->plainTextToken;

    $scan = Scan::factory()->for($user1)->completed()->create();

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden();
});

test('retrieving result for non-existent scan returns 404', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/scan/99999/result', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertNotFound();
});

test('scan status transitions from analyzing to completed', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $scan = Scan::factory()->for($user)->analyzing()->create();

    // Initial status should be 'analyzing'
    $response = getJson("/api/scan/{$scan->id}/status", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertJson([
        'scan' => ['status' => 'analyzing'],
    ]);

    // Simulate job completion
    $scan->update([
        'status' => 'completed',
        'meal_name' => 'Grilled Chicken Salad',
        'adjusted_score' => 85,
        'analyzed_at' => now(),
    ]);

    // Status should now be 'completed'
    $response = getJson("/api/scan/{$scan->id}/status", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertJson([
        'scan' => ['status' => 'completed'],
    ]);
});

test('failed scan shows appropriate status', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $scan = Scan::factory()->for($user)->create([
        'status' => 'failed',
    ]);

    $response = getJson("/api/scan/{$scan->id}/status", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'scan' => [
                'status' => 'failed',
            ],
        ]);
});
