<?php

use App\Models\Profile;
use App\Models\User;

use function Pest\Laravel\getJson;
use function Pest\Laravel\putJson;

// T050: Profile Management Tests

test('authenticated users can view their profile', function () {
    $user = User::factory()
        ->has(Profile::factory())
        ->create();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = getJson('/api/profile', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'profile' => [
                'id',
                'user_id',
                'age',
                'sex',
                'weight_kg',
                'height_cm',
                'activity_level',
                'health_conditions',
                'dietary_preferences',
                'allergens',
                'completion_percentage',
                'onboarding_completed',
                'created_at',
                'updated_at',
            ],
        ])
        ->assertJson([
            'profile' => [
                'user_id' => $user->id,
            ],
        ]);
});

test('unauthenticated users cannot view profile', function () {
    $response = getJson('/api/profile');

    $response->assertUnauthorized();
});

test('authenticated users can update their profile', function () {
    $user = User::factory()->create();

    // Create empty profile
    Profile::create([
        'user_id' => $user->id,
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = putJson('/api/profile', [
        'age' => 35,
        'sex' => 'female',
        'weight_kg' => 68.5,
        'height_cm' => 165,
        'activity_level' => 'moderate',
        'health_conditions' => ['gallstones', 'high cholesterol'],
        'dietary_preferences' => ['vegetarian'],
        'allergens' => ['peanuts'],
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'message',
            'profile' => [
                'age',
                'sex',
                'weight_kg',
                'height_cm',
                'activity_level',
                'health_conditions',
                'dietary_preferences',
                'allergens',
                'completion_percentage',
                'onboarding_completed',
            ],
        ]);

    // Verify the data was actually updated
    $profile = $response->json('profile');
    expect($profile['age'])->toBe(35);
    expect($profile['sex'])->toBe('female');
    expect($profile['weight_kg'])->toBe('68.50');
    expect($profile['height_cm'])->toBe('165.00');
    expect($profile['activity_level'])->toBe('moderate');
    expect($profile['health_conditions'])->toBe(['gallstones', 'high cholesterol']);
    expect($profile['dietary_preferences'])->toBe(['vegetarian']);
    expect($profile['allergens'])->toBe(['peanuts']);
    expect($profile['completion_percentage'])->toBe(100);
    expect($profile['onboarding_completed'])->toBeTrue();

    // Verify database was updated
    $this->assertDatabaseHas('profiles', [
        'user_id' => $user->id,
        'age' => 35,
        'sex' => 'female',
        'completion_percentage' => 100,
        'onboarding_completed' => 1,
    ]);
});

test('profile update creates profile if it does not exist', function () {
    $user = User::factory()->create();

    // Ensure no profile exists
    expect($user->profile)->toBeNull();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = putJson('/api/profile', [
        'age' => 28,
        'sex' => 'male',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJson([
            'message' => 'Profile updated successfully',
        ]);

    // Verify profile was created
    $user->refresh();
    expect($user->profile)->not->toBeNull();
    expect($user->profile->age)->toBe(28);
    expect($user->profile->sex)->toBe('male');
});

test('profile completion percentage is calculated correctly', function () {
    $user = User::factory()->create();

    // Create empty profile
    Profile::create([
        'user_id' => $user->id,
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Update with 4 out of 8 fields = 50%
    $response = putJson('/api/profile', [
        'age' => 30,
        'sex' => 'male',
        'weight_kg' => 75,
        'height_cm' => 180,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    $profile = $response->json('profile');
    expect($profile['completion_percentage'])->toBe(50);
    expect($profile['onboarding_completed'])->toBeFalse();
});

test('profile update validates age range', function () {
    $user = User::factory()
        ->has(Profile::factory())
        ->create();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = putJson('/api/profile', [
        'age' => 150,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['age']);
});

test('profile update validates sex options', function () {
    $user = User::factory()
        ->has(Profile::factory())
        ->create();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = putJson('/api/profile', [
        'sex' => 'invalid-option',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['sex']);
});

test('profile update validates activity level options', function () {
    $user = User::factory()
        ->has(Profile::factory())
        ->create();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = putJson('/api/profile', [
        'activity_level' => 'extreme',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['activity_level']);
});

test('profile update accepts partial data', function () {
    $user = User::factory()->create();

    // Create profile with some initial data
    Profile::create([
        'user_id' => $user->id,
        'age' => 25,
        'sex' => 'male',
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Only update weight
    $response = putJson('/api/profile', [
        'weight_kg' => 80,
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful();

    // Verify other fields remain unchanged and weight was updated
    $user->refresh();
    expect($user->profile->age)->toBe(25);
    expect($user->profile->sex)->toBe('male');
    expect((float) $user->profile->weight_kg)->toBe(80.0);
});

test('profile update validates health conditions as array', function () {
    $user = User::factory()
        ->has(Profile::factory())
        ->create();

    $token = $user->createToken('mobile-app')->plainTextToken;

    $response = putJson('/api/profile', [
        'health_conditions' => 'not-an-array',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['health_conditions']);
});

test('unauthenticated users cannot update profile', function () {
    $response = putJson('/api/profile', [
        'age' => 30,
    ]);

    $response->assertUnauthorized();
});
