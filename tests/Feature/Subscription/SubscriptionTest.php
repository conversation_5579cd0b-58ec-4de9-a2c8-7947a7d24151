<?php

use App\Models\Scan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;

use function Pest\Laravel\getJson;
use function Pest\Laravel\postJson;

uses(RefreshDatabase::class);

// T127: Subscription Tests (Basic - just tier checking)

test('free users can scan up to 3 photos per day', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'free',
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // First 3 scans should succeed
    for ($i = 0; $i < 3; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // 4th scan should be blocked
    $photo = UploadedFile::fake()->image('meal4.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden()
        ->assertJson([
            'message' => 'Daily limit reached. Upgrade to Premium for unlimited scans.',
        ]);
});

test('daily scan limit resets at midnight user local time', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'free',
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 3 scans from yesterday
    Scan::factory()->count(3)->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'created_at' => Carbon::yesterday(),
    ]);

    // Today's scan should succeed (counter reset)
    $photo = UploadedFile::fake()->image('meal.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'breakfast',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();
});

test('premium users have unlimited photo scans', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'premium',
        'subscription_expires_at' => Carbon::now()->addMonth(),
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 10 photo scans (well beyond free limit)
    for ($i = 0; $i < 10; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // All 10 scans should succeed
    expect(Scan::where('user_id', $user->id)->count())->toBe(10);
});

test('barcode scans are unlimited for free users', function () {
    $user = User::factory()->create([
        'subscription_tier' => 'free',
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Mock barcode API
    \Illuminate\Support\Facades\Http::fake([
        'world.openfoodfacts.org/*' => \Illuminate\Support\Facades\Http::response([
            'status' => 1,
            'product' => [
                'product_name' => 'Test Product',
                'ingredients_text' => 'Test ingredients',
                'allergens' => '',
                'nutriments' => [
                    'fat_100g' => 5.0,
                    'saturated-fat_100g' => 1.0,
                ],
            ],
        ], 200),
    ]);

    // Create 10 barcode scans (well beyond photo limit)
    for ($i = 0; $i < 10; $i++) {
        $response = postJson('/api/scan/barcode', [
            'barcode' => "123456789012{$i}",
            'meal_type' => 'snack',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // All 10 barcode scans should succeed
    expect(Scan::where('user_id', $user->id)->where('type', 'barcode')->count())->toBe(10);
});

test('upgrade prompt shown when free user hits daily limit', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'free',
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 3 scans today
    Scan::factory()->count(3)->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'created_at' => Carbon::now(),
    ]);

    // 4th scan attempt should show upgrade message
    $photo = UploadedFile::fake()->image('meal.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden()
        ->assertJson([
            'message' => 'Daily limit reached. Upgrade to Premium for unlimited scans.',
            'upgrade_required' => true,
        ]);
});

test('expired premium subscription reverts to free tier limits', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'premium',
        'subscription_expires_at' => Carbon::yesterday(), // Expired
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 3 scans today
    for ($i = 0; $i < 3; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // 4th scan should be blocked (expired premium = free tier)
    $photo = UploadedFile::fake()->image('meal4.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden()
        ->assertJson([
            'message' => 'Daily limit reached. Upgrade to Premium for unlimited scans.',
        ]);
});

test('scan count endpoint returns current usage for free users', function () {
    $user = User::factory()->create([
        'subscription_tier' => 'free',
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 2 photo scans today
    Scan::factory()->count(2)->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'created_at' => Carbon::now(),
    ]);

    $response = getJson('/api/subscription/scan-count', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk()
        ->assertJson([
            'scans_used_today' => 2,
            'daily_limit' => 3,
            'scans_remaining' => 1,
            'subscription_tier' => 'free',
        ]);
});

test('scan count endpoint shows unlimited for premium users', function () {
    $user = User::factory()->create([
        'subscription_tier' => 'premium',
        'subscription_expires_at' => Carbon::now()->addMonth(),
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 10 photo scans today
    Scan::factory()->count(10)->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'created_at' => Carbon::now(),
    ]);

    $response = getJson('/api/subscription/scan-count', [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertOk()
        ->assertJson([
            'scans_used_today' => 10,
            'daily_limit' => null, // Unlimited
            'scans_remaining' => null, // Unlimited
            'subscription_tier' => 'premium',
        ]);
});

test('users on trial have premium scan limits', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'premium',
        'trial_ends_at' => Carbon::now()->addDays(7),
        'subscription_expires_at' => null,
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 10 photo scans (well beyond free limit)
    for ($i = 0; $i < 10; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // All 10 scans should succeed during trial
    expect(Scan::where('user_id', $user->id)->count())->toBe(10);
});

test('expired trial reverts to free tier limits', function () {
    Storage::fake('public');
    Queue::fake();

    $user = User::factory()->create([
        'subscription_tier' => 'free',
        'trial_ends_at' => Carbon::yesterday(), // Trial expired
        'trial_used' => true,
    ]);

    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create 3 scans today
    for ($i = 0; $i < 3; $i++) {
        $photo = UploadedFile::fake()->image("meal{$i}.jpg");

        $response = postJson('/api/scan/photo', [
            'photo' => $photo,
            'meal_type' => 'lunch',
        ], [
            'Authorization' => "Bearer {$token}",
        ]);

        $response->assertCreated();
    }

    // 4th scan should be blocked (trial expired = free tier)
    $photo = UploadedFile::fake()->image('meal4.jpg');

    $response = postJson('/api/scan/photo', [
        'photo' => $photo,
        'meal_type' => 'lunch',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertForbidden();
});
