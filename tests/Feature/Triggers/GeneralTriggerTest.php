<?php

use App\Models\Scan;
use App\Models\ScanIngredient;
use App\Models\User;
use App\Models\UserTrigger;
use Illuminate\Support\Facades\Queue;

use function Pest\Laravel\getJson;

// T212: General Trigger Tests - User with general trigger "greasy food", scan high-fat meal, verify moderate risk score with explanation

test('user with general trigger "greasy food" receives moderate risk score for high-fat meal', function () {
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create general trigger for user
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Greasy Food',
        'severity' => 'moderate',
        'identification_source' => 'user_input',
        'confidence_score' => 80.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create a completed scan with high-fat ingredients
    $scan = Scan::factory()->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'meal_name' => 'Fried Chicken',
        'status' => 'completed',
        'adjusted_score' => 50, // Moderate risk
        'personalized_reasoning' => 'This meal contains greasy ingredients which match YOUR trigger "Greasy Food". Consider avoiding this meal.',
        'analyzed_at' => now(),
    ]);

    // Add high-fat ingredients
    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Fried Chicken',
        'category' => 'protein',
        'is_trigger' => true,
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Cooking Oil',
        'category' => 'fat',
        'is_trigger' => true,
    ]);

    // Retrieve scan result
    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'scan' => [
                'id',
                'meal_name',
                'safety_score',
                'personalized_reasoning',
                'trigger_warnings',
            ],
        ]);

    $scanData = $response->json('scan');

    // Verify moderate risk score (not 0 or 100)
    expect($scanData['safety_score'])->toBeGreaterThan(0);
    expect($scanData['safety_score'])->toBeLessThan(80);

    // Verify personalized explanation mentions the general trigger
    expect($scanData['personalized_reasoning'])->toContain('Greasy');
});

test('general trigger matches category of ingredients, not exact names', function () {
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create general trigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Fatty Foods',
        'severity' => 'moderate',
        'identification_source' => 'user_input',
        'confidence_score' => 75.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create scan with various high-fat foods (not exact match to "Fatty Foods")
    $scan = Scan::factory()->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'meal_name' => 'Bacon Cheeseburger',
        'status' => 'completed',
        'adjusted_score' => 45,
        'personalized_reasoning' => 'This meal contains high-fat ingredients (bacon, cheese) which may trigger your sensitivity to fatty foods.',
        'trigger_warnings' => [
            [
                'trigger_name' => 'Fatty Foods',
                'severity' => 'moderate',
            ],
        ],
        'analyzed_at' => now(),
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Bacon',
        'category' => 'protein',
        'is_trigger' => true,
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Cheese',
        'category' => 'dairy',
        'is_trigger' => true,
    ]);

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $scanData = $response->json('scan');

    // Verify trigger detection despite different ingredient names
    expect($scanData['trigger_warnings'])->not->toBeEmpty();
    expect($scanData['safety_score'])->toBeLessThan(60);
});

test('specific triggers take precedence over general triggers', function () {
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create both general and specific triggers
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Fried Foods',
        'severity' => 'moderate',
        'identification_source' => 'user_input',
        'confidence_score' => 70.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'French Fries',
        'severity' => 'high',
        'identification_source' => 'pattern_detected',
        'confidence_score' => 95.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create scan with french fries
    $scan = Scan::factory()->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'meal_name' => 'French Fries',
        'status' => 'completed',
        'adjusted_score' => 25, // High severity deduction
        'personalized_reasoning' => 'French Fries is YOUR known trigger with high severity.',
        'analyzed_at' => now(),
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'French Fries',
        'category' => 'carbohydrate',
        'is_trigger' => true,
    ]);

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $scanData = $response->json('scan');

    // Specific trigger (high severity = -40 points) should apply, not general (moderate = -25)
    expect($scanData['safety_score'])->toBeLessThan(30);
    expect($scanData['personalized_reasoning'])->toContain('French Fries');
});

test('general triggers with low severity apply minimal deduction', function () {
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create low severity general trigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Spicy Foods',
        'severity' => 'low',
        'identification_source' => 'user_input',
        'confidence_score' => 60.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create scan with spicy ingredients
    $scan = Scan::factory()->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'meal_name' => 'Mild Curry',
        'status' => 'completed',
        'adjusted_score' => 75, // Low severity = -15 points (from base 90)
        'personalized_reasoning' => 'This meal contains mild spicy ingredients. You marked spicy foods as a low-severity trigger.',
        'analyzed_at' => now(),
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Curry Powder',
        'category' => 'spice',
        'is_trigger' => true,
    ]);

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $scanData = $response->json('scan');

    // Low severity should result in higher safety score (minimal deduction)
    expect($scanData['safety_score'])->toBeGreaterThan(70);
    expect($scanData['safety_score'])->toBeLessThan(90);
});

test('meals without general trigger ingredients receive high safety scores', function () {
    Queue::fake();

    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    // Create general trigger
    UserTrigger::create([
        'user_id' => $user->id,
        'trigger_name' => 'Dairy Products',
        'severity' => 'moderate',
        'identification_source' => 'user_input',
        'confidence_score' => 80.00,
        'status' => 'active',
        'confirmed_at' => now(),
    ]);

    // Create scan with NO dairy
    $scan = Scan::factory()->create([
        'user_id' => $user->id,
        'type' => 'photo',
        'meal_name' => 'Grilled Salmon with Vegetables',
        'status' => 'completed',
        'adjusted_score' => 90, // High score, no triggers
        'personalized_reasoning' => 'This meal appears safe for YOUR profile. No known triggers detected.',
        'analyzed_at' => now(),
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Salmon',
        'category' => 'protein',
        'is_trigger' => false,
    ]);

    ScanIngredient::create([
        'scan_id' => $scan->id,
        'ingredient_name' => 'Broccoli',
        'category' => 'vegetable',
        'is_trigger' => false,
    ]);

    $response = getJson("/api/scan/{$scan->id}/result", [
        'Authorization' => "Bearer {$token}",
    ]);

    $scanData = $response->json('scan');

    // Meal without triggers should have high safety score
    expect($scanData['safety_score'])->toBeGreaterThan(80);
    expect($scanData['trigger_warnings'])->toBeEmpty();
});
