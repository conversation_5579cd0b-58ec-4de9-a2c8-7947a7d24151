<?php

use App\Models\Attack;
use App\Models\Scan;
use App\Models\User;

use function Pest\Laravel\postJson;

// T174: Attack-Scan Correlation Tests

test('attack correlation finds scans 3-8 hours before attack', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attackTime = now();

    // Create scans at various times
    $scan4HoursAgo = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(4),
        'meal_name' => 'Scan 4 hours ago',
        'status' => 'completed',
    ]);

    $scan6HoursAgo = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(6),
        'meal_name' => 'Scan 6 hours ago',
        'status' => 'completed',
    ]);

    // Should NOT be correlated (too recent)
    $scan2HoursAgo = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(2),
        'meal_name' => 'Scan 2 hours ago',
        'status' => 'completed',
    ]);

    // Should NOT be correlated (too old)
    $scan10HoursAgo = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(10),
        'meal_name' => 'Scan 10 hours ago',
        'status' => 'completed',
    ]);

    // Log attack
    $response = postJson('/api/attacks', [
        'onset_at' => $attackTime->toISOString(),
        'pain_intensity' => 8,
        'duration_minutes' => 120,
        'symptoms' => ['severe pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();
    $attackId = $response->json('attack.id');

    // Verify correlations in attack_scans pivot table
    $this->assertDatabaseHas('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $scan4HoursAgo->id,
    ]);

    $this->assertDatabaseHas('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $scan6HoursAgo->id,
    ]);

    // Verify incorrect correlations don't exist
    $this->assertDatabaseMissing('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $scan2HoursAgo->id,
    ]);

    $this->assertDatabaseMissing('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $scan10HoursAgo->id,
    ]);
});

test('attack correlation records hours_before_attack in pivot table', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attackTime = now();

    $scan5HoursAgo = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(5),
        'meal_name' => 'Test Meal',
        'status' => 'completed',
    ]);

    $response = postJson('/api/attacks', [
        'onset_at' => $attackTime->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => 90,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();
    $attackId = $response->json('attack.id');

    // Verify hours_before_attack is recorded accurately
    $this->assertDatabaseHas('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $scan5HoursAgo->id,
    ]);

    // Check the hours_before_attack value
    $correlation = \DB::table('attack_scans')
        ->where('attack_id', $attackId)
        ->where('scan_id', $scan5HoursAgo->id)
        ->first();

    expect($correlation->hours_before_attack)->toBeGreaterThan(4.9);
    expect($correlation->hours_before_attack)->toBeLessThan(5.1);
});

test('attack correlation calculates match_weight correctly', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attackTime = now();

    // Create scan with detected ingredients
    $scan = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(5),
        'meal_name' => 'Pizza',
        'status' => 'completed',
        'detected_ingredients' => [
            ['name' => 'cheese', 'category' => 'dairy'],
            ['name' => 'tomato sauce', 'category' => 'vegetable'],
        ],
    ]);

    $response = postJson('/api/attacks', [
        'onset_at' => $attackTime->toISOString(),
        'pain_intensity' => 6,
        'duration_minutes' => 60,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();
    $attackId = $response->json('attack.id');

    // Verify match_weight is calculated
    $correlation = \DB::table('attack_scans')
        ->where('attack_id', $attackId)
        ->where('scan_id', $scan->id)
        ->first();

    expect($correlation)->not->toBeNull();
    expect($correlation->match_weight)->toBeGreaterThan(0);
    expect($correlation->match_weight)->toBeLessThanOrEqual(100);
});

test('attack correlation works with multiple scans', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attackTime = now();

    // Create multiple scans in the 3-8 hour window
    $scans = [];
    for ($i = 3; $i <= 8; $i++) {
        $scans[] = Scan::factory()->for($user)->create([
            'created_at' => $attackTime->copy()->subHours($i),
            'meal_name' => "Meal {$i} hours ago",
            'status' => 'completed',
        ]);
    }

    $response = postJson('/api/attacks', [
        'onset_at' => $attackTime->toISOString(),
        'pain_intensity' => 9,
        'duration_minutes' => 150,
        'symptoms' => ['severe pain', 'vomiting'],
        'medical_care_type' => 'emergency_room',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();
    $attackId = $response->json('attack.id');

    // Verify all scans in window are correlated
    foreach ($scans as $scan) {
        $this->assertDatabaseHas('attack_scans', [
            'attack_id' => $attackId,
            'scan_id' => $scan->id,
        ]);
    }

    // Verify total correlation count
    $correlationCount = \DB::table('attack_scans')
        ->where('attack_id', $attackId)
        ->count();

    expect($correlationCount)->toBe(count($scans));
});

test('attack correlation only correlates completed scans', function () {
    $user = User::factory()->create();
    $token = $user->createToken('mobile-app')->plainTextToken;

    $attackTime = now();

    // Create completed scan
    $completedScan = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(5),
        'status' => 'completed',
    ]);

    // Create analyzing scan (should not be correlated)
    $analyzingScan = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(6),
        'status' => 'analyzing',
    ]);

    // Create failed scan (should not be correlated)
    $failedScan = Scan::factory()->for($user)->create([
        'created_at' => $attackTime->copy()->subHours(7),
        'status' => 'failed',
    ]);

    $response = postJson('/api/attacks', [
        'onset_at' => $attackTime->toISOString(),
        'pain_intensity' => 7,
        'duration_minutes' => 100,
        'symptoms' => ['pain'],
        'medical_care_type' => 'none',
    ], [
        'Authorization' => "Bearer {$token}",
    ]);

    $response->assertCreated();
    $attackId = $response->json('attack.id');

    // Only completed scan should be correlated
    $this->assertDatabaseHas('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $completedScan->id,
    ]);

    $this->assertDatabaseMissing('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $analyzingScan->id,
    ]);

    $this->assertDatabaseMissing('attack_scans', [
        'attack_id' => $attackId,
        'scan_id' => $failedScan->id,
    ]);
});
