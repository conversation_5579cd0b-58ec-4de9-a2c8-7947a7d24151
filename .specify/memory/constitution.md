<!--
Sync Impact Report:
- Version change: none → 1.0.0 (initial version)
- Modified principles: N/A (new constitution)
- Added sections: Core Principles (7), Security & Privacy, Performance Standards, Governance
- Removed sections: N/A
- Templates requiring updates:
  ✅ plan-template.md - Constitution Check section references this file
  ✅ spec-template.md - Requirements align with principles
  ✅ tasks-template.md - Task phases align with testing principles
- Follow-up TODOs: None
-->

# GallDiet Constitution

## Core Principles

### I. Personalization-First Architecture

Every feature MUST be designed to leverage user profile data for personalized experiences. Generic, one-size-fits-all approaches are explicitly rejected.

**Requirements:**
- All food analysis features MUST incorporate user's known triggers, allergies, and dietary preferences
- Safety scoring algorithms MUST adjust based on individual user history and confirmed triggers
- AI prompts MUST include complete user profile context for personalized recommendations
- Database queries MUST efficiently support profile-based filtering and personalization

**Rationale:** GallDiet's core value proposition is discovering each user's unique triggers. Generic advice fails users and undermines the product's competitive advantage.

### II. API-First Development

All backend functionality MUST be exposed through RESTful JSON APIs. No server-side rendering or Blade views permitted.

**Requirements:**
- All API routes MUST use Laravel Sanctum authentication (`auth:sanctum` middleware)
- All responses MUST use Eloquent API Resources for consistent JSON formatting
- All endpoints MUST return appropriate HTTP status codes (200, 201, 204, 400, 401, 403, 404, 422, 500)
- API contracts MUST be documented before implementation
- Mobile app MUST communicate exclusively via API endpoints

**Rationale:** Clean separation between backend API and mobile frontend enables independent development, testing, and future platform expansion (web, wearables).

### III. Test-Driven Development (NON-NEGOTIABLE)

All code changes MUST be validated by automated tests. Code without tests is considered incomplete.

**Requirements:**
- Tests MUST be written using Pest 4 framework
- Feature tests MUST validate complete user journeys and API contracts
- Unit tests MUST cover business logic and edge cases
- Browser tests MUST validate critical user flows (onboarding, scanning, personalization)
- All tests MUST pass before code review approval
- Tests MUST be run using `php artisan test --filter=<relevant>` after changes

**Test Coverage Targets:**
- API endpoints: 100% contract coverage
- User profile logic: 100% unit coverage
- Personalization algorithms: 100% unit coverage
- Critical user flows: Browser test coverage

**Rationale:** Medical and health applications require exceptional reliability. TDD prevents regressions and ensures personalization logic works correctly for diverse user profiles.

### IV. User Experience Consistency

Mobile app MUST deliver smooth, delightful interactions that reduce food anxiety and build trust.

**Requirements:**
- Loading states MUST show progress with personalized messaging ("Checking against YOUR triggers...")
- Interactive states MUST provide haptic feedback and smooth animations (NativeWind transitions)
- Error messages MUST be helpful and guide users toward resolution
- First scan result MUST demonstrate personalization within 5 minutes of onboarding
- Profile setup MUST complete in under 3 minutes with progress indicators
- Empty states MUST encourage action with clear next steps

**Performance Targets:**
- Scan analysis response: < 4 seconds (2-4 second target)
- Screen transitions: < 100ms with smooth animations
- API response times: < 200ms p95
- Profile queries: < 50ms with proper indexing

**Rationale:** Users are anxious about food choices. Smooth, responsive UX builds confidence and trust, directly impacting retention and perceived value.

### V. Medical Responsibility & Safety

Health guidance MUST prioritize user safety and never substitute for professional medical care.

**Requirements:**
- Emergency support features MUST prominently direct users to seek immediate medical attention
- App MUST NOT diagnose, treat, cure, or prevent disease
- Medical disclaimers MUST appear on all screens providing health guidance
- Allergen warnings MUST be impossible to miss (safety score: 0, prominent visual alerts)
- Attack documentation MUST encourage professional medical evaluation
- ER visit suggestions MUST never be discouraged or downplayed

**Rationale:** FDA compliance requires clear positioning as wellness tool, not medical device. User safety is paramount and builds trust with medical professionals.

### VI. Data Privacy & Security

User health data MUST be protected with defense-in-depth security practices.

**Requirements:**
- All user profile data MUST be encrypted at rest
- All API communication MUST use HTTPS/TLS
- Authentication MUST use Laravel Sanctum with secure token management
- User data MUST NOT be shared without explicit opt-in consent
- Personal health information (PHI) MUST follow HIPAA-aligned practices
- Data exports MUST be user-controlled and portable
- Privacy policy MUST be GDPR and CCPA compliant

**Rationale:** Health data is deeply personal. Privacy violations destroy trust and create legal liability. Transparency and user control over data builds confidence.

### VII. Performance & Scalability

System MUST maintain responsiveness under growing user load and data volume.

**Requirements:**
- Database queries MUST prevent N+1 problems via eager loading
- Profile data MUST be cached in Redis for fast retrieval
- Long-running operations (AI analysis) MUST use Laravel queues (ShouldQueue)
- API endpoints MUST implement rate limiting to prevent abuse
- Large datasets MUST use pagination and lazy loading
- Mobile app MUST cache profile data for offline safety score calculations

**Performance Budgets:**
- API endpoints: < 200ms p95 latency
- Profile queries: < 50ms with Redis caching
- Scan analysis: < 4 seconds end-to-end
- Database migrations: < 30 seconds execution time
- Mobile app bundle: < 5MB initial download

**Rationale:** Slow performance undermines user confidence in AI analysis and personalization. Medical decisions require reliable, fast responses.

## Security & Privacy Standards

**Authentication & Authorization:**
- Laravel Sanctum MUST be used for all API authentication
- Token expiration MUST be configured appropriately (24-hour default, 7-day remember me)
- Password reset MUST use Laravel's built-in secure flow
- Multi-factor authentication MUST be supported for premium users

**Data Handling:**
- User triggers and attack history MUST be encrypted at rest (database encryption)
- Food scan images MUST be processed server-side and not permanently stored without consent
- Profile exports MUST be encrypted in transit and access-controlled
- Deletion requests MUST purge all user data within 30 days (GDPR compliance)

**API Security:**
- All endpoints MUST validate input using Laravel Form Requests
- CSRF protection MUST be enabled for web endpoints
- CORS MUST be configured to allow only authorized origins
- SQL injection MUST be prevented via Eloquent ORM (no raw queries without parameterization)

## Performance Standards

**Backend (Laravel API):**
- Eloquent queries MUST use eager loading to prevent N+1 problems
- Complex queries MUST be profiled with Laravel Debugbar in development
- Redis caching MUST be used for frequently accessed profile data
- Queue jobs MUST handle time-consuming operations (AI calls, pattern analysis)
- Database indexes MUST support common query patterns (user_id, created_at, trigger lookups)

**Frontend (React Native):**
- Profile data MUST be cached locally using AsyncStorage for offline access
- Images MUST be lazy-loaded and optimized for mobile networks
- API calls MUST implement retry logic with exponential backoff
- State management MUST use React Context efficiently (avoid unnecessary re-renders)
- Bundle size MUST be monitored and kept under 5MB

**AI & External Services:**
- LLM API calls MUST have 10-second timeout with graceful degradation
- Backup LLM providers MUST be configured for failover
- Rate limiting MUST prevent API quota exhaustion
- Error responses MUST cache generic fallback recommendations

## Governance

**Constitutional Authority:**
- This constitution supersedes all other development practices and guidelines
- All code reviews MUST verify compliance with constitutional principles
- Non-compliance MUST be justified in `plan.md` Complexity Tracking section
- Amendments require documentation, team approval, and migration plan

**Amendment Process:**
- MAJOR version bump: Backward-incompatible principle changes or removals (requires stakeholder approval)
- MINOR version bump: New principle additions or materially expanded guidance (requires tech lead approval)
- PATCH version bump: Clarifications, wording improvements, typo fixes (requires code review)

**Compliance Review:**
- Every feature specification MUST include Constitution Check in `plan.md`
- Violations of principles MUST be explicitly justified in Complexity Tracking section
- Pull requests MUST include constitutional compliance verification
- Quarterly audits MUST review adherence to performance and security standards

**Development Workflow:**
- CLAUDE.md provides runtime development guidance for AI assistants
- Templates in `.specify/templates/` MUST align with constitutional principles
- All technical decisions MUST reference relevant constitutional principles
- Deviations require architectural decision record (ADR) documentation

**Version**: 1.0.0 | **Ratified**: 2025-10-11 | **Last Amended**: 2025-10-11
