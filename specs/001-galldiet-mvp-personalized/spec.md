# Feature Specification: GallDiet MVP - Personalized Gallstone Diet Management

**Feature Branch**: `001-galldiet-mvp-personalized`
**Created**: 2025-10-12
**Status**: Draft
**Input**: User description: "GallDiet MVP - Personalized Gallstone Diet Management Application"

## User Scenarios & Testing *(mandatory)*

### User Story 1 - New User Discovers Personal Triggers Through Systematic Scanning (Priority: P1)

<PERSON>, a 38-year-old software engineer, was recently diagnosed with gallstones. She's frustrated by generic "avoid fatty foods" advice that doesn't account for individual differences. She downloads GallDiet to identify her specific triggers quickly and scientifically.

**<PERSON>'s Journey**:
1. Completes onboarding (7-8 screens, under 3 minutes) - no known triggers yet
2. App reassures: "Don't know your triggers? We'll help you discover them!"
3. <PERSON>ans her first meal (grilled chicken salad) - sees personalized result: "Score: 68/100. Since you're new and don't have confirmed triggers yet, we're being conservative."
4. Scans meals systematically over 2-3 weeks, logging symptoms after eating
5. Day 18: App detects pattern - "<PERSON><PERSON> detected: You've had symptoms 3-5 hours after eating coconut products (3 of 3 instances). Confidence: 87%"
6. <PERSON> confirms coconut as trigger
7. Breakthrough moment: "I never would have guessed coconut! I thought it was all fat, but I eat avocados fine."
8. Continues scanning - app now adjusts all future scores based on her coconut trigger

**Why this priority**: Core value proposition demonstration. Proves personalization works and differentiates from generic apps. This is the "aha moment" that converts free users to believers and eventually Premium subscribers.

**Independent Test**: Can be fully tested by creating a test user account, completing onboarding without triggers, scanning 10+ meals with symptom logging, and verifying the pattern detection identifies correlations after logging 3+ instances of the same food followed by symptoms.

**Acceptance Scenarios**:

1. **Given** a new user with no medical history, **When** they complete onboarding and indicate "I don't know my triggers yet", **Then** they see reassuring message and conservative safety scoring
2. **Given** a user has scanned the same ingredient 3 times and logged symptoms each time, **When** pattern detection runs, **Then** system suggests this as a potential trigger with confidence score
3. **Given** a user confirms a trigger, **When** they scan future meals containing that trigger, **Then** safety scores drop 15-40 points and prominent warnings appear
4. **Given** a user scans foods without their triggers, **When** viewing results, **Then** scores reflect general gallstone guidelines without personal penalties
5. **Given** a user has been using the app for 2-4 weeks, **When** they review their progress, **Then** they see at least one identified trigger or clear path toward identification

---

### User Story 2 - Experienced User Sees Immediate Personalization Validation (Priority: P1)

Amanda, a 45-year-old marketing manager, was diagnosed 8 months ago. Through painful trial and error, she knows fried foods and ice cream trigger attacks, but she can eat avocados fine. She's frustrated that MyFitnessPal treats all fats the same. She downloads GallDiet seeking personalized guidance.

**Amanda's Journey**:
1. Completes onboarding - enters known triggers: "Fried foods (High severity)" and "Full-fat dairy (High severity)"
2. Profile completeness: 85% (gamification encourages completion)
3. Scans leftover pizza immediately to test the app
4. Sees personalized result: "Safety Score: 35/100 FOR YOU. This contains fried dough and full-fat cheese, which match YOUR High severity triggers. You've had 3 attacks after eating fried foods."
5. Reaction: "Wow, it actually used MY triggers! This IS different!"
6. Scans avocado toast next - sees: "Safety Score: 88/100. While avocados are high-fat, they don't match your specific triggers and are generally well-tolerated by users with your profile."
7. Validation moment: "Finally an app that understands everyone is different!"
8. Uses app daily, hits 3-scan limit by day 5
9. Sees upgrade prompt: "You're discovering YOUR triggers quickly. Premium gives unlimited scans."
10. Upgrades to Premium on day 14 after seeing clear value

**Why this priority**: Critical for user retention. Users with known triggers need immediate proof that personalization works, otherwise they churn within 48 hours. This validates the value proposition instantly and drives Premium conversions.

**Independent Test**: Can be fully tested by creating a user account, setting 2-3 known triggers in onboarding, scanning meals that contain those triggers, and verifying the results show personalized warnings with trigger-specific language referencing the user's profile.

**Acceptance Scenarios**:

1. **Given** a user enters "fried foods" as a High severity trigger, **When** they scan fried chicken, **Then** score drops to 15-35 range with prominent warning: "Contains YOUR trigger: fried foods"
2. **Given** a user has allergens in their profile, **When** they scan a product containing that allergen, **Then** score drops to 0 with critical alert
3. **Given** a user scans a high-fat food that's NOT their trigger, **When** viewing results, **Then** score is moderate with note: "While high-fat, this doesn't match your specific triggers"
4. **Given** a user hits their 3rd scan of the day on free tier, **When** they attempt a 4th scan, **Then** they see upgrade prompt with clear value messaging
5. **Given** a Premium user, **When** they scan unlimited times, **Then** all scans work without restriction

---

### User Story 3 - Busy Traveler Gets Quick Restaurant Decisions (Priority: P2)

Brian, a 52-year-old sales executive, travels constantly for work. He eats 90% of meals at restaurants and has vague understanding of his triggers ("greasy food"). He needs quick yes/no answers at the point of decision to avoid attacks during client meetings.

**Brian's Journey**:
1. Completes onboarding quickly (skips some optional sections - 70% complete)
2. Enters known trigger: "Greasy food" (general category)
3. At steakhouse for client dinner, needs fast answer
4. Scans pasta carbonara with camera - sees: "Safety Score: 35/100. Heavy cream, bacon - matches your 'greasy food' trigger."
5. Scans grilled salmon instead - sees: "Safety Score: 84/100. Grilled fish, light preparation - safe for your profile."
6. Orders salmon confidently, no attack
7. Uses app regularly during travel, values convenience over comprehensive tracking
8. Month 2: Realizes app saved him from 3+ potential attacks during business trips
9. Upgrades to Premium for unlimited scans while traveling

**Why this priority**: Represents significant user segment (busy professionals) and demonstrates immediate utility value. Shows app works for users at different engagement levels - not everyone needs comprehensive tracking. Drives steady Premium conversions from convenience-seeking users.

**Independent Test**: Can be fully tested by creating a user with general trigger category, scanning multiple restaurant meals in quick succession, and verifying results display quickly (< 4 seconds) with clear safety scores and simple reasoning.

**Acceptance Scenarios**:

1. **Given** a user with general trigger "greasy food", **When** they scan high-fat meals, **Then** scores reflect moderate to high risk with explanations
2. **Given** a user scans a meal photo, **When** backend processes, **Then** results appear within 2-4 seconds with engaging loading states
3. **Given** a user scans multiple items quickly, **When** viewing scan history, **Then** all scans are saved with timestamps and safety scores
4. **Given** a user marks a scan as "I ate this" with no symptoms, **When** they scan similar meals later, **Then** system notes: "You've safely eaten similar meals"
5. **Given** a user is at 70% profile completion, **When** they use the app, **Then** they see occasional prompts to complete profile for better accuracy

---

### User Story 4 - User Experiences Attack and Identifies Trigger Through Correlation (Priority: P1)

Amanda has been using GallDiet for 3 weeks. She's scanned regularly and avoided her known triggers. Today she ate chocolate (scanned it, score was 45/100), and 6 hours later experienced a gallstone attack.

**Attack Journey**:
1. Attack starts - severe pain
2. Amanda opens GallDiet, taps prominent "Emergency Support" button
3. Sees: "SEEK MEDICAL CARE NOW" with big buttons for Call 911, Find ER, etc.
4. Goes to ER (app never discourages this)
5. In waiting room, documents symptoms using app (pain location, intensity, start time)
6. App auto-pulls recent meals from scan history for medical staff
7. Generates shareable PDF symptom report to show doctor
8. After treatment, returns home
9. App prompts: "Welcome back. Would you like to record details about this episode?"
10. Amanda logs the attack
11. App analyzes: "Pattern detected: You ate chocolate 6 hours before attack. You've also had: chocolate cake (3 weeks ago) → symptoms, hot chocolate (2 weeks ago) → discomfort."
12. Suggests: "Chocolate may be a trigger for you (Confidence: 82%)"
13. Amanda confirms chocolate as new trigger
14. Breakthrough: "I never connected these dots! Chocolate specifically, not all sweets."
15. All future scans now check for chocolate and warn accordingly

**Why this priority**: Demonstrates the core pattern detection capability that creates long-term value and user lock-in. Shows responsible medical guidance (always directing to ER). The attack correlation feature is what enables trigger discovery beyond just user guessing.

**Independent Test**: Can be fully tested by creating attack logs with specific timestamps, verifying system correlates with scan history from 3-8 hours prior, and confirming trigger suggestions appear with confidence scores when 2+ correlations exist.

**Acceptance Scenarios**:

1. **Given** a user taps Emergency Support, **When** screen loads, **Then** prominent "SEEK MEDICAL CARE NOW" message displays with ER/911 buttons
2. **Given** a user documents attack symptoms, **When** they request report, **Then** system generates PDF with symptoms + recent meals from scan history
3. **Given** a user logs attack, **When** system analyzes history, **Then** meals from 3-8 hours prior are checked for correlation patterns
4. **Given** a food appears in 2+ attack pre-windows, **When** pattern detection runs, **Then** system suggests it as potential trigger with confidence score
5. **Given** a user confirms an attack-correlated trigger, **When** they scan future foods with that ingredient, **Then** warnings reference attack history: "This caused an attack for you on [date]"
6. **Given** a user has been attack-free for 30+ days, **When** they open app, **Then** they see celebration: "30-day attack-free streak!"

---

### User Story 5 - User Modifies Risky Recipe to Make It Safe (Priority: P2)

Amanda scans pasta carbonara at home and sees score 20/100 (contains her triggers: heavy cream, bacon). She wants to eat it but needs a safer version.

**Recipe Modification Journey**:
1. Scans meal - sees low score with trigger warnings
2. Taps "See Safer Version" button
3. AI generates modified recipe personalized to her triggers:
   - Original: Heavy cream → Modified: Greek yogurt (avoids her full-fat dairy trigger)
   - Original: Regular bacon → Modified: Turkey bacon
   - Shows nutritional comparison: 850 cal → 520 cal, 24g sat fat → 6g sat fat
4. New safety score for modified version: 82/100
5. Simple cooking instructions (5 steps)
6. Amanda cooks modified version
7. Saves to "My Safe Recipes" collection
8. Scans it next time - system recognizes: "This is your modified carbonara (Score: 82)"

**Why this priority**: Key Premium feature that provides ongoing value beyond just "avoid this." Helps users maintain variety and enjoyment while managing triggers. Strong retention driver because users build recipe collections over time.

**Independent Test**: Can be fully tested by scanning a meal that scores < 50, requesting modification, and verifying the system generates substitutions that specifically address the user's personal triggers (not generic advice).

**Acceptance Scenarios**:

1. **Given** a scanned meal scores below 50, **When** results display, **Then** "See Safer Version" button appears
2. **Given** a user requests recipe modification, **When** AI processes, **Then** substitutions specifically address user's personal triggers
3. **Given** a modified recipe is generated, **When** user views it, **Then** system shows nutritional comparison and new personalized safety score
4. **Given** a user saves modified recipe, **When** they scan it later, **Then** system recognizes it from their collection
5. **Given** a free tier user views recipe modification, **When** they try to access details, **Then** they see Premium upsell

---

### User Story 6 - User Manages Profile and Subscription (Priority: P3)

Amanda wants to update her profile as she learns more about herself, view her complete trigger list, and manage her Premium subscription.

**Profile Management Journey**:
1. Opens Profile tab
2. Sees profile completeness: 85% with suggestions to improve
3. Updates severity level from "Moderate" to "Mild" after 3 attack-free months
4. Adds new discovered trigger: "Chocolate (Moderate severity)"
5. Reviews trigger list - sees all triggers with how they were identified:
   - Fried foods (User input) - 4 correlations
   - Full-fat dairy (User input) - 3 correlations
   - Chocolate (Pattern detected, User confirmed) - 2 correlations
6. Adjusts dietary preferences (adds "Low-carb")
7. Views subscription details
8. Sees: "Premium since Day 14, $9.99/month, Next billing: Oct 28"
9. Reviews Premium benefits unlocked
10. Manages payment method

**Why this priority**: Essential for user control and transparency, but lower priority than core scanning features. Users need this but it's not the primary value driver. Good implementation builds trust and supports retention.

**Independent Test**: Can be fully tested by navigating to profile section, updating various profile fields, and verifying changes are saved and reflected in future scan analyses.

**Acceptance Scenarios**:

1. **Given** a user opens Profile tab, **When** screen loads, **Then** they see profile completeness percentage with specific improvement suggestions
2. **Given** a user updates trigger severity, **When** they scan meals with that trigger, **Then** scoring algorithm reflects new severity level
3. **Given** a user views trigger list, **When** displayed, **Then** each trigger shows source (User input, Pattern detected, Attack correlated) and statistics
4. **Given** a Premium user views subscription, **When** they access settings, **Then** they see billing date, amount, cancel/modify options
5. **Given** a user cancels Premium, **When** they continue using app, **Then** they revert to free tier limits after billing period ends

---

### Edge Cases

**Profile & Onboarding**:
- What happens when a user skips onboarding entirely? System uses most conservative scoring (everything treated as potential risk) and prompts to complete profile for better accuracy.
- What happens if a user enters contradictory information (allergen + preference for that food)? System prioritizes allergen (safety first) and flags contradiction for user review.
- What happens if a user has 20+ triggers? System continues to track all but suggests they consult gastroenterologist (this level of restriction is unusual).

**Scanning & Analysis**:
- What happens when barcode isn't in Open Food Facts database? System offers to submit product for future addition and suggests manual meal photo scan.
- What happens when AI can't identify meal ingredients with confidence? System returns results with low confidence score and suggests trying different photo angle or lighting.
- What happens when camera permission is denied? System shows helpful message explaining camera is required for core functionality with link to settings.
- What happens when user is offline? Barcode scanning fails gracefully with "Internet required" message. Previously viewed scans remain viewable from cache.
- What happens when AI service (Gemini) is down? System shows error: "Analysis temporarily unavailable" and offers to retry or queue for later processing.
- What happens when scan takes longer than 10 seconds? System shows extended loading state: "Still analyzing... complex meal" and implements 10-second timeout with graceful failure.

**Pattern Detection & Triggers**:
- What happens when system detects conflicting patterns (food causes attacks sometimes but not others)? System notes inconsistency and suggests other factors (portion size, preparation method, food combinations).
- What happens when user rejects a suggested trigger 3 times? System stops suggesting it and logs as "user disagreement" for potential future analysis.
- What happens when user wants to delete attack history? System allows deletion with warning: "This will affect trigger pattern detection accuracy."

**Free vs Premium**:
- What happens when free user hits scan limit at critical moment (restaurant)? Prominent but respectful upgrade prompt with option to use barcode scan (unlimited) instead.
- What happens when Premium subscription payment fails? Grace period of 3 days with reminders, then revert to free tier limits.
- What happens when user cancels trial before 7 days? Immediate revert to free tier, no charge, can restart trial later (one trial per user lifetime).

**Emergency & Medical**:
- What happens if user repeatedly uses Emergency Support without going to ER? System doesn't restrict access (safety first) but notes pattern and includes in health reports.
- What happens when user tries to scan medication barcodes? System detects and shows: "This appears to be medication. GallDiet is for food only. Consult your doctor about medication."
- What happens when location services are disabled but user taps "Find Nearest ER"? System requests location permission with explanation, falls back to manual address entry if denied.

**Data & Privacy**:
- What happens when user requests data export? System generates comprehensive JSON file with all profile data, scans, attacks, triggers within 24 hours (GDPR compliance).
- What happens when user requests account deletion? System sends confirmation email, 30-day grace period, then permanent deletion of all data.
- What happens when user switches devices? Data syncs from backend via account login, AsyncStorage cache rebuilds from API.

## Requirements *(mandatory)*

### Functional Requirements

**Authentication & Profile Management**:

- **FR-001**: System MUST allow users to create accounts with email/password authentication
- **FR-002**: System MUST provide 7-8 screen onboarding flow collecting condition type, severity, known triggers, dietary preferences, allergies, and health goals
- **FR-003**: System MUST support users indicating "I don't know my triggers yet" during onboarding without blocking progress
- **FR-004**: System MUST calculate and display profile completeness percentage (0-100%)
- **FR-005**: System MUST encrypt all user profile data at rest
- **FR-006**: Users MUST be able to update their profile information at any time
- **FR-007**: System MUST allow users to add, edit, and remove trigger foods with severity ratings (Low, Moderate, High)
- **FR-008**: System MUST track how each trigger was identified (User input, Pattern detected, Attack correlated)
- **FR-009**: Users MUST be able to manage dietary preferences (vegetarian, vegan, gluten-free, dairy-free, etc.)
- **FR-010**: Users MUST be able to specify food allergies separately from intolerances

**Food Scanning - Barcode**:

- **FR-011**: System MUST provide camera interface for scanning product barcodes
- **FR-012**: System MUST integrate with Open Food Facts API for barcode product lookup
- **FR-013**: System MUST display personalized safety score (0-100) with < 2 seconds p95 latency after successful barcode scan
- **FR-014**: System MUST color-code safety scores: Green (80-100), Yellow (50-79), Red (0-49)
- **FR-015**: System MUST explain WHY the score was assigned based on user's specific profile
- **FR-016**: System MUST show prominent warnings when scanned product contains user's known triggers
- **FR-017**: System MUST set safety score to 0 and show critical alert when product contains user's allergens
- **FR-018**: System MUST be unlimited for free tier users (barcode scanning only)
- **FR-019**: System MUST save each barcode scan to user's scan history with timestamp

**Food Scanning - Meal Photos**:

- **FR-020**: System MUST provide camera interface for photographing meals
- **FR-021**: System MUST upload meal photos to backend for AI analysis
- **FR-022**: System MUST show engaging loading states during analysis: "Analyzing..." → "Checking YOUR profile..." → "Calculating YOUR score..."
- **FR-023**: System MUST complete meal photo analysis and return results within 2-4 seconds (p95 latency)
- **FR-024**: System MUST use Gemini 2.0 Flash Thinking (primary) with automatic fallback to Gemini 2.5 Flash on failure for meal analysis
- **FR-025**: System MUST include complete user profile context in AI analysis prompts
- **FR-026**: System MUST display detected ingredients, problem ingredients (for this user), and safe ingredients
- **FR-027**: System MUST show AI confidence score indicating certainty of ingredient identification
- **FR-028**: System MUST limit meal photo scans to 3 per day for free tier users
- **FR-029**: System MUST track daily scan count and reset at midnight user local time
- **FR-030**: Premium users MUST have unlimited meal photo scans

**Personalization Engine**:

- **FR-031**: System MUST adjust safety scores based on user's specific triggers: -15 points (Low severity), -25 points (Moderate), -40 points (High)
- **FR-032**: System MUST apply additional -10 point penalty for users with "Severe" condition level on scores below 80
- **FR-033**: System MUST override all scoring and set to 0 when allergen is detected
- **FR-034**: System MUST compare scanned items against user's scan history and note: "You've safely eaten similar meals"
- **FR-035**: System MUST reference user's attack history when triggers are detected: "This caused an attack for you on [date]" (format: "Jan 15, 2025 at 3:42 PM" in user's local timezone)
- **FR-036**: System MUST consider user's dietary preferences when generating recommendations and alternatives
- **FR-037**: System MUST personalize all messaging with "YOUR" language: "Based on YOUR triggers..." "Safe FOR YOU..."

**Scan History & Tracking**:

- **FR-038**: System MUST maintain comprehensive list of all user scans (barcodes and photos)
- **FR-039**: Users MUST be able to filter scan history by date range, safety score, meal type, and triggers detected
- **FR-040**: Users MUST be able to search scan history by food name or ingredient
- **FR-041**: System MUST display each scan with photo/product image, date/time, safety score, and consumption status
- **FR-042**: Users MUST be able to mark scans as: "I ate this", "I avoided this", or "I modified this"
- **FR-043**: Users MUST be able to log optional symptoms after eating (none, mild discomfort, moderate symptoms, attack)
- **FR-044**: Users MUST be able to create custom collections/favorites (e.g., "My Safe Breakfasts")
- **FR-045**: System MUST generate weekly summary showing scan count, average safety score, and trend analysis
- **FR-046**: Free tier users MUST have 90-day scan history retention; Premium users have unlimited retention
- **FR-047**: System MUST cache recent scan history locally for offline viewing

**Recipe Modification**:

- **FR-048**: System MUST automatically offer recipe modification when scanned meal scores below 50
- **FR-049**: System MUST generate ingredient substitutions specifically tailored to user's personal triggers
- **FR-050**: System MUST show nutritional comparison between original and modified versions
- **FR-051**: System MUST provide simple cooking instructions (maximum 5 steps)
- **FR-052**: System MUST calculate new safety score for modified recipe based on substitutions
- **FR-053**: Users MUST be able to save modified recipes to personal collection
- **FR-054**: Recipe modification MUST be Premium-only feature (free users see teaser)
- **FR-055**: System MUST recognize previously saved recipes when scanned again

**Emergency Support**:

- **FR-056**: System MUST display prominent "Emergency Support" button on home screen
- **FR-057**: System MUST show "SEEK MEDICAL CARE NOW" message when user indicates severe pain
- **FR-058**: System MUST provide one-tap buttons for: Call 911, Find Nearest ER (GPS), Call My Doctor, Text Emergency Contact
- **FR-059**: System MUST never discourage or downplay the need for emergency room visits
- **FR-060**: System MUST provide symptom documentation interface: pain start time, location (body diagram), intensity (1-10 slider), symptoms checklist
- **FR-061**: System MUST auto-pull recent meals from scan history for symptom report
- **FR-062**: System MUST generate shareable PDF/text symptom summary for medical staff
- **FR-063**: System MUST include calming breathing exercise clearly marked as "comfort only, not treatment"
- **FR-064**: System MUST provide post-attack logging after medical care: episode details, diagnosis, treatment received
- **FR-065**: System MUST correlate attack with recent scans (3-8 hours prior) to identify potential triggers

**Attack Tracking & Pattern Detection**:

- **FR-066**: Users MUST be able to log attack episodes with full details (date, time, duration, severity, symptoms)
- **FR-067**: System MUST analyze scan history 3-8 hours before logged attack for pattern detection
- **FR-068**: System MUST identify potential triggers when food appears in 2+ attack correlation windows
- **FR-069**: System MUST show confidence scores (0-100%) for trigger suggestions based on correlation strength
- **FR-070**: System MUST present trigger suggestions with evidence: "You ate X, 6 hours later had attack. Pattern occurred 3 times."
- **FR-071**: Users MUST be able to confirm or reject suggested triggers
- **FR-072**: System MUST track attack frequency over time and show trends
- **FR-073**: System MUST celebrate milestones: "Attack-free for 30 days!" with visual animations
- **FR-074**: System MUST stop suggesting triggers user has rejected 3+ times

**Subscription & Payments**:

- **FR-075**: System MUST support free tier with functional feature set (3 photo scans/day, unlimited barcode, 90-day history)
- **FR-076**: System MUST offer Premium tier at $9.99/month with: unlimited scans, unlimited history, recipe modifications, advanced features
- **FR-077**: System MUST display upgrade prompts when: user hits daily limit, user scans 5+ consecutive days, user views Premium features
- **FR-078**: System MUST offer 7-day free trial for Premium (one per user lifetime)
- **FR-079**: System MUST integrate with Stripe via Laravel Cashier for payment processing
- **FR-080**: Users MUST be able to manage subscription (view, cancel, update payment) in Profile settings
- **FR-081**: System MUST implement 3-day grace period for failed payments before reverting to free tier
- **FR-082**: System MUST allow subscription cancellation anytime with access until end of billing period

**Navigation & User Experience**:

- **FR-083**: System MUST provide bottom navigation with 4 tabs: Home (with profile button in header), Scan, Patterns, History. Settings/Profile accessed from Home header button.
- **FR-084**: System MUST complete all screen transitions in under 300ms
- **FR-085**: System MUST provide haptic feedback for key interactions (scan complete, achievement unlocked)
- **FR-086**: System MUST use expandable detail sections to avoid overwhelming users
- **FR-087**: System MUST implement minimum 44pt touch targets for all interactive elements
- **FR-088**: System MUST show progress indicators for multi-step processes (onboarding, analysis)
- **FR-089**: System MUST display celebration animations for achievements (first scan, trigger discovered, attack-free milestones)
- **FR-090**: System MUST use consistent color-coding throughout: Green (safe), Yellow (moderate), Red (avoid)

**Accessibility**:

- **FR-091**: All interactive elements MUST have screen reader labels
- **FR-092**: System MUST maintain minimum 44pt touch target size
- **FR-093**: System MUST meet WCAG AA color contrast standards (4.5:1 for text)
- **FR-094**: System MUST scale text with system font size settings
- **FR-095**: System MUST respect "reduce motion" system preference for animations

**Data & Privacy**:

- **FR-096**: System MUST encrypt user profile data, trigger lists, and attack history at rest
- **FR-097**: System MUST use HTTPS/TLS for all API communication
- **FR-098**: System MUST not share user data between users without explicit opt-in consent
- **FR-099**: System MUST provide data export feature (comprehensive JSON file within 24 hours)
- **FR-100**: System MUST provide account deletion with 30-day grace period and complete data purge
- **FR-101**: System MUST display medical disclaimers on all screens providing health guidance
- **FR-102**: System MUST use AsyncStorage (not localStorage) for mobile app local caching

**Technical Constraints**:

- **FR-103**: Meal photo AI analysis MUST require internet connection (cloud-based LLMs)
- **FR-104**: Scan history MUST be viewable offline from local cache
- **FR-105**: System MUST request and handle camera permissions appropriately
- **FR-106**: System MUST request and handle location permissions for "Find Nearest ER" feature
- **FR-107**: System MUST implement 10-second timeout for AI analysis with graceful failure
- **FR-108**: System MUST handle Open Food Facts API failures gracefully (suggest photo scan alternative)

### Key Entities

- **User Profile**: Represents complete user health and dietary information including condition type (gallstones, post-surgery), severity level (mild, moderate, severe), diagnosis date, known trigger foods with severities, dietary preferences (vegetarian, vegan, gluten-free, etc.), food allergies (life-threatening) vs intolerances (discomfort), health goals, subscription tier (free, premium), profile completeness percentage. Related to: Triggers, Scans, Attacks, Recipes.

- **Trigger Food**: Represents specific food or ingredient that causes adverse reactions for a user. Attributes: food name/category, severity level (low, moderate, high), identification source (user input, pattern detected, attack correlated), confidence score, attack correlation count, date added, date last triggered, user notes. Related to: User Profile, Attacks, Scans.

- **Scan**: Represents single instance of food analysis (barcode or photo). Attributes: scan type (barcode/photo), timestamp, food name/description, detected ingredients, safety score (0-100), score reasoning, confidence level, photo/product image, consumption status (ate, avoided, modified), symptom logging (optional), meal type (breakfast, lunch, dinner, snack), location (optional), collection tags. Related to: User Profile, Triggers, Attacks.

- **Attack Episode**: Represents gallstone attack occurrence. Attributes: date/time of onset, duration, pain intensity (1-10), pain location (body diagram data), symptoms checklist (nausea, vomiting, fever, etc.), medical care sought (ER, urgent care, doctor, none), diagnosis received, treatment provided, user notes. Related to: User Profile, Scans (3-8 hours prior), Triggers (correlated).

- **Recipe Modification**: Represents AI-generated safer version of risky meal. Attributes: original meal name, original safety score, modified meal name, modified safety score, ingredient substitutions (original → replacement with reasoning), nutritional comparison (calories, fat, saturated fat), cooking instructions (max 5 steps), date created, user rating (optional). Related to: User Profile, Triggers, Scans.

- **Pattern Detection Result**: Represents AI-identified correlation between food and symptoms/attacks. Attributes: suspected trigger food, confidence score (0-100%), evidence (list of scan+attack correlations), correlation count, date range analyzed, user response (confirmed, rejected, pending). Related to: User Profile, Scans, Attacks, Triggers.

- **Subscription**: Represents user's payment and tier information. Attributes: tier (free, premium), billing amount, billing frequency (monthly), start date, next billing date, payment method (Stripe customer ID), trial status (active, used, not_used), cancellation date (if applicable), grace period status. Related to: User Profile.

- **Weekly Summary**: Represents automated analysis of user's week. Attributes: week start date, total scans, average safety score, score trend (up, down, stable), trigger avoidance rate, meals by safety category (safe, moderate, risky), attack-free days, improvement insights, recommendations. Related to: User Profile, Scans, Attacks.

- **Collection**: Represents user-created favorites list. Attributes: name (e.g., "My Safe Breakfasts"), description, scan count, average safety score, date created, last accessed. Related to: User Profile, Scans.

## Success Criteria *(mandatory)*

### Measurable Outcomes

**Onboarding & Activation**:

- **SC-001**: 55-65% of users who start onboarding complete the full profile (all 7-8 screens) within their first session
- **SC-002**: Users complete onboarding in under 3 minutes on average
- **SC-003**: 90% of users complete their first scan within 10 minutes of finishing onboarding
- **SC-004**: Users see personalized results (referencing their specific profile) on their first scan within 5 minutes of profile completion

**Core Functionality Performance**:

- **SC-005**: Barcode scans return results in under 2 seconds (p95 latency)
- **SC-006**: Meal photo analysis completes in 2-4 seconds (p95 latency)
- **SC-007**: Screen transitions complete in under 300ms (p95)
- **SC-008**: Scan history loads within 1 second with 100+ cached items

**User Engagement & Retention**:

- **SC-009**: 60% of users return to app 7 days after first scan (7-day retention)
- **SC-010**: Active users scan an average of 3-5 items per day
- **SC-011**: Users who identify their first trigger within 4 weeks have 70%+ retention at 30 days
- **SC-012**: Users who see personalized trigger warnings in first 3 scans have 50%+ higher 30-day retention

**Pattern Detection & Trigger Identification**:

- **SC-013**: Users identify their first trigger within 2-4 weeks of regular scanning (minimum 10 scans per week)
- **SC-014**: Pattern detection identifies potential triggers with 75%+ accuracy (user confirmation rate)
- **SC-015**: Users confirm 70%+ of AI-suggested triggers as legitimate
- **SC-016**: Attack correlation analysis identifies at least one trigger in 80% of cases with 3+ logged attacks

**Monetization & Conversion**:

- **SC-017**: 12-15% of engaged users (5+ days of scanning) convert to Premium within 30 days
- **SC-018**: Users who hit the 3-scan daily limit convert to Premium at 25%+ rate
- **SC-019**: Free trial to paid Premium conversion rate reaches 60%+
- **SC-020**: Premium subscribers maintain 90%+ monthly retention (10% or less churn)

**User Satisfaction & Value**:

- **SC-021**: Net Promoter Score (NPS) reaches 50+ among users with 30+ days activity
- **SC-022**: 80% of users rate the personalization as "very helpful" or "essential" after 2 weeks
- **SC-023**: 90% of users successfully complete primary task (scan food, get personalized result) on first attempt
- **SC-024**: Users report 60%+ reduction in food-related anxiety after 4 weeks (self-reported survey)

**Medical Responsibility & Safety**:

- **SC-025**: 100% of emergency support interactions display "SEEK MEDICAL CARE" messaging prominently
- **SC-026**: Allergen alerts (score = 0) are impossible to miss - 100% of users notice red critical warnings in testing
- **SC-027**: Medical disclaimers appear on 100% of screens providing health guidance
- **SC-028**: Zero incidents of app discouraging appropriate medical care

**Accessibility**:

- **SC-029**: 100% of interactive elements have screen reader labels (verified via automated testing)
- **SC-030**: Color contrast meets WCAG AA standards (4.5:1) on 100% of text elements
- **SC-031**: App functions correctly at 200% system font size
- **SC-032**: All core features remain usable with "reduce motion" enabled

**Technical Quality**:

- **SC-033**: App maintains 99%+ uptime for core scanning features
- **SC-034**: AI analysis success rate (non-timeout, non-error) reaches 95%+
- **SC-035**: App bundle size stays under 5MB for initial download
- **SC-036**: App handles 10,000+ concurrent users without degradation

**Business Impact**:

- **SC-037**: Achieve $15,000 MRR (1,500 Premium users at $10 average) within 18 months
- **SC-038**: Customer Acquisition Cost (CAC) stays under $20 for organic channels
- **SC-039**: Lifetime Value (LTV) reaches $100+ (10 months average retention at $10/month)
- **SC-040**: LTV:CAC ratio maintains 5:1 or better

## Assumptions

**User Behavior**:
- Users have smartphones with working cameras (iOS 13+ or Android 8+)
- Users are comfortable taking photos of food in public/social settings
- Users will tolerate 2-4 second wait times for AI analysis
- Users understand basic health concepts (fat content, ingredients, symptoms)
- Users can read and comprehend English at 6th-grade level
- Users will scan meals BEFORE eating (not after) for safety guidance

**Medical & Health**:
- Users have been diagnosed with gallstones or related digestive conditions by medical professionals
- Users understand the app is educational, not medical advice
- Users will seek appropriate medical care when experiencing attacks (app encourages this)
- Standard gallstone dietary triggers (high fat, fried foods, dairy, etc.) apply to most users as baseline
- Pattern detection with 3+ correlations provides reliable trigger identification
- Users can distinguish between mild discomfort and medical emergency

**Technical Environment**:
- Users have reliable internet connectivity for initial scans (3G minimum)
- Open Food Facts API maintains 99%+ uptime and adequate coverage
- Gemini AI API maintains sufficient availability and rate limits for user base
- Mobile devices support required camera APIs and permissions
- AsyncStorage provides adequate storage for 90-day history cache (estimated 50-100MB)
- Stripe payment processing maintains PCI compliance and availability

**Market & Competition**:
- Target users are frustrated with generic diet apps (validated by user research)
- Personalization is valued enough to justify $9.99/month pricing
- Users are willing to invest 2-3 minutes in onboarding for better personalization
- Medical professionals will accept patient-generated health reports
- Competitors have not implemented deep personalization in this market yet
- Word-of-mouth and community referrals will drive significant user acquisition

**Business Model**:
- Free tier provides enough value to build trust without cannibalization
- 3 photo scans/day is sufficient for free tier users to see value
- Unlimited barcode scanning as free feature doesn't impact revenue significantly
- Premium conversion rates match or exceed industry standards (12-15%)
- Premium Plus ($19.99/month) will be added in Phase 2 but architecture supports it
- Subscription model is preferred over one-time purchase by target demographic

**Data & Privacy**:
- Users trust cloud storage of health data with proper encryption
- HIPAA-aligned practices (not full HIPAA compliance) are sufficient for wellness app
- Users will opt-in to data collection for pattern detection
- Anonymized aggregate data can inform algorithm improvements
- Data export capability satisfies GDPR/CCPA requirements
- 30-day deletion grace period balances user protection and account recovery

**Development & Deployment**:
- Laravel 12 API backend can scale to 10,000+ concurrent users with proper caching
- React Native + Expo can deliver native-feeling experience across iOS/Android
- AI costs (Gemini API) remain economically viable at scale
- Single developer with AI assistance (Claude Code) can ship MVP in 3 months
- Spec-kit development methodology provides adequate structure and velocity
- No major platform policy changes impact core functionality before launch

**Future Expansion** (not MVP but informs architecture):
- Users will want multiple profiles (pre/post surgery) - data model supports this
- Restaurant menu analysis will be highly valued - scan history structure accommodates
- Additional conditions (IBS, Crohn's) can use same personalization framework
- Community features may be added - user IDs and privacy model support this
- Apple Health/Google Fit integration is feasible - entity relationships compatible
- Web dashboard may be added - API-first architecture enables this
