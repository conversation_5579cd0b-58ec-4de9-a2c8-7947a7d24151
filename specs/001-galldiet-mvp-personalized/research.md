# Technical Research: GallD<PERSON> MVP

**Feature**: <PERSON>all<PERSON><PERSON> MVP - Personalized Gallstone Diet Management
**Date**: 2025-10-12
**Status**: Phase 0 Complete

## Overview

This document consolidates research findings for key technical decisions required for GallDiet MVP implementation. All unknowns from Technical Context have been resolved with specific recommendations.

---

## 1. Gemini API Integration Strategy

### Decision: Use Gemini 2.0 Flash Thinking (Primary) with Gemini 2.5 Flash (Fallback)

**Rationale**:
- Gemini 2.0 Flash Thinking excels at structured reasoning tasks (ingredient identification + personalized analysis)
- 2.0 Flash Thinking provides explicit reasoning traces helpful for debugging AI responses
- 2.5 Flash serves as fallback if 2.0 Flash Thinking unavailable or times out
- Both models support vision + structured JSON output mode

**Implementation Approach**:
```php
// GeminiVisionService.php
public function analyzeMeal(string $imagePath, array $userProfile): array
{
    try {
        // Primary: Gemini 2.0 Flash Thinking
        return $this->callGemini('gemini-2.0-flash-thinking-exp', $imagePath, $userProfile);
    } catch (GeminiException $e) {
        Log::warning('Gemini 2.0 Flash Thinking failed, falling back to 2.5 Flash', [
            'error' => $e->getMessage()
        ]);

        // Fallback: Gemini 2.5 Flash
        return $this->callGemini('gemini-2.5-flash', $imagePath, $userProfile);
    }
}
```

**Key Configuration**:
- JSON mode enabled via `response_mime_type: "application/json"`
- JSON schema validation using `response_schema` parameter
- Temperature: 0.3 (low for consistency, high enough for natural language reasoning)
- Max tokens: 1000 (sufficient for structured response with 2-3 sentence reasoning)
- Timeout: 10 seconds (per requirements)
- Retry logic: 1 retry with exponential backoff (2s delay) before fallback

**Cost Estimates** (as of Oct 2025):
- Gemini 2.0 Flash Thinking: ~$0.0015 per request (1 image + 1K token response)
- Expected usage: 100 scans/user/month × 1000 users = 100K scans/month
- Monthly AI cost: ~$150 (acceptable for MVP)
- Revenue breakeven: 15 Premium subscriptions ($9.99 each)

**Alternatives Considered**:
- **OpenAI GPT-4 Vision**: Higher cost ($0.01-0.03 per request), better accuracy but economically unviable for MVP
- **Claude 3.5 Sonnet (Vision)**: Excellent quality but no structured output mode, requires regex parsing (fragile)
- **Open-source (LLaVA, Qwen-VL)**: Free but requires self-hosting (GPU infrastructure complexity exceeds solo dev capacity)

**Risk Mitigation**:
- Cache generic ingredient data (e.g., "fried chicken" always contains "fried coating" + "chicken") to reduce API calls
- Implement circuit breaker pattern to prevent cascade failures
- Fallback to generic gallstone guidance if both models fail (stored responses in database)

---

## 2. Open Food Facts API Integration

### Decision: Use Official REST API with Local Caching Strategy

**Rationale**:
- Free tier sufficient for MVP (unlimited requests, no rate limiting)
- 2.3M+ products in database (strong coverage for US/EU markets)
- RESTful API with simple barcode lookup endpoint
- JSON responses with standardized ingredient data

**Implementation Approach**:
```php
// OpenFoodFactsService.php
public function lookupBarcode(string $barcode): ?array
{
    // Check cache first (Redis, 90-day TTL)
    $cached = Cache::remember("barcode:{$barcode}", now()->addDays(90), function () use ($barcode) {
        $response = Http::timeout(5)->get("https://world.openfoodfacts.org/api/v2/product/{$barcode}.json");

        if ($response->failed() || $response->json('status') === 0) {
            return null; // Product not found
        }

        return $this->normalizeProduct($response->json('product'));
    });

    return $cached;
}

private function normalizeProduct(array $raw): array
{
    return [
        'name' => $raw['product_name'] ?? 'Unknown Product',
        'ingredients' => $raw['ingredients_text'] ?? '',
        'ingredients_list' => $raw['ingredients'] ?? [],
        'allergens' => $raw['allergens_tags'] ?? [],
        'fat_100g' => $raw['nutriments']['fat_100g'] ?? null,
        'saturated_fat_100g' => $raw['nutriments']['saturated-fat_100g'] ?? null,
        'image_url' => $raw['image_url'] ?? null,
    ];
}
```

**Key Features**:
- Cache responses in Redis (90-day TTL matches scan history retention)
- Fallback to mobile app if product not found: "Product not in database. Try photo scan instead."
- Support for offline re-scans (AsyncStorage caches previously scanned products)
- Product submission feature (Phase 2): Users can contribute missing products to Open Food Facts

**Coverage Gaps**:
- Restaurant-specific items not in database (e.g., McDonald's menu items) - users will use photo scan
- Regional/local products may have lower coverage - acceptable for MVP, improves over time as community adds products
- Homemade meals not applicable - photo scan is primary method

**Rate Limiting**:
- No hard limits on free tier, but respectful usage requested (max 100 req/sec)
- MVP usage: ~1000 barcode scans/day peak = 0.01 req/sec average (well within limits)

**Alternatives Considered**:
- **USDA FoodData Central**: Government database, high quality but requires API key and has stricter rate limits (1000 req/hour)
- **Edamam Food Database**: Commercial API, $0.0004/request (viable but unnecessary for MVP when Open Food Facts is free)
- **Nutritionix**: Commercial API focused on US products, $99/month for 30K requests (not cost-effective for MVP)

---

## 3. Laravel Cashier (Stripe) Subscription Implementation

### Decision: Laravel Cashier for Stripe with Webhook-Based State Management

**Rationale**:
- Laravel Cashier abstracts Stripe complexity (subscriptions, webhooks, billing portal)
- Handles edge cases (failed payments, upgrades, cancellations) automatically
- Stripe is industry standard, trusted by users for health app payments
- Free for first $1M in revenue (0% platform fee), then 2.9% + $0.30 per transaction

**Implementation Approach**:
```php
// SubscriptionController.php
public function startTrial(Request $request)
{
    $user = $request->user();

    // Check if trial already used
    if ($user->subscription && $user->subscription->trial_used) {
        return response()->json(['error' => 'Trial already used'], 422);
    }

    // Create Stripe customer if doesn't exist
    if (!$user->stripe_id) {
        $user->createAsStripeCustomer();
    }

    // Start 7-day trial
    $user->newSubscription('default', env('STRIPE_PREMIUM_PRICE_ID'))
        ->trialDays(7)
        ->create($request->payment_method);

    return response()->json([
        'message' => '7-day Premium trial started',
        'trial_ends_at' => $user->subscription->trial_ends_at,
    ]);
}

// ProcessWebhookJob.php (handles Stripe webhooks async)
public function handle(array $payload)
{
    match ($payload['type']) {
        'customer.subscription.created' => $this->handleSubscriptionCreated($payload),
        'customer.subscription.updated' => $this->handleSubscriptionUpdated($payload),
        'customer.subscription.deleted' => $this->handleSubscriptionCancelled($payload),
        'invoice.payment_failed' => $this->handlePaymentFailed($payload),
        'invoice.payment_succeeded' => $this->handlePaymentSucceeded($payload),
        default => Log::info('Unhandled webhook type', ['type' => $payload['type']]),
    };
}
```

**Subscription State Management**:
- Store subscription status in `subscriptions` table (Cashier's default)
- Mirror key fields to user profile for fast access (denormalized):
  - `users.subscription_tier` enum('free', 'premium')
  - `users.trial_used` boolean
  - `users.subscription_expires_at` timestamp
- Webhook updates propagate to all user devices via SSE (real-time sync)

**Grace Period Implementation**:
```php
// Middleware: CheckSubscription.php
public function handle(Request $request, Closure $next)
{
    $user = $request->user();

    // Check if payment failed and grace period active
    if ($user->subscription->pastDue() && $user->subscription->grace_period_ends_at > now()) {
        // Allow access but show warning
        $response = $next($request);
        $response->header('X-Subscription-Warning', 'Payment failed. Update payment method within ' . $user->subscription->grace_period_ends_at->diffForHumans());
        return $response;
    }

    // Grace period expired, revert to free tier
    if ($user->subscription->pastDue()) {
        return response()->json(['error' => 'Premium expired. Please update payment method.'], 403);
    }

    return $next($request);
}
```

**Testing Strategy**:
- Use Stripe's test mode (test API keys) for all development
- Stripe CLI for webhook testing locally: `stripe listen --forward-to localhost/stripe/webhook`
- Test scenarios: trial start, trial→paid conversion, cancellation, failed payment, grace period, reactivation

**Alternatives Considered**:
- **Manual Stripe API Integration**: More control but requires implementing subscription state machine, webhook handling, billing portal (100+ hours of dev time)
- **Paddle**: Merchant of record model (handles EU VAT), but 5% + $0.50 per transaction (higher than Stripe)
- **RevenueCat**: Mobile-first subscription platform, excellent for app stores but unnecessary for web-based Stripe checkout

---

## 4. React Native + Expo Development Constraints

### Decision: Expo Managed Workflow with EAS Build for MVP

**Rationale**:
- Expo provides managed infrastructure (builds, OTA updates, push notifications) critical for solo dev
- EAS (Expo Application Services) handles iOS/Android builds without Xcode/Android Studio
- Expo SDK ~54.0 supports all required features (Camera, Barcode Scanner, AsyncStorage, Notifications)
- Can eject to bare workflow later if custom native modules needed (but unlikely for MVP)

**Key Constraints & Solutions**:

| Constraint | Impact | Solution |
|-----------|--------|----------|
| **AsyncStorage vs localStorage** | Web developers often default to localStorage, but React Native requires AsyncStorage | Strict code review rule: All persistence uses `@react-native-async-storage/async-storage`. No web APIs allowed. |
| **Camera permissions** | iOS requires NSCameraUsageDescription, Android needs camera permission | Configured in app.json: `"ios": { "infoPlist": { "NSCameraUsageDescription": "GallDiet needs camera access to scan meals" } }` |
| **Offline functionality** | React Native lacks IndexedDB/localStorage, requires different caching strategy | Use AsyncStorage + SQLite (expo-sqlite) for complex queries. See section 5 below. |
| **Performance (screen transitions)** | React Native bridge overhead prevents sub-100ms transitions | Accept 300ms target (industry standard), optimize with React.memo, useMemo, lazy loading |
| **Push notifications** | Requires Expo Push Notifications service + device tokens | Use expo-notifications with backend token registration endpoint |

**NativeWind (Tailwind for React Native)**:
- Provides Tailwind-like utility classes for styling
- Eliminates StyleSheet boilerplate
- Example: `<Text className="text-lg font-bold text-red-500">Warning</Text>`
- Supports dark mode: `className="bg-white dark:bg-gray-900"`
- Critical for rapid UI development (no separate stylesheet files)

**Expo Router (File-Based Routing)**:
- Replaces React Navigation boilerplate
- File structure determines routes: `app/(tabs)/scan.tsx` → `/scan` route
- Supports nested layouts, dynamic routes, deep linking
- Example: `app/scan-result/[id].tsx` → `/scan-result/123`

**EAS Build Configuration** (eas.json):
```json
{
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal",
      "ios": { "simulator": true }
    },
    "production": {
      "autoIncrement": true
    }
  }
}
```

**Alternatives Considered**:
- **Native Swift/Kotlin**: Best performance but 3-4x longer development time (separate iOS/Android codebases)
- **Flutter**: Excellent performance but Dart learning curve + less mature ecosystem than React Native
- **React Native (Bare Workflow)**: More control but requires Xcode/Android Studio setup (DevOps complexity exceeds solo dev capacity)

---

## 5. Offline Functionality & Multi-Device Sync

### Decision: Hybrid Sync Strategy (Real-Time SSE + Periodic Background)

**Rationale** (from clarifications.md Q5 & Q10):
- Critical data (profile changes) require real-time sync across devices
- Bulk data (scan history) can sync periodically to save battery/bandwidth
- Offline queue ensures no data loss when network unavailable

**Implementation Architecture**:

**1. Backend: Server-Sent Events (SSE) for Real-Time Sync**
```php
// SyncController.php
public function stream(Request $request)
{
    return response()->stream(function () use ($request) {
        $user = $request->user();

        while (true) {
            // Check for changes every 2 seconds
            $changes = $user->getPendingSyncChanges();

            if ($changes->isNotEmpty()) {
                echo "data: " . json_encode($changes) . "\n\n";
                ob_flush();
                flush();

                $user->markSyncChangesProcessed($changes);
            }

            sleep(2);

            // Check if client still connected
            if (connection_aborted()) {
                break;
            }
        }
    }, 200, [
        'Content-Type' => 'text/event-stream',
        'Cache-Control' => 'no-cache',
        'X-Accel-Buffering' => 'no', // Disable nginx buffering
    ]);
}
```

**2. Mobile: EventSource Client + Background Sync**
```typescript
// services/sync/SyncManager.ts
class SyncManager {
  private eventSource: EventSource | null = null;
  private syncQueue: SyncItem[] = [];

  // Real-time sync via SSE
  startRealTimeSync(authToken: string) {
    this.eventSource = new EventSource(`${API_URL}/api/sync/stream`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });

    this.eventSource.onmessage = (event) => {
      const changes = JSON.parse(event.data);
      this.applyChanges(changes); // Update AsyncStorage + trigger UI refresh
    };
  }

  // Offline queue
  async queueChange(item: SyncItem) {
    this.syncQueue.push(item);
    await AsyncStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));

    // Attempt immediate sync if online
    if (await NetInfo.fetch().then(state => state.isConnected)) {
      await this.processSyncQueue();
    }
  }

  // Periodic background sync (every 30 minutes)
  async periodicSync() {
    await this.processSyncQueue();
    await this.pullLatestScanHistory();
  }
}
```

**3. Conflict Resolution Strategy** (from clarifications.md Q10):
- **Last-write-wins**: For simple fields (e.g., profile updates), newest timestamp wins
- **Merge**: For collections (e.g., triggers), combine server + local changes, deduplicate by ID
- **User prompt**: If conflicting actions detected (e.g., user marked scan "ate" offline, "avoided" online), prompt user to resolve

**Offline Data Storage**:
```typescript
// AsyncStorage keys
const STORAGE_KEYS = {
  USER_PROFILE: 'user_profile',
  TRIGGERS: 'triggers',
  SCAN_HISTORY: 'scan_history', // Last 90 days, thumbnails only
  SYNC_QUEUE: 'sync_queue',
  CACHED_BARCODES: 'cached_barcodes',
  LAST_SYNC_TIMESTAMP: 'last_sync_timestamp',
};

// Storage size estimates
// - Profile: ~10KB
// - Triggers: ~5KB (50 triggers max)
// - Scan history (90 days): ~13.5MB (270 scans × 50KB thumbnail each)
// - Sync queue: ~100KB (pending changes)
// - Cached barcodes: ~500KB (100 products × 5KB each)
// Total: ~14MB (acceptable for AsyncStorage, iOS/Android limit is 2GB)
```

**Network State Detection**:
```typescript
import NetInfo from '@react-native-community/netinfo';

// Listen for connectivity changes
NetInfo.addEventListener(state => {
  if (state.isConnected && state.isInternetReachable) {
    syncManager.processSyncQueue(); // Upload pending changes
    syncManager.startRealTimeSync(); // Reconnect SSE
  } else {
    syncManager.stopRealTimeSync(); // Disconnect SSE
  }
});
```

**Alternatives Considered**:
- **WebSocket**: Bidirectional but more complex than SSE, requires persistent connection (battery drain)
- **Polling**: Simple but inefficient (constant API calls every N seconds)
- **Push Notifications**: Can trigger sync but not suitable for real-time data updates (user must open app)
- **Firebase Realtime Database**: Managed real-time sync but vendor lock-in + cost at scale

---

## 6. Laravel Queue Configuration (Redis Driver)

### Decision: Redis Queue Driver with Horizon for Monitoring

**Rationale**:
- Redis already required for caching (profile data), reusing for queues avoids additional infrastructure
- Redis provides reliable queue with persistence (AOF enabled)
- Laravel Horizon provides beautiful dashboard for queue monitoring/management
- Async job processing critical for AI analysis (2-4 second response time)

**Queue Architecture**:
```php
// config/queue.php
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 90, // Job timeout (AI calls may take up to 10s + overhead)
        'block_for' => null,
    ],
],

// Separate queues for priority
'queues' => [
    'high' => ['profile_sync', 'allergen_checks'], // Real-time, < 1s
    'default' => ['meal_analysis', 'barcode_lookup'], // Standard, 2-4s
    'low' => ['pattern_detection', 'weekly_summaries'], // Batch, minutes
],
```

**Job Implementation Example**:
```php
// Jobs/AnalyzeMealPhotoJob.php
class AnalyzeMealPhotoJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 15; // 10s AI call + 5s buffer
    public $tries = 2; // Retry once on failure

    public function __construct(
        public Scan $scan,
        public User $user
    ) {}

    public function handle(GeminiVisionService $gemini)
    {
        $profile = $this->user->profile;

        try {
            $analysis = $gemini->analyzeMeal($this->scan->image_path, $profile->toArray());

            $this->scan->update([
                'status' => 'completed',
                'meal_name' => $analysis['meal_name'],
                'detected_ingredients' => $analysis['detected_ingredients'],
                'safety_score' => $analysis['adjusted_score'],
                'confidence' => $analysis['confidence'],
                'reasoning' => $analysis['personalized_reasoning'],
                'analyzed_at' => now(),
            ]);

            // Notify mobile app via SSE
            event(new ScanAnalysisCompleted($this->scan));

        } catch (GeminiException $e) {
            Log::error('Meal analysis failed', [
                'scan_id' => $this->scan->id,
                'error' => $e->getMessage(),
            ]);

            $this->scan->update([
                'status' => 'failed',
                'error_message' => 'Analysis failed. Please try again.',
            ]);

            // Retry or mark as failed
            if ($this->attempts() < $this->tries) {
                $this->release(5); // Retry after 5 seconds
            } else {
                $this->fail($e);
            }
        }
    }
}
```

**Horizon Configuration** (config/horizon.php):
```php
'environments' => [
    'production' => [
        'supervisor-1' => [
            'connection' => 'redis',
            'queue' => ['high', 'default'],
            'balance' => 'auto',
            'processes' => 10,
            'tries' => 3,
        ],
        'supervisor-low' => [
            'connection' => 'redis',
            'queue' => ['low'],
            'balance' => 'simple',
            'processes' => 3,
            'tries' => 1,
        ],
    ],
],
```

**Running Workers**:
```bash
# Development (via Sail)
./vendor/bin/sail artisan horizon

# Production (via systemd)
php artisan horizon
```

**Monitoring**:
- Horizon dashboard: `/horizon` (password-protected in production)
- Metrics: jobs/minute, queue wait time, failed jobs
- Alerts: Configure Horizon to notify Slack/email on high failure rate

**Alternatives Considered**:
- **Database Queue**: Simpler but slower (disk I/O bottleneck at scale)
- **SQS (AWS)**: Managed service but vendor lock-in + cost ($0.40-0.50 per 1M requests)
- **RabbitMQ**: More features (message routing, exchanges) but overkill for MVP + operational complexity

---

## 7. Error Tracking & Analytics Setup

### Decision: Sentry (Errors) + PostHog (Analytics) for MVP

**Rationale**:
- **Sentry**: Industry standard for error tracking, free tier covers MVP (5K errors/month), excellent React Native + Laravel SDKs
- **PostHog**: Open-source product analytics, self-hostable or cloud (free tier: 1M events/month), privacy-friendly

**Sentry Integration**:

**Backend (Laravel)**:
```bash
composer require sentry/sentry-laravel
php artisan sentry:publish --dsn=https://<EMAIL>/xxx
```

```php
// config/sentry.php
'breadcrumbs' => [
    'sql_queries' => true, // Log SQL queries leading to error
    'sql_bindings' => false, // Don't log sensitive data
],

'send_default_pii' => false, // HIPAA-aligned: no personal health info
'before_send' => function (\Sentry\Event $event): ?\Sentry\Event {
    // Scrub sensitive fields
    $event = $this->scrubSensitiveData($event);
    return $event;
},
```

**Mobile (React Native)**:
```bash
npm install --save @sentry/react-native
npx @sentry/wizard -i reactNative -p ios android
```

```typescript
// app/_layout.tsx
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'https://<EMAIL>/xxx',
  tracesSampleRate: 0.1, // 10% performance monitoring
  beforeSend(event) {
    // Scrub user health data before sending
    return scrubHealthData(event);
  },
});
```

**PostHog Integration**:

**Backend (Laravel)**:
```bash
composer require posthog/posthog-php
```

```php
// Services/Analytics/PostHogService.php
class PostHogService
{
    public function trackScanCompleted(User $user, Scan $scan)
    {
        PostHog::capture([
            'distinctId' => $user->id,
            'event' => 'scan_completed',
            'properties' => [
                'scan_type' => $scan->type, // 'barcode' or 'photo'
                'safety_score' => $scan->safety_score,
                'trigger_count' => $scan->triggers_detected->count(),
                'subscription_tier' => $user->subscription_tier,
                // NO personal health data (PHI) sent to PostHog
            ],
        ]);
    }
}
```

**Mobile (React Native)**:
```bash
npm install posthog-react-native
```

```typescript
// services/analytics/posthog.ts
import PostHog from 'posthog-react-native';

export const posthog = new PostHog('phc_xxx', {
  host: 'https://app.posthog.com',
  captureNativeAppLifecycleEvents: true,
});

// Track user actions
posthog.capture('scan_initiated', {
  scan_type: 'photo',
  source_screen: 'home',
});
```

**Key Metrics to Track** (PostHog):
- Funnel: Onboarding completion rate (per screen dropout)
- Event: Scan frequency (scans/user/day)
- Event: First trigger identified (time from signup)
- Event: Premium conversion (trial → paid)
- Event: Scan result satisfaction (implicit via retry rate)

**Privacy Considerations**:
- **NO PHI sent to third parties**: User IDs anonymized (hashed), no trigger names, no attack details
- **Opt-out mechanism**: Settings screen allows users to disable analytics (preserves error tracking for stability)
- **Data retention**: PostHog data auto-deleted after 1 year (GDPR compliance)

**Alternatives Considered**:
- **Bugsnag**: Similar to Sentry but more expensive ($29/month for 5K errors vs. Sentry free tier)
- **Mixpanel**: Traditional analytics but $20/month minimum (PostHog free tier sufficient)
- **Google Analytics**: Privacy concerns for health app + complex GDPR compliance

---

## 8. Image Storage Strategy (MVP vs. Phase 2)

### Decision: Laravel Public Disk (MVP) → S3/Cloudflare R2 (Phase 2)

**Rationale**:
- MVP simplicity: Laravel's public disk stores images on server filesystem (no external service setup)
- Phase 2 migration: S3/R2 for scalability, CDN, and backup (straightforward Laravel Filesystem migration)

**MVP Implementation** (Laravel Public Disk):
```php
// config/filesystems.php
'disks' => [
    'public' => [
        'driver' => 'local',
        'root' => storage_path('app/public'),
        'url' => env('APP_URL').'/storage',
        'visibility' => 'public',
    ],
],

// ScanController.php
public function uploadMealPhoto(Request $request)
{
    $path = $request->file('photo')->store('scans', 'public');

    $scan = Scan::create([
        'user_id' => $request->user()->id,
        'type' => 'photo',
        'image_path' => $path,
        'status' => 'analyzing',
    ]);

    // Dispatch async analysis job
    AnalyzeMealPhotoJob::dispatch($scan, $request->user());

    return response()->json([
        'scan_id' => $scan->id,
        'status' => 'analyzing',
        'image_url' => Storage::url($path),
    ]);
}
```

**Storage Estimates (MVP)**:
- Average image size: 2MB (mobile photo, uncompressed)
- Images/user/month: 100 scans
- Storage/user/month: 200MB
- 1000 users: 200GB/month
- Server disk space: 1TB SSD sufficient for 5 months (acceptable for MVP validation period)

**Phase 2 Migration** (S3/Cloudflare R2):
```php
// config/filesystems.php
'disks' => [
    's3' => [
        'driver' => 's3',
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION'),
        'bucket' => env('AWS_BUCKET'),
        'url' => env('AWS_URL'),
    ],

    // OR Cloudflare R2 (S3-compatible, cheaper)
    'r2' => [
        'driver' => 's3',
        'key' => env('R2_ACCESS_KEY_ID'),
        'secret' => env('R2_SECRET_ACCESS_KEY'),
        'region' => 'auto',
        'bucket' => env('R2_BUCKET'),
        'endpoint' => env('R2_ENDPOINT'),
        'url' => env('R2_URL'),
    ],
],

// Migration command
php artisan storage:migrate public s3 --scans
```

**Cost Comparison (1000 users, 200GB/month storage, 50K requests)**:
- **Local Storage (MVP)**: $0 (included in server cost)
- **AWS S3**: ~$5/month storage + $0.40 requests = $5.40/month
- **Cloudflare R2**: ~$3/month storage + $0 requests (free egress) = $3/month ✅ Best value
- **Backblaze B2**: ~$1/month storage + $1/month bandwidth = $2/month (cheapest but slower)

**Phase 2 Migration Trigger**:
- Storage exceeds 500GB (approaching 1TB server limit)
- OR 18 months post-launch (planned Phase 2 infrastructure upgrade)
- OR CDN/edge caching needed for performance (international users)

**Alternatives Considered**:
- **Supabase Storage**: Generous free tier (1GB) but vendor lock-in, complex migration
- **Firebase Storage**: Good for mobile but expensive at scale ($0.026/GB/month)
- **imgix/Cloudinary**: Image optimization CDN, $9-49/month but overkill for MVP (images already compressed by mobile camera)

---

## Summary Table: All Research Decisions

| Area | Decision | Rationale | Cost (MVP) | Migration Path |
|------|----------|-----------|-----------|----------------|
| **AI Vision** | Gemini 2.0 Flash Thinking + 2.5 Flash fallback | Best cost/performance, structured JSON output | ~$150/month | GPT-4 Vision if quality issues |
| **Barcode Lookup** | Open Food Facts REST API + Redis caching | Free, 2.3M products, simple integration | $0 | Edamam if coverage insufficient |
| **Subscriptions** | Laravel Cashier (Stripe) | Mature, handles edge cases, trusted | 2.9% + $0.30/txn | Paddle if EU VAT simplification needed |
| **Mobile Framework** | React Native + Expo (Managed Workflow) | Solo dev velocity, managed infrastructure | $0 (free tier) | Bare workflow if native modules needed |
| **Offline/Sync** | SSE (real-time) + AsyncStorage + periodic sync | Battery efficient, no data loss, multi-device | $0 | WebSocket if bidirectional needed |
| **Queue System** | Redis + Laravel Horizon | Reuses caching infra, beautiful monitoring | $0 (included) | SQS if managed service preferred |
| **Error Tracking** | Sentry (errors) + PostHog (analytics) | Industry standard, privacy-friendly, free tiers | $0 | Self-hosted PostHog if privacy critical |
| **Image Storage** | Public Disk → Cloudflare R2 (Phase 2) | MVP simplicity, easy migration, low cost | $0 → $3/month | S3 if AWS ecosystem preferred |

---

## Open Questions (For Phase 1 Design)

These questions will be addressed during data-model.md and API contract design:

1. **Database Indexes**: Which queries are most frequent? (Profile lookups, scan history pagination, trigger searches)
2. **Caching Strategy**: Which data should be cached in Redis vs. fetched from PostgreSQL? (Profile: cache, History: paginate from DB)
3. **API Pagination**: Limit/offset or cursor-based? (Cursor-based for infinite scroll in mobile app)
4. **Test Data Factories**: What realistic data needed for pattern detection testing? (Minimum 15 scans + 3 attacks per test user)
5. **Multi-Device Sync Conflicts**: How to handle race conditions (e.g., two devices updating same trigger simultaneously)? (Last-write-wins with timestamp comparison)

---

## Q11: Pattern Detection Confidence Score Validation

**Question**: What is the rationale for the confidence score formula coefficients (25%, 20%, 30%, 25%)?

**Decision**: Initial Heuristic (Subject to Post-MVP A/B Testing)

**Rationale**:
The confidence score formula from clarifications.md Q1:
```
Confidence Score = (Correlation Count × 25%) + (Match Weight × 20%) + (Consistency × 30%) + (Recency × 25%)
```

Coefficients are initial heuristics based on:
- **Consistency (30%)**: Weighted highest because reproducibility is strongest signal
- **Correlation Count (25%)**: Second highest because multiple occurrences build evidence
- **Recency (25%)**: Recent patterns more relevant than old data
- **Match Weight (20%)**: Exact vs. category matches matter but are secondary to pattern strength

**Post-MVP Validation**:
- Track user confirmation rates (accept/reject) by confidence band
- A/B test coefficient adjustments with 100+ users
- Target: 75%+ user confirmation rate for suggestions ≥60% confidence (per contracts/README.md)

**Implementation**: Use formula as specified in T087, log confirmation outcomes for future optimization.

---

## Phase 0 Research: COMPLETE ✅

All technical unknowns resolved. Ready to proceed to Phase 1: Data Model & API Contract Design.
