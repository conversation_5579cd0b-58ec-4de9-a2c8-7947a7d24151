# GallDiet API Contracts

**Feature**: GallDiet MVP - Personalized Gallstone Diet Management
**Created**: 2025-10-12
**API Version**: 1.0.0

## Overview

This directory contains the API contract specifications for the GallDiet MVP. The API follows RESTful conventions and uses Laravel Sanctum for authentication.

## Files

- **`api-spec.yaml`** - Complete OpenAPI 3.0 specification with all endpoints, schemas, and examples

## API Documentation

### Base URLs

- **Development**: `http://localhost:8000/api`
- **Staging**: `https://staging.galldiet.app/api`
- **Production**: `https://api.galldiet.app/api`

### Authentication

All endpoints (except `/register` and `/login`) require a Sanctum bearer token:

```
Authorization: Bearer {token}
```

### API Resource Groups

The API is organized into 10 resource groups:

1. **Authentication** (`/register`, `/login`, `/logout`)
   - User registration and authentication
   - Returns Sanctum tokens for API access

2. **Profile** (`/profile`, `/profile/completeness`)
   - User profile and health information management
   - Profile completeness calculation with suggestions

3. **Triggers** (`/triggers`, `/triggers/{id}`)
   - User-specific trigger food management
   - CRUD operations for personal triggers

4. **Scanning** (`/scan/barcode`, `/scan/photo`, `/scan/{id}/action`)
   - Barcode scanning (unlimited, all users)
   - Photo scanning (3/day free, unlimited premium)
   - Asynchronous AI analysis with job polling

5. **History** (`/history`, `/history/{id}`)
   - Scan history with cursor-based pagination
   - Filtering by date, score, meal type, consumption status

6. **Attacks** (`/attacks`, `/attacks/{id}`)
   - Gallstone attack logging with symptom tracking
   - Attack correlation with recent scans (1-8 hour window)

7. **Patterns** (`/patterns/suggestions`, `/patterns/{id}/confirm`, `/patterns/{id}/reject`)
   - AI pattern detection for trigger identification
   - Confidence-scored suggestions based on attack correlations

8. **Recipes** (`/recipes/modify`, `/recipes`, `/recipes/{id}/save`)
   - AI-generated recipe modifications (Premium feature)
   - Personalized substitutions addressing user triggers

9. **Subscription** (`/subscription/trial`, `/subscription/subscribe`, `/subscription/cancel`, `/subscription/status`, `/subscription/webhook`)
   - Stripe integration via Laravel Cashier
   - Trial management (7-day free trial, one per user)
   - Subscription state management with grace periods

10. **Sync** (`/sync/status`, `/sync/push`, `/sync/pull`)
    - Multi-device synchronization
    - Conflict resolution with last-write-wins strategy

### Rate Limiting

**Tier-Based Limits**:
- **Free Tier**: 3 photo scans per day (resets midnight user local time), unlimited barcode scans
- **Premium Tier**: Unlimited scans, all features

**API Rate Limit**:
- 60 requests per minute per user (enforced by Laravel Sanctum middleware)

### Response Formats

All responses use Eloquent API Resources and follow this structure:

**Success Response**:
```json
{
  "data": { ... },
  "message": "Operation successful",
  "meta": { ... }
}
```

**Error Response**:
```json
{
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  }
}
```

### HTTP Status Codes

- **200 OK**: Successful GET, PUT, DELETE (with content)
- **201 Created**: Successful POST creating new resource
- **202 Accepted**: Request accepted, processing asynchronously (photo scans)
- **204 No Content**: Successful DELETE (no response body)
- **400 Bad Request**: Invalid request (e.g., trial already used)
- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: Valid auth but insufficient permissions (e.g., free tier limit)
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **425 Too Early**: Resource not ready yet (scan analysis in progress)
- **500 Internal Server Error**: Unexpected server error

## Key Features

### 1. Personalized Scoring Algorithm

Scans return personalized safety scores (0-100) based on:
- User's specific triggers (with severity: low=-15, moderate=-25, high=-40 points)
- User's allergens (automatic score=0 override)
- User's attack history (references previous correlations)
- General gallstone dietary guidelines (baseline)

**Example Response**:
```json
{
  "data": {
    "safety_score": 35,
    "base_score": 65,
    "trigger_warnings": [
      "Your known trigger 'fried foods' detected",
      "This caused 3 attacks for you"
    ],
    "personalized_reasoning": "This scores 35/100 FOR YOU because it contains fried chicken, which matches YOUR High severity trigger..."
  }
}
```

### 2. Asynchronous Photo Analysis

Photo scanning uses a job-based workflow:

1. **POST /scan/photo** → Returns `job_id`, status `analyzing`
2. **GET /scan/photo/{job_id}/status** → Poll for progress (analyzing, completed, failed)
3. **GET /scan/photo/{job_id}/result** → Retrieve completed analysis

**Expected Timeline**:
- Image upload: < 1 second
- AI analysis: 2-4 seconds (p95 latency)
- Total: < 5 seconds end-to-end

### 3. Pattern Detection Confidence Scoring

Pattern suggestions include detailed evidence:

```json
{
  "suspected_trigger_name": "chocolate",
  "confidence_score": 82.5,
  "correlation_count": 3,
  "consistency_rate": 100.0,
  "evidence": [
    {
      "scan_id": 123,
      "attack_id": 45,
      "hours_before": 4,
      "match_weight": 100
    }
  ]
}
```

**Confidence Formula**:
```
Confidence = (Correlation Count × 25%) + (Match Weight × 20%) + (Consistency × 30%) + (Recency × 25%)
```

**Minimum Threshold**: 60% (suggestions below this are not shown)

### 4. Multi-Device Sync

Sync uses a hybrid strategy:

**Real-Time Sync** (via Server-Sent Events):
- Profile changes (triggers, allergens, preferences)
- Subscription status changes
- Attack logging

**Periodic Sync** (every 30 minutes):
- Scan history
- Pattern detection results
- Weekly summaries

**Conflict Resolution**:
- Last-write-wins (based on timestamp)
- Duplicate detection for concurrent scans
- Client version tracking for consistency

### 5. Subscription State Machine

```
free → trial_active → premium_active → premium_lapsed
  ↓         ↓                ↓
trial_used  ↓         premium_grace
            ↓                ↓
       trial_used    premium_lapsed
```

**Grace Period**: 3 days of Premium access after payment failure

## Mobile App Integration

### Authentication Flow

1. User registers: `POST /register` → Receive token
2. Store token in secure storage (iOS Keychain, Android KeyStore)
3. Include token in all requests: `Authorization: Bearer {token}`
4. On 401 response: Clear token, redirect to login

### Offline Support

**Cached Locally** (AsyncStorage):
- User profile (triggers, allergens, preferences)
- Scan history thumbnails (last 90 days, ~50KB each)
- Barcode product data (previously scanned)

**Requires Internet**:
- Photo scanning (AI analysis)
- New barcode lookups
- Pattern detection
- Subscription operations

**Queue for Sync**:
- Scan consumption status changes
- Attack logging
- Profile updates

### Error Handling

**Network Errors**:
- Implement exponential backoff retry (1s, 2s, 4s, 8s, 16s, max 30s)
- Show user-friendly messages
- Queue failed operations for later retry

**Validation Errors (422)**:
- Parse `errors` object and display field-specific messages
- Highlight invalid form fields

**Rate Limits (403)**:
- Show upgrade prompt for scan limits
- Display scan count progress (e.g., "2/3 scans used today")

## Testing

### Example Requests

**Register New User**:
```bash
curl -X POST http://localhost:8000/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "password_confirmation": "SecurePass123!"
  }'
```

**Scan Barcode**:
```bash
curl -X POST http://localhost:8000/api/scan/barcode \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "barcode": "0123456789012",
    "meal_type": "lunch"
  }'
```

**Get Scan History**:
```bash
curl -X GET "http://localhost:8000/api/history?limit=30&safety_score_max=50" \
  -H "Authorization: Bearer {token}"
```

**Log Attack**:
```bash
curl -X POST http://localhost:8000/api/attacks \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "onset_at": "2025-10-12T14:30:00Z",
    "pain_intensity": 8,
    "duration_minutes": 180,
    "symptoms": {
      "nausea": true,
      "vomiting": true,
      "back_pain": true
    },
    "medical_care_type": "emergency_room"
  }'
```

### Postman Collection

A Postman collection will be generated from this OpenAPI spec:

```bash
# Generate Postman collection (requires openapi-to-postmanv2)
openapi2postmanv2 -s api-spec.yaml -o galldiet-api.postman.json
```

## Implementation Notes

### Backend (Laravel 12)

**Required Packages**:
- `laravel/sanctum` - API authentication
- `laravel/cashier` - Stripe subscriptions
- `spatie/laravel-data` - Data transfer objects
- `intervention/image` - Image processing (thumbnails)

**Key Services**:
- `ScanAnalysisService` - AI meal analysis orchestration
- `PersonalizationEngine` - Safety score calculation
- `PatternDetectionService` - Trigger correlation algorithm
- `SyncService` - Multi-device synchronization

**Queue Jobs**:
- `AnalyzeScanPhotoJob` - Async AI analysis (2-4 seconds)
- `DetectPatternsJob` - Run after attack logging (5-10 seconds)
- `GenerateWeeklySummaryJob` - Scheduled weekly

**Caching Strategy**:
- Redis cache for user profiles (TTL: 1 hour)
- Redis cache for barcode lookups (TTL: 90 days)
- Invalidate on profile/trigger updates

### Frontend (React Native + Expo)

**Required Packages**:
- `@react-native-async-storage/async-storage` - Local persistence
- `expo-camera` - Camera access
- `expo-barcode-scanner` - Barcode scanning
- `axios` - HTTP client
- `react-query` - API state management

**Key Screens**:
- `ScanScreen.tsx` - Camera interface
- `ScanResultScreen.tsx` - Analysis display
- `HistoryScreen.tsx` - Scan history list
- `PatternSuggestionsScreen.tsx` - Trigger suggestions

**AsyncStorage Keys**:
- `@galldiet:auth_token` - Sanctum token
- `@galldiet:user_profile` - Cached profile
- `@galldiet:scan_history` - Recent scans
- `@galldiet:sync_queue` - Pending changes

## Versioning

**API Version**: 1.0.0

**Breaking Change Policy**:
- Major version bump (2.0.0): Breaking changes to existing endpoints
- Minor version bump (1.1.0): New endpoints, backward compatible
- Patch version bump (1.0.1): Bug fixes only

**Deprecation Process**:
1. Mark endpoint as deprecated in OpenAPI spec
2. Return `X-Deprecated: true` header for 3 months
3. Remove in next major version

## Security

**Authentication**: Laravel Sanctum bearer tokens

**Data Encryption**:
- Profile data encrypted at rest (Laravel encryption)
- HTTPS/TLS for all API communication

**Rate Limiting**:
- Per-user: 60 requests/minute
- Per-IP: 100 requests/minute (unauthenticated endpoints)

**Input Validation**:
- All requests validated via Laravel Form Requests
- File uploads: Type validation, size limits (10MB photos)

**Stripe Webhooks**:
- Signature verification required
- Idempotent processing

## Support

For API-related questions or issues:
- **Email**: <EMAIL>
- **Documentation**: https://docs.galldiet.app
- **Issue Tracker**: Internal use only

---

**Last Updated**: 2025-10-12
**Next Review**: Before Phase 2 planning
