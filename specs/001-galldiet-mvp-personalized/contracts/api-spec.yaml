openapi: 3.0.3

info:
  title: GallDiet API
  version: 1.0.0
  description: |
    **GallDiet MVP API** - Personalized Gallstone Diet Management

    This is a RESTful API for the GallDiet mobile application, providing personalized dietary guidance for users with gallstones. The API uses Laravel Sanctum for authentication and returns all responses in JSON format using Eloquent API Resources.

    ## Key Features
    - Personalized food safety scoring based on user triggers and allergens
    - AI-powered meal photo analysis using Gemini 2.0 Flash Thinking
    - Pattern detection to identify trigger foods from attack correlations
    - Recipe modifications for safer meal alternatives
    - Multi-device sync with conflict resolution
    - Subscription management via Stripe

    ## Authentication
    All endpoints (except registration and login) require a valid Sanctum bearer token in the `Authorization` header.

    ## Rate Limiting
    - **Free Tier**: 3 photo scans per day (resets at midnight user local time), unlimited barcode scans
    - **Premium Tier**: Unlimited scans, all features unlocked
    - **API Rate Limit**: 60 requests per minute per user

    ## Error Handling
    All errors follow a consistent JSON structure with appropriate HTTP status codes.

  contact:
    name: GallDiet Support
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://galldiet.app/terms

servers:
  - url: http://localhost:8000/api
    description: Development server
  - url: https://staging.galldiet.app/api
    description: Staging server
  - url: https://api.galldiet.app/api
    description: Production server

security:
  - sanctum: []

tags:
  - name: Authentication
    description: User registration, login, and logout
  - name: Profile
    description: User profile and health information management
  - name: Triggers
    description: User-specific trigger food management
  - name: Scanning
    description: Food scanning (barcode and photo) and analysis
  - name: History
    description: Scan history and tracking
  - name: Attacks
    description: Gallstone attack logging and tracking
  - name: Patterns
    description: AI pattern detection and trigger suggestions
  - name: Recipes
    description: Recipe modification for safer meal alternatives
  - name: Subscription
    description: Subscription and payment management
  - name: Sync
    description: Multi-device data synchronization

paths:
  # ============================================================
  # AUTHENTICATION API
  # ============================================================
  /register:
    post:
      tags: [Authentication]
      summary: Register a new user
      description: Creates a new user account and returns authentication token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - password_confirmation
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  minLength: 8
                  example: SecurePass123!
                password_confirmation:
                  type: string
                  format: password
                  example: SecurePass123!
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    example: 1|abcdef123456...
                  message:
                    type: string
                    example: Account created successfully
        '422':
          $ref: '#/components/responses/ValidationError'

  /login:
    post:
      tags: [Authentication]
      summary: Authenticate user
      description: Login with email and password, returns Sanctum token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: SecurePass123!
                device_name:
                  type: string
                  description: Optional device name for token identification
                  example: iPhone 14 Pro
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    example: 2|xyz789token...
                  message:
                    type: string
                    example: Login successful
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'

  /logout:
    post:
      tags: [Authentication]
      summary: Logout user
      description: Revokes current Sanctum token
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Logged out successfully

  # ============================================================
  # PROFILE API
  # ============================================================
  /profile:
    get:
      tags: [Profile]
      summary: Get user profile
      description: Returns complete user profile including health information, triggers, and allergens
      responses:
        '200':
          description: Profile retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/UserProfile'
        '401':
          $ref: '#/components/responses/Unauthorized'

    put:
      tags: [Profile]
      summary: Update user profile
      description: Update user health information and preferences
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/UserProfile'
                  message:
                    type: string
                    example: Profile updated successfully
        '422':
          $ref: '#/components/responses/ValidationError'

  /profile/completeness:
    get:
      tags: [Profile]
      summary: Get profile completeness
      description: Returns profile completeness percentage and suggestions for improvement
      responses:
        '200':
          description: Completeness data retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  completeness_percentage:
                    type: integer
                    example: 63
                  missing_fields:
                    type: array
                    items:
                      type: object
                      properties:
                        field:
                          type: string
                          example: emergency_contact_phone
                        points:
                          type: integer
                          example: 3
                        suggestion:
                          type: string
                          example: Add emergency contact for safety
                  breakdown:
                    type: object
                    properties:
                      condition_info:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 23
                          max:
                            type: integer
                            example: 25
                      known_triggers:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 10
                          max:
                            type: integer
                            example: 25
                      dietary_preferences:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 10
                          max:
                            type: integer
                            example: 15
                      allergens:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 10
                          max:
                            type: integer
                            example: 20
                      health_goals:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 10
                          max:
                            type: integer
                            example: 10
                      personal_details:
                        type: object
                        properties:
                          current:
                            type: integer
                            example: 0
                          max:
                            type: integer
                            example: 5

  # ============================================================
  # TRIGGERS API
  # ============================================================
  /triggers:
    get:
      tags: [Triggers]
      summary: Get user triggers
      description: Returns all active triggers with correlation statistics
      responses:
        '200':
          description: Triggers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Trigger'

    post:
      tags: [Triggers]
      summary: Add new trigger
      description: Add a new trigger food to user profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - trigger_name
                - severity
              properties:
                trigger_name:
                  type: string
                  example: fried foods
                severity:
                  type: string
                  enum: [low, moderate, high]
                  example: high
                identification_source:
                  type: string
                  enum: [user_input, pattern_detected, attack_correlated]
                  default: user_input
                notes:
                  type: string
                  example: Causes severe pain within 3-4 hours
      responses:
        '201':
          description: Trigger added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Trigger'
                  message:
                    type: string
                    example: Trigger added successfully
        '422':
          $ref: '#/components/responses/ValidationError'

  /triggers/{id}:
    put:
      tags: [Triggers]
      summary: Update trigger
      description: Update trigger severity or notes
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                severity:
                  type: string
                  enum: [low, moderate, high]
                notes:
                  type: string
      responses:
        '200':
          description: Trigger updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Trigger'
                  message:
                    type: string
                    example: Trigger updated successfully
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [Triggers]
      summary: Delete trigger
      description: Remove trigger from user profile
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Trigger deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'

  # ============================================================
  # SCANNING API
  # ============================================================
  /scan/barcode:
    post:
      tags: [Scanning]
      summary: Scan product barcode
      description: |
        Scans product barcode, looks up in Open Food Facts, and returns personalized safety score.
        This endpoint is unlimited for all users (free and premium).
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - barcode
              properties:
                barcode:
                  type: string
                  example: '0123456789012'
                meal_type:
                  type: string
                  enum: [breakfast, lunch, dinner, snack, other]
                location:
                  type: string
                  example: Trader Joe's
      responses:
        '200':
          description: Scan completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/ScanResult'
        '404':
          description: Product not found in Open Food Facts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: Product not found in database
                suggestion: Try scanning the meal with your camera instead
        '422':
          $ref: '#/components/responses/ValidationError'

  /scan/photo:
    post:
      tags: [Scanning]
      summary: Scan meal photo
      description: |
        Uploads meal photo for AI analysis. Free tier users are limited to 3 scans per day.
        Analysis happens asynchronously - use the job_id to poll for results.
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - photo
              properties:
                photo:
                  type: string
                  format: binary
                  description: JPEG, PNG, or HEIC image (max 10MB)
                meal_type:
                  type: string
                  enum: [breakfast, lunch, dinner, snack, other]
                location:
                  type: string
                  example: Restaurant Name
      responses:
        '202':
          description: Photo uploaded, analysis in progress
          content:
            application/json:
              schema:
                type: object
                properties:
                  job_id:
                    type: string
                    format: uuid
                    example: 550e8400-e29b-41d4-a716-************
                  status:
                    type: string
                    example: analyzing
                  message:
                    type: string
                    example: Photo uploaded successfully. Analysis in progress.
                  poll_url:
                    type: string
                    example: /api/scan/photo/550e8400-e29b-41d4-a716-************/status
        '403':
          description: Daily scan limit reached (free tier)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Daily scan limit reached (3/day). Upgrade to Premium for unlimited scans.
                  scans_used_today:
                    type: integer
                    example: 3
                  scans_limit:
                    type: integer
                    example: 3
                  upgrade_url:
                    type: string
                    example: /api/subscription/trial
        '422':
          $ref: '#/components/responses/ValidationError'

  /scan/photo/{job_id}/status:
    get:
      tags: [Scanning]
      summary: Get scan analysis status
      description: Poll for scan analysis status and progress
      parameters:
        - name: job_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  job_id:
                    type: string
                    format: uuid
                  status:
                    type: string
                    enum: [analyzing, completed, failed]
                    example: analyzing
                  progress:
                    type: integer
                    minimum: 0
                    maximum: 100
                    example: 75
                  message:
                    type: string
                    example: Checking YOUR profile...
                  result_url:
                    type: string
                    nullable: true
                    example: /api/scan/photo/550e8400-e29b-41d4-a716-************/result
        '404':
          $ref: '#/components/responses/NotFound'

  /scan/photo/{job_id}/result:
    get:
      tags: [Scanning]
      summary: Get scan analysis result
      description: Get completed scan analysis with personalized safety score
      parameters:
        - name: job_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Analysis completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/ScanResult'
        '404':
          $ref: '#/components/responses/NotFound'
        '425':
          description: Analysis not yet complete
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Analysis still in progress
                  status_url:
                    type: string
                    example: /api/scan/photo/550e8400-e29b-41d4-a716-************/status

  /scan/{id}/action:
    post:
      tags: [Scanning]
      summary: Mark scan action
      description: Mark whether user ate, avoided, or modified the scanned food
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - consumption_status
              properties:
                consumption_status:
                  type: string
                  enum: [ate, avoided, modified]
                  example: ate
                consumed_at:
                  type: string
                  format: date-time
                  example: '2025-10-12T14:30:00Z'
                symptom_severity:
                  type: string
                  enum: [none, mild, moderate, attack]
                  nullable: true
                symptom_notes:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Action recorded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/ScanResult'
                  message:
                    type: string
                    example: Consumption status updated
        '404':
          $ref: '#/components/responses/NotFound'

  # ============================================================
  # HISTORY API
  # ============================================================
  /history:
    get:
      tags: [History]
      summary: Get scan history
      description: |
        Returns paginated scan history with cursor-based pagination.
        Free tier: 90-day retention. Premium: Unlimited retention.
      parameters:
        - name: cursor
          in: query
          schema:
            type: string
          description: Cursor for pagination
        - name: limit
          in: query
          schema:
            type: integer
            default: 30
            maximum: 100
        - name: meal_type
          in: query
          schema:
            type: string
            enum: [breakfast, lunch, dinner, snack, other]
        - name: safety_score_min
          in: query
          schema:
            type: integer
            minimum: 0
            maximum: 100
        - name: safety_score_max
          in: query
          schema:
            type: integer
            minimum: 0
            maximum: 100
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
        - name: consumption_status
          in: query
          schema:
            type: string
            enum: [ate, avoided, modified, unknown]
      responses:
        '200':
          description: History retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ScanResult'
                  meta:
                    type: object
                    properties:
                      next_cursor:
                        type: string
                        nullable: true
                      previous_cursor:
                        type: string
                        nullable: true
                      per_page:
                        type: integer
                      path:
                        type: string

  /history/{id}:
    get:
      tags: [History]
      summary: Get single scan
      description: Retrieve detailed information for a specific scan
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Scan retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/ScanResult'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags: [History]
      summary: Delete scan
      description: Soft delete scan from history (affects pattern detection accuracy)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Scan deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'

  # ============================================================
  # ATTACK API
  # ============================================================
  /attacks:
    post:
      tags: [Attacks]
      summary: Log attack episode
      description: Log a gallstone attack with symptoms and timing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AttackRequest'
      responses:
        '201':
          description: Attack logged successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Attack'
                  message:
                    type: string
                    example: Attack logged successfully
                  pattern_detection_triggered:
                    type: boolean
                    description: Whether pattern detection analysis was triggered
        '422':
          $ref: '#/components/responses/ValidationError'

    get:
      tags: [Attacks]
      summary: Get attack history
      description: Returns all logged attacks with correlation statistics
      parameters:
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: Attacks retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Attack'
                  stats:
                    type: object
                    properties:
                      total_attacks:
                        type: integer
                      attack_free_days:
                        type: integer
                      average_intensity:
                        type: number
                        format: float

  /attacks/{id}:
    get:
      tags: [Attacks]
      summary: Get attack details
      description: Get detailed information for specific attack including correlated scans
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Attack retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Attack'
                  correlated_scans:
                    type: array
                    description: Scans from 1-8 hours before attack
                    items:
                      $ref: '#/components/schemas/ScanResult'
        '404':
          $ref: '#/components/responses/NotFound'

  # ============================================================
  # PATTERN DETECTION API
  # ============================================================
  /patterns/suggestions:
    get:
      tags: [Patterns]
      summary: Get trigger suggestions
      description: |
        Returns AI-detected pattern suggestions based on attack correlations.
        Only shows suggestions with confidence >= 60%.
      responses:
        '200':
          description: Suggestions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PatternSuggestion'
                  message:
                    type: string
                    nullable: true
                    example: We've detected 2 potential triggers based on your attack history

  /patterns/{id}/confirm:
    post:
      tags: [Patterns]
      summary: Confirm suggested trigger
      description: Confirm AI-suggested trigger and add to user's trigger list
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Pattern detection result ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                severity:
                  type: string
                  enum: [low, moderate, high]
                  description: User-specified severity (optional, defaults to moderate)
                notes:
                  type: string
      responses:
        '200':
          description: Trigger confirmed and added
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Trigger'
                  message:
                    type: string
                    example: Trigger confirmed and added to your profile
        '404':
          $ref: '#/components/responses/NotFound'

  /patterns/{id}/reject:
    post:
      tags: [Patterns]
      summary: Reject suggested trigger
      description: Reject AI-suggested trigger (stops suggesting after 3 rejections)
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  example: I eat this regularly without problems
      responses:
        '200':
          description: Suggestion rejected
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Suggestion rejected
        '404':
          $ref: '#/components/responses/NotFound'

  # ============================================================
  # RECIPE API
  # ============================================================
  /recipes/modify:
    post:
      tags: [Recipes]
      summary: Request recipe modification
      description: |
        Generate safer version of risky meal (Premium feature).
        Free users see teaser with blurred preview.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - scan_id
              properties:
                scan_id:
                  type: integer
                  description: ID of scan to modify
                dietary_preferences:
                  type: array
                  items:
                    type: string
                  description: Additional preferences (optional)
      responses:
        '200':
          description: Modified recipe generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/RecipeModification'
        '403':
          description: Premium feature (free tier)
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Recipe modifications are a Premium feature
                  teaser:
                    type: object
                    properties:
                      sample_substitution:
                        type: string
                        example: "Heavy cream → Greek yogurt"
                      estimated_safety_score:
                        type: integer
                        example: 82
                  upgrade_url:
                    type: string
                    example: /api/subscription/trial
        '404':
          $ref: '#/components/responses/NotFound'

  /recipes:
    get:
      tags: [Recipes]
      summary: Get saved recipes
      description: Returns user's saved modified recipes (Premium feature)
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Recipes retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/RecipeModification'

  /recipes/{id}/save:
    post:
      tags: [Recipes]
      summary: Save recipe to collection
      description: Save modified recipe to user's personal collection
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                rating:
                  type: integer
                  minimum: 1
                  maximum: 5
                notes:
                  type: string
      responses:
        '200':
          description: Recipe saved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Recipe saved to your collection
        '404':
          $ref: '#/components/responses/NotFound'

  # ============================================================
  # SUBSCRIPTION API
  # ============================================================
  /subscription/trial:
    post:
      tags: [Subscription]
      summary: Start Premium trial
      description: |
        Start 7-day free trial (one per user lifetime).
        Requires payment method but no charge until day 8.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - payment_method_id
              properties:
                payment_method_id:
                  type: string
                  description: Stripe payment method ID
                  example: pm_1234567890
      responses:
        '200':
          description: Trial started successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
                  message:
                    type: string
                    example: 7-day free trial started. You won't be charged until Oct 19.
        '400':
          description: Trial already used
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: You've already used your free trial
                  direct_subscribe_url:
                    type: string
                    example: /api/subscription/subscribe
        '422':
          $ref: '#/components/responses/ValidationError'

  /subscription/subscribe:
    post:
      tags: [Subscription]
      summary: Subscribe to Premium
      description: Start paid Premium subscription (no trial)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - payment_method_id
              properties:
                payment_method_id:
                  type: string
                  description: Stripe payment method ID
      responses:
        '200':
          description: Subscription created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'
                  message:
                    type: string
                    example: Premium subscription activated. Charged $9.99/month.
        '422':
          $ref: '#/components/responses/ValidationError'

  /subscription/cancel:
    delete:
      tags: [Subscription]
      summary: Cancel subscription
      description: Cancel Premium subscription (access until end of billing period)
      responses:
        '200':
          description: Subscription cancelled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Subscription cancelled. Premium access until Oct 28.
                  access_until:
                    type: string
                    format: date-time
        '400':
          description: No active subscription
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subscription/status:
    get:
      tags: [Subscription]
      summary: Get subscription status
      description: Returns current subscription tier and details
      responses:
        '200':
          description: Status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Subscription'

  /subscription/webhook:
    post:
      tags: [Subscription]
      summary: Stripe webhook handler
      description: Handles Stripe webhook events (payment success, failure, etc.)
      security: []
      parameters:
        - name: Stripe-Signature
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Webhook processed successfully
        '400':
          description: Invalid signature or payload

  # ============================================================
  # SYNC API
  # ============================================================
  /sync/status:
    get:
      tags: [Sync]
      summary: Get sync status
      description: Returns last sync timestamp and pending changes count
      responses:
        '200':
          description: Sync status retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  last_synced_at:
                    type: string
                    format: date-time
                    nullable: true
                  pending_changes:
                    type: integer
                    description: Number of changes pending sync
                  server_version:
                    type: integer
                    description: Server data version for conflict detection

  /sync/push:
    post:
      tags: [Sync]
      summary: Push local changes
      description: Upload local changes made while offline
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                changes:
                  type: array
                  items:
                    type: object
                    properties:
                      entity_type:
                        type: string
                        enum: [scan, trigger, attack, profile]
                      entity_id:
                        type: integer
                        nullable: true
                      action:
                        type: string
                        enum: [create, update, delete]
                      data:
                        type: object
                      timestamp:
                        type: string
                        format: date-time
                client_version:
                  type: integer
      responses:
        '200':
          description: Changes synced successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  synced_count:
                    type: integer
                  conflicts:
                    type: array
                    items:
                      type: object
                      properties:
                        entity_type:
                          type: string
                        entity_id:
                          type: integer
                        conflict_type:
                          type: string
                          example: concurrent_modification
                        resolution:
                          type: string
                          example: server_wins
                  server_version:
                    type: integer

  /sync/pull:
    get:
      tags: [Sync]
      summary: Pull server changes
      description: Download changes from server since last sync
      parameters:
        - name: since
          in: query
          required: false
          schema:
            type: string
            format: date-time
          description: Last sync timestamp (omit for full sync)
        - name: version
          in: query
          schema:
            type: integer
          description: Client data version
      responses:
        '200':
          description: Changes retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  changes:
                    type: array
                    items:
                      type: object
                      properties:
                        entity_type:
                          type: string
                        entity_id:
                          type: integer
                        action:
                          type: string
                        data:
                          type: object
                        timestamp:
                          type: string
                          format: date-time
                  server_version:
                    type: integer
                  full_sync_required:
                    type: boolean
                    description: True if client version too old

# ============================================================
# COMPONENTS
# ============================================================
components:
  securitySchemes:
    sanctum:
      type: http
      scheme: bearer
      bearerFormat: Sanctum token
      description: Laravel Sanctum authentication token

  schemas:
    # -------------------- User & Profile --------------------
    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
        email:
          type: string
          format: email
          example: <EMAIL>
        subscription_tier:
          type: string
          enum: [free, premium]
          example: free
        trial_used:
          type: boolean
        profile:
          $ref: '#/components/schemas/UserProfile'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    UserProfile:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        condition_type:
          type: string
          enum: [gallstones, post_surgery]
          example: gallstones
        severity_level:
          type: string
          enum: [mild, moderate, severe]
          example: moderate
        diagnosis_date:
          type: string
          format: date
          nullable: true
        previous_attack_count:
          type: integer
          example: 2
        health_goals:
          type: array
          items:
            type: string
          example: [avoid_attacks, identify_triggers]
        emergency_contact_name:
          type: string
          nullable: true
        emergency_contact_phone:
          type: string
          nullable: true
        doctor_name:
          type: string
          nullable: true
        doctor_phone:
          type: string
          nullable: true
        completeness_percentage:
          type: integer
          example: 63
        triggers:
          type: array
          items:
            $ref: '#/components/schemas/Trigger'
        allergens:
          type: array
          items:
            $ref: '#/components/schemas/Allergen'
        dietary_preferences:
          type: array
          items:
            type: string
          example: [vegetarian, gluten_free]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    UpdateProfileRequest:
      type: object
      properties:
        condition_type:
          type: string
          enum: [gallstones, post_surgery]
        severity_level:
          type: string
          enum: [mild, moderate, severe]
        diagnosis_date:
          type: string
          format: date
        previous_attack_count:
          type: integer
          minimum: 0
        health_goals:
          type: array
          items:
            type: string
            enum: [avoid_attacks, identify_triggers, avoid_surgery, maintain_weight, reduce_medication]
        emergency_contact_name:
          type: string
        emergency_contact_phone:
          type: string
        doctor_name:
          type: string
        doctor_phone:
          type: string
        dietary_preferences:
          type: array
          items:
            type: string

    # -------------------- Triggers & Allergens --------------------
    Trigger:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        trigger_name:
          type: string
          example: fried foods
        severity:
          type: string
          enum: [low, moderate, high]
          example: high
        identification_source:
          type: string
          enum: [user_input, pattern_detected, attack_correlated]
          example: user_input
        confidence_score:
          type: number
          format: float
          example: 100.0
        attack_correlation_count:
          type: integer
          example: 4
        total_exposures:
          type: integer
          example: 8
        last_attack_date:
          type: string
          format: date-time
          nullable: true
        notes:
          type: string
          nullable: true
        status:
          type: string
          enum: [active, rejected, pending_confirmation]
          example: active
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    Allergen:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        allergen_name:
          type: string
          example: peanuts
        allergen_type:
          type: string
          enum: [life_threatening, intolerance]
          example: life_threatening
        reaction_description:
          type: string
          nullable: true
        last_reaction_date:
          type: string
          format: date
          nullable: true
        medical_confirmation:
          type: boolean
        notes:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time

    # -------------------- Scans --------------------
    ScanResult:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        scan_type:
          type: string
          enum: [barcode, photo]
          example: photo
        status:
          type: string
          enum: [analyzing, completed, failed]
          example: completed
        food_name:
          type: string
          example: Fried Chicken with Fries
        detected_ingredients:
          type: array
          items:
            type: string
          example: [fried chicken, french fries, coleslaw]
        image_url:
          type: string
          format: uri
          nullable: true
        thumbnail_url:
          type: string
          format: uri
          nullable: true
        barcode:
          type: string
          nullable: true
        safety_score:
          type: integer
          minimum: 0
          maximum: 100
          example: 35
        base_score:
          type: integer
          description: Score before personalization
          example: 65
        confidence_score:
          type: number
          format: float
          example: 87.5
          description: AI confidence in ingredient identification
        problem_ingredients:
          type: array
          items:
            type: object
            properties:
              ingredient:
                type: string
              reason:
                type: string
              severity:
                type: string
                enum: [low, moderate, high]
          example:
            - ingredient: fried coating
              reason: matches YOUR trigger: fried foods
              severity: high
        safe_ingredients:
          type: array
          items:
            type: string
          example: [lettuce, tomato]
        trigger_warnings:
          type: array
          items:
            type: string
          example:
            - Your known trigger 'fried foods' detected
            - This caused 3 attacks for you
        allergen_alert:
          type: string
          nullable: true
          example: "CRITICAL: Contains peanuts (YOUR allergen)"
        personalized_reasoning:
          type: string
          example: This scores 35/100 FOR YOU because it contains fried chicken, which matches YOUR High severity trigger 'fried foods'. We strongly recommend avoiding this meal.
        consumption_status:
          type: string
          enum: [unknown, ate, avoided, modified]
          example: unknown
        consumed_at:
          type: string
          format: date-time
          nullable: true
        symptoms_logged:
          type: boolean
        symptom_severity:
          type: string
          enum: [none, mild, moderate, attack]
          nullable: true
        symptom_notes:
          type: string
          nullable: true
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner, snack, other]
          nullable: true
        location:
          type: string
          nullable: true
        analyzed_at:
          type: string
          format: date-time
          nullable: true
        ai_model_used:
          type: string
          example: gemini-2.0-flash-thinking-exp
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # -------------------- Attacks --------------------
    AttackRequest:
      type: object
      required:
        - onset_at
        - pain_intensity
        - medical_care_type
      properties:
        onset_at:
          type: string
          format: date-time
          example: '2025-10-12T14:30:00Z'
        duration_minutes:
          type: integer
          minimum: 1
          example: 180
        pain_intensity:
          type: integer
          minimum: 1
          maximum: 10
          example: 8
        pain_location:
          type: object
          properties:
            x:
              type: number
            y:
              type: number
            region:
              type: string
              example: upper_right_abdomen
        symptoms:
          type: object
          properties:
            nausea:
              type: boolean
            vomiting:
              type: boolean
            fever:
              type: boolean
            jaundice:
              type: boolean
            back_pain:
              type: boolean
        medical_care_type:
          type: string
          enum: [none, home_treatment, doctor_call, urgent_care, emergency_room, hospitalized]
          example: emergency_room
        diagnosis_received:
          type: string
          nullable: true
        treatment_received:
          type: string
          nullable: true
        notes:
          type: string
          nullable: true

    Attack:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        onset_at:
          type: string
          format: date-time
        duration_minutes:
          type: integer
        pain_intensity:
          type: integer
        pain_location:
          type: object
        symptoms:
          type: object
        medical_care_type:
          type: string
        diagnosis_received:
          type: string
          nullable: true
        treatment_received:
          type: string
          nullable: true
        correlated_scans_analyzed:
          type: boolean
        suspected_trigger_id:
          type: integer
          nullable: true
        correlation_confidence:
          type: number
          format: float
          nullable: true
        notes:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # -------------------- Pattern Detection --------------------
    PatternSuggestion:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        suspected_trigger_name:
          type: string
          example: chocolate
        confidence_score:
          type: number
          format: float
          example: 82.5
        correlation_count:
          type: integer
          example: 3
          description: Number of times food appeared before attacks
        total_exposures:
          type: integer
          example: 3
          description: Total times user ate this food
        consistency_rate:
          type: number
          format: float
          example: 100.0
          description: Percentage of times eating led to attack
        evidence:
          type: array
          items:
            type: object
            properties:
              scan_id:
                type: integer
              attack_id:
                type: integer
              hours_before:
                type: integer
              match_weight:
                type: integer
        date_range_start:
          type: string
          format: date
        date_range_end:
          type: string
          format: date
        most_recent_correlation_date:
          type: string
          format: date
        status:
          type: string
          enum: [pending, confirmed, rejected]
        created_at:
          type: string
          format: date-time

    # -------------------- Recipes --------------------
    RecipeModification:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        original_scan_id:
          type: integer
          nullable: true
        original_meal_name:
          type: string
          example: Pasta Carbonara
        original_safety_score:
          type: integer
          example: 25
        original_ingredients:
          type: array
          items:
            type: string
        modified_meal_name:
          type: string
          example: Lighter Pasta Carbonara
        modified_safety_score:
          type: integer
          example: 82
        substitutions:
          type: array
          items:
            type: object
            properties:
              original:
                type: string
                example: heavy cream
              replacement:
                type: string
                example: greek yogurt
              reason:
                type: string
                example: Avoids your full-fat dairy trigger
        original_nutrition:
          type: object
          properties:
            calories:
              type: integer
            fat_g:
              type: integer
            saturated_fat_g:
              type: integer
        modified_nutrition:
          type: object
          properties:
            calories:
              type: integer
            fat_g:
              type: integer
            saturated_fat_g:
              type: integer
        cooking_instructions:
          type: array
          items:
            type: string
          maxItems: 5
        prep_time_minutes:
          type: integer
        cook_time_minutes:
          type: integer
        user_rating:
          type: integer
          minimum: 1
          maximum: 5
          nullable: true
        user_made_it:
          type: boolean
        user_notes:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time

    # -------------------- Subscription --------------------
    Subscription:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        tier:
          type: string
          enum: [free, premium]
          example: premium
        stripe_id:
          type: string
          nullable: true
        stripe_status:
          type: string
          enum: [active, canceled, incomplete, past_due, trialing]
          nullable: true
        trial_ends_at:
          type: string
          format: date-time
          nullable: true
        ends_at:
          type: string
          format: date-time
          nullable: true
          description: Cancellation date if cancelled
        grace_period_ends_at:
          type: string
          format: date-time
          nullable: true
        billing_amount:
          type: number
          format: float
          example: 9.99
        billing_frequency:
          type: string
          example: monthly
        next_billing_date:
          type: string
          format: date
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # -------------------- Errors --------------------
    Error:
      type: object
      properties:
        message:
          type: string
          example: The given data was invalid
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

  responses:
    Unauthorized:
      description: Unauthorized - Invalid or missing authentication token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: Unauthenticated

    ValidationError:
      description: Validation error - Invalid input data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: The given data was invalid
            errors:
              email:
                - The email field is required
              password:
                - The password must be at least 8 characters

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: Resource not found

    ServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: An unexpected error occurred
