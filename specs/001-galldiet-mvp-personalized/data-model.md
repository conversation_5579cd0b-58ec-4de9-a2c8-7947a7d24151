# Data Model: GallD<PERSON> MVP

**Feature**: Gall<PERSON><PERSON> MVP - Personalized Gallstone Diet Management
**Date**: 2025-10-12
**Status**: Phase 1 Design
**Database**: PostgreSQL 14+
**ORM**: Lara<PERSON> 12 Eloquent

## Overview

The GallDiet data model is designed with the following principles:

- **Personalization-First**: All scan results and analyses are contextualized to the user's unique profile
- **Normalized Schema**: Separate tables for triggers and allergens (clarifications.md Q9) to maintain clear distinction
- **Pattern Detection Ready**: Schema supports correlation analysis between scans and attacks (1-8 hour time windows)
- **Multi-Device Sync**: Timestamp-based conflict resolution, real-time sync via SSE
- **Privacy & Security**: Encrypted at rest (<PERSON><PERSON> encryption), no PHI in third-party analytics
- **Scalability**: Indexed for common query patterns, Redis caching for hot data

## Complete Database Schema

### Core Tables

#### `users`
Primary authentication and subscription metadata.

```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    password VARCHAR(255) NOT NULL,
    remember_token VARCHAR(100),

    -- Subscription (denormalized for fast access)
    subscription_tier VARCHAR(20) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium')),
    trial_used BOOLEAN DEFAULT false,
    subscription_expires_at TIMESTAMP WITH TIME ZONE,

    -- Stripe (Laravel Cashier)
    stripe_id VARCHAR(255),
    pm_type VARCHAR(255),
    pm_last_four VARCHAR(4),
    trial_ends_at TIMESTAMP WITH TIME ZONE,

    -- Sync metadata
    last_synced_at TIMESTAMP WITH TIME ZONE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_stripe_id ON users(stripe_id);
CREATE INDEX idx_users_subscription_tier ON users(subscription_tier);
```

**Eloquent Relationships**:
- `hasOne(UserProfile::class)`
- `hasMany(UserTrigger::class)`
- `hasMany(UserAllergen::class)`
- `hasMany(Scan::class)`
- `hasMany(Attack::class)`
- `hasMany(Collection::class)`

---

#### `user_profiles`
Complete user health and dietary information. One-to-one with `users`.

```sql
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Condition information (20 points in completeness)
    condition_type VARCHAR(50) NOT NULL DEFAULT 'gallstones' CHECK (condition_type IN ('gallstones', 'post_surgery')),
    severity_level VARCHAR(20) NOT NULL DEFAULT 'moderate' CHECK (severity_level IN ('mild', 'moderate', 'severe')),
    diagnosis_date DATE,
    previous_attack_count INT DEFAULT 0,

    -- Health goals (10 points)
    health_goals TEXT[], -- ['avoid_attacks', 'identify_triggers', 'avoid_surgery', 'maintain_weight']

    -- Emergency contacts (5 points)
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(50),
    doctor_name VARCHAR(255),
    doctor_phone VARCHAR(50),

    -- Profile completeness (calculated)
    completeness_percentage INT DEFAULT 0 CHECK (completeness_percentage BETWEEN 0 AND 100),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_severity ON user_profiles(severity_level);
```

**Eloquent Relationships**:
- `belongsTo(User::class)`

**Laravel Casts**:
```php
protected function casts(): array
{
    return [
        'health_goals' => 'array',
        'diagnosis_date' => 'date',
        'completeness_percentage' => 'integer',
    ];
}
```

---

#### `user_triggers`
Food/ingredient triggers causing gallstone attacks. Separate from allergens (clarifications.md Q9).

```sql
CREATE TABLE user_triggers (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    trigger_name VARCHAR(255) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'moderate', 'high')),

    -- Identification source
    identification_source VARCHAR(30) NOT NULL CHECK (identification_source IN ('user_input', 'pattern_detected', 'attack_correlated')),
    confidence_score DECIMAL(5,2) DEFAULT 0.00 CHECK (confidence_score BETWEEN 0 AND 100),

    -- Attack correlation statistics
    attack_correlation_count INT DEFAULT 0,
    total_exposures INT DEFAULT 0, -- Times user scanned/ate this trigger
    last_attack_date TIMESTAMP WITH TIME ZONE,

    -- User notes
    notes TEXT,

    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'rejected', 'pending_confirmation')),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejection_count INT DEFAULT 0, -- Auto-hide after 3 rejections

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, trigger_name) -- No duplicate triggers per user
);

CREATE INDEX idx_user_triggers_user_id ON user_triggers(user_id);
CREATE INDEX idx_user_triggers_severity ON user_triggers(user_id, severity);
CREATE INDEX idx_user_triggers_source ON user_triggers(identification_source);
CREATE INDEX idx_user_triggers_status ON user_triggers(user_id, status);
```

**Eloquent Relationships**:
- `belongsTo(User::class)`
- `hasMany(Scan::class)` (through trigger detections)

**Laravel Validation Rules**:
```php
'trigger_name' => 'required|string|max:255',
'severity' => 'required|in:low,moderate,high',
'identification_source' => 'required|in:user_input,pattern_detected,attack_correlated',
'confidence_score' => 'nullable|numeric|between:0,100',
```

---

#### `user_allergens`
Life-threatening allergens and intolerances. Highest safety priority (score = 0). Separate from triggers.

```sql
CREATE TABLE user_allergens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    allergen_name VARCHAR(255) NOT NULL,
    allergen_type VARCHAR(30) NOT NULL CHECK (allergen_type IN ('life_threatening', 'intolerance')),

    -- Reaction details
    reaction_description TEXT,
    last_reaction_date DATE,
    medical_confirmation BOOLEAN DEFAULT false,

    notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, allergen_name) -- No duplicate allergens per user
);

CREATE INDEX idx_user_allergens_user_id ON user_allergens(user_id);
CREATE INDEX idx_user_allergens_type ON user_allergens(allergen_type);
```

**Eloquent Relationships**:
- `belongsTo(User::class)`

**Business Logic Constraint** (enforced in Laravel, not DB):
- If `allergen_name` exists in `user_allergens`, it cannot exist in `user_triggers` for same user
- Allergen takes precedence during onboarding deduplication

---

#### `dietary_preferences`
Pivot table for user dietary preferences (vegetarian, vegan, gluten-free, etc.).

```sql
CREATE TABLE dietary_preferences (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_name VARCHAR(50) NOT NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, preference_name)
);

CREATE INDEX idx_dietary_preferences_user_id ON dietary_preferences(user_id);
```

**Common Preference Values**:
- `vegetarian`, `vegan`, `pescatarian`, `gluten_free`, `dairy_free`, `low_carb`, `keto`, `paleo`, `halal`, `kosher`

**Eloquent Relationships**:
- `belongsTo(User::class)`

---

#### `scans`
All food scans (barcode and photo). Core entity for scan history and pattern detection.

```sql
CREATE TABLE scans (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Scan metadata
    scan_type VARCHAR(10) NOT NULL CHECK (scan_type IN ('barcode', 'photo')),
    status VARCHAR(20) DEFAULT 'analyzing' CHECK (status IN ('analyzing', 'completed', 'failed')),

    -- Food information
    food_name VARCHAR(255),
    detected_ingredients JSONB, -- ["ingredient1", "ingredient2"]
    ingredients_text TEXT, -- From Open Food Facts or AI

    -- Images
    image_path VARCHAR(500), -- Storage path (public disk or S3)
    image_url VARCHAR(500), -- Public URL
    thumbnail_url VARCHAR(500), -- 300x300px for offline cache

    -- Barcode specific
    barcode VARCHAR(50),
    product_id VARCHAR(100), -- Open Food Facts ID

    -- Analysis results
    safety_score INT CHECK (safety_score BETWEEN 0 AND 100),
    base_score INT, -- Before personalization
    confidence_score DECIMAL(5,2) CHECK (confidence_score BETWEEN 0 AND 100),

    -- Personalization
    problem_ingredients JSONB, -- [{"ingredient": "fried coating", "reason": "matches YOUR trigger: fried foods", "severity": "high"}]
    safe_ingredients JSONB, -- ["grilled chicken", "lettuce"]
    trigger_warnings JSONB, -- ["Your known trigger 'fried foods' detected", "This caused 3 attacks for you"]
    allergen_alert TEXT, -- "CRITICAL: Contains peanuts (YOUR allergen)" or NULL
    personalized_reasoning TEXT, -- 2-3 sentences from AI

    -- Consumption tracking
    consumption_status VARCHAR(20) CHECK (consumption_status IN ('unknown', 'ate', 'avoided', 'modified')),
    consumed_at TIMESTAMP WITH TIME ZONE,

    -- Symptom logging (optional)
    symptoms_logged BOOLEAN DEFAULT false,
    symptom_severity VARCHAR(20) CHECK (symptom_severity IN ('none', 'mild', 'moderate', 'attack')),
    symptom_notes TEXT,
    symptom_logged_at TIMESTAMP WITH TIME ZONE,

    -- Meal context
    meal_type VARCHAR(20) CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack', 'other')),
    location VARCHAR(255), -- Optional restaurant/location name

    -- Analysis metadata
    analyzed_at TIMESTAMP WITH TIME ZONE,
    analysis_duration_ms INT, -- Performance tracking
    ai_model_used VARCHAR(50), -- 'gemini-2.0-flash-thinking-exp' or 'gemini-2.5-flash'
    error_message TEXT,

    -- Sync
    synced_to_devices BOOLEAN DEFAULT false,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE -- Soft delete
);

CREATE INDEX idx_scans_user_id ON scans(user_id, created_at DESC); -- Primary history query
CREATE INDEX idx_scans_barcode ON scans(barcode); -- Barcode lookup
CREATE INDEX idx_scans_status ON scans(user_id, status); -- Pending analysis
CREATE INDEX idx_scans_consumed_at ON scans(user_id, consumed_at) WHERE consumed_at IS NOT NULL; -- Attack correlation
CREATE INDEX idx_scans_safety_score ON scans(user_id, safety_score); -- Filter by safety
CREATE INDEX idx_scans_created_at ON scans(created_at); -- Date range queries
CREATE INDEX idx_scans_consumption_status ON scans(user_id, consumption_status); -- Pattern detection
```

**Eloquent Relationships**:
- `belongsTo(User::class)`
- `belongsToMany(Collection::class)` (via `collection_scans` pivot)
- `hasMany(Attack::class)` (attacks within 8 hours)

**Laravel Casts**:
```php
protected function casts(): array
{
    return [
        'detected_ingredients' => 'array',
        'problem_ingredients' => 'array',
        'safe_ingredients' => 'array',
        'trigger_warnings' => 'array',
        'consumed_at' => 'datetime',
        'symptom_logged_at' => 'datetime',
        'analyzed_at' => 'datetime',
        'symptoms_logged' => 'boolean',
        'synced_to_devices' => 'boolean',
    ];
}
```

**Laravel Validation Rules**:
```php
'scan_type' => 'required|in:barcode,photo',
'food_name' => 'nullable|string|max:255',
'safety_score' => 'nullable|integer|between:0,100',
'consumption_status' => 'nullable|in:unknown,ate,avoided,modified',
'symptom_severity' => 'nullable|in:none,mild,moderate,attack',
```

---

#### `attacks`
Gallstone attack episodes. Used for pattern detection and trigger correlation.

```sql
CREATE TABLE attacks (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Attack timing
    onset_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Critical for correlation
    duration_minutes INT, -- Attack duration

    -- Pain details
    pain_intensity INT CHECK (pain_intensity BETWEEN 1 AND 10),
    pain_location JSONB, -- Body diagram coordinates: {"x": 150, "y": 200, "region": "upper_right_abdomen"}

    -- Symptoms (checklist)
    symptoms JSONB, -- {"nausea": true, "vomiting": true, "fever": false, "jaundice": false, "back_pain": true}

    -- Medical care
    medical_care_type VARCHAR(30) CHECK (medical_care_type IN ('none', 'home_treatment', 'doctor_call', 'urgent_care', 'emergency_room', 'hospitalized')),
    diagnosis_received TEXT,
    treatment_received TEXT,

    -- Pattern detection
    correlated_scans_analyzed BOOLEAN DEFAULT false,
    suspected_trigger_id BIGINT REFERENCES user_triggers(id) ON DELETE SET NULL,
    correlation_confidence DECIMAL(5,2), -- From pattern detection algorithm

    -- User notes
    notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_attacks_user_id ON attacks(user_id, onset_at DESC); -- Attack history
CREATE INDEX idx_attacks_onset_at ON attacks(user_id, onset_at); -- Correlation window queries
CREATE INDEX idx_attacks_correlated ON attacks(user_id, correlated_scans_analyzed); -- Pattern detection queue
```

**Eloquent Relationships**:
- `belongsTo(User::class)`
- `belongsTo(UserTrigger::class, 'suspected_trigger_id')`
- `hasMany(Scan::class)` (scans 1-8 hours before onset)

**Laravel Casts**:
```php
protected function casts(): array
{
    return [
        'onset_at' => 'datetime',
        'pain_location' => 'array',
        'symptoms' => 'array',
        'correlated_scans_analyzed' => 'boolean',
    ];
}
```

**Business Logic** (clarifications.md Q4):
- Correlation window: Scans between `onset_at - 8 hours` and `onset_at - 1 hour`
- Confidence weights: 3-6h = 100%, 1-2h = 70%, 7-8h = 60%

---

#### `pattern_detection_results`
AI-identified correlations between foods and attacks. Suggested triggers shown to user.

```sql
CREATE TABLE pattern_detection_results (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Suspected trigger
    suspected_trigger_name VARCHAR(255) NOT NULL,
    confidence_score DECIMAL(5,2) NOT NULL CHECK (confidence_score BETWEEN 0 AND 100),

    -- Evidence
    correlation_count INT NOT NULL, -- Times this food appeared before attacks
    total_exposures INT, -- Times user ate this food (from scans)
    consistency_rate DECIMAL(5,2), -- (correlations / total_exposures) * 100

    -- Correlation details
    evidence JSONB, -- [{"scan_id": 123, "attack_id": 45, "hours_before": 4, "match_weight": 100}]
    date_range_start DATE,
    date_range_end DATE,

    -- Time window scoring (clarifications.md Q1)
    primary_window_count INT DEFAULT 0, -- 3-6 hours (100% weight)
    extended_window_count INT DEFAULT 0, -- 1-2h + 7-8h (70% / 60% weight)

    -- Recency bonus
    most_recent_correlation_date DATE,
    recency_bonus_score DECIMAL(5,2), -- 25% of total confidence

    -- User response
    status VARCHAR(30) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected')),
    user_response_at TIMESTAMP WITH TIME ZONE,

    -- If confirmed, creates entry in user_triggers
    created_trigger_id BIGINT REFERENCES user_triggers(id) ON DELETE SET NULL,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_pattern_results_user_id ON pattern_detection_results(user_id, status);
CREATE INDEX idx_pattern_results_confidence ON pattern_detection_results(confidence_score DESC);
CREATE INDEX idx_pattern_results_created_at ON pattern_detection_results(created_at);
```

**Eloquent Relationships**:
- `belongsTo(User::class)`
- `belongsTo(UserTrigger::class, 'created_trigger_id')`

**Laravel Casts**:
```php
protected function casts(): array
{
    return [
        'evidence' => 'array',
        'date_range_start' => 'date',
        'date_range_end' => 'date',
        'most_recent_correlation_date' => 'date',
        'user_response_at' => 'datetime',
    ];
}
```

**Confidence Score Formula** (clarifications.md Q1):
```
Confidence Score = (Correlation Count × 25%) + (Match Weight × 20%) + (Consistency Bonus × 30%) + (Recency Bonus × 25%)
```

---

#### `recipe_modifications`
AI-generated safer versions of risky meals. Premium feature.

```sql
CREATE TABLE recipe_modifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    original_scan_id BIGINT REFERENCES scans(id) ON DELETE SET NULL,

    -- Original meal
    original_meal_name VARCHAR(255) NOT NULL,
    original_safety_score INT NOT NULL,
    original_ingredients JSONB,

    -- Modified meal
    modified_meal_name VARCHAR(255) NOT NULL,
    modified_safety_score INT NOT NULL,

    -- Substitutions
    substitutions JSONB, -- [{"original": "heavy cream", "replacement": "greek yogurt", "reason": "Avoids your full-fat dairy trigger"}]

    -- Nutritional comparison
    original_nutrition JSONB, -- {"calories": 850, "fat_g": 45, "saturated_fat_g": 24}
    modified_nutrition JSONB, -- {"calories": 520, "fat_g": 18, "saturated_fat_g": 6}

    -- Cooking instructions
    cooking_instructions JSONB, -- ["Step 1: ...", "Step 2: ...", "Step 3: ..."] (max 5 steps)
    prep_time_minutes INT,
    cook_time_minutes INT,

    -- User engagement
    user_rating INT CHECK (user_rating BETWEEN 1 AND 5),
    user_made_it BOOLEAN DEFAULT false,
    user_notes TEXT,

    -- Image (optional, user can upload photo of modified version)
    modified_image_path VARCHAR(500),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_recipe_mods_user_id ON recipe_modifications(user_id, created_at DESC);
CREATE INDEX idx_recipe_mods_scan_id ON recipe_modifications(original_scan_id);
CREATE INDEX idx_recipe_mods_rating ON recipe_modifications(user_rating) WHERE user_rating IS NOT NULL;
```

**Eloquent Relationships**:
- `belongsTo(User::class)`
- `belongsTo(Scan::class, 'original_scan_id')`

**Laravel Casts**:
```php
protected function casts(): array
{
    return [
        'original_ingredients' => 'array',
        'substitutions' => 'array',
        'original_nutrition' => 'array',
        'modified_nutrition' => 'array',
        'cooking_instructions' => 'array',
        'user_made_it' => 'boolean',
    ];
}
```

**Laravel Validation Rules**:
```php
'original_meal_name' => 'required|string|max:255',
'modified_meal_name' => 'required|string|max:255',
'original_safety_score' => 'required|integer|between:0,100',
'modified_safety_score' => 'required|integer|between:0,100',
'substitutions' => 'required|array|min:1',
'cooking_instructions' => 'required|array|max:5',
```

---

#### `collections`
User-created favorites/collections (e.g., "My Safe Breakfasts", "Restaurants I Trust").

```sql
CREATE TABLE collections (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    name VARCHAR(255) NOT NULL,
    description TEXT,

    -- Statistics (cached for performance)
    scan_count INT DEFAULT 0,
    average_safety_score DECIMAL(5,2),

    -- Free tier limit: 3 collections
    -- Premium: unlimited

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_accessed_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_collections_user_id ON collections(user_id, created_at DESC);
```

**Eloquent Relationships**:
- `belongsTo(User::class)`
- `belongsToMany(Scan::class)` (via `collection_scans` pivot)

---

#### `collection_scans`
Pivot table linking collections to scans (many-to-many).

```sql
CREATE TABLE collection_scans (
    id BIGSERIAL PRIMARY KEY,
    collection_id BIGINT NOT NULL REFERENCES collections(id) ON DELETE CASCADE,
    scan_id BIGINT NOT NULL REFERENCES scans(id) ON DELETE CASCADE,

    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(collection_id, scan_id)
);

CREATE INDEX idx_collection_scans_collection_id ON collection_scans(collection_id);
CREATE INDEX idx_collection_scans_scan_id ON collection_scans(scan_id);
```

---

#### `subscriptions`
Laravel Cashier subscription table (managed by Cashier, extended for GallDiet).

```sql
CREATE TABLE subscriptions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Cashier fields (standard)
    type VARCHAR(255) NOT NULL DEFAULT 'default', -- Subscription type
    stripe_id VARCHAR(255) NOT NULL UNIQUE, -- Stripe subscription ID
    stripe_status VARCHAR(255) NOT NULL, -- 'active', 'canceled', 'incomplete', 'past_due', 'trialing'
    stripe_price VARCHAR(255), -- Stripe price ID
    quantity INT,

    -- Trial
    trial_ends_at TIMESTAMP WITH TIME ZONE,

    -- Subscription period
    ends_at TIMESTAMP WITH TIME ZONE, -- Cancellation date (if cancelled)

    -- GallDiet extensions
    grace_period_ends_at TIMESTAMP WITH TIME ZONE, -- 3-day grace for failed payments
    previous_stripe_status VARCHAR(255), -- Track status changes

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, type)
);

CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_id ON subscriptions(stripe_id);
CREATE INDEX idx_subscriptions_stripe_status ON subscriptions(stripe_status);
CREATE INDEX idx_subscriptions_trial_ends_at ON subscriptions(trial_ends_at) WHERE trial_ends_at IS NOT NULL;
CREATE INDEX idx_subscriptions_grace_period ON subscriptions(grace_period_ends_at) WHERE grace_period_ends_at IS NOT NULL;
```

**Eloquent Relationships**:
- `belongsTo(User::class)`

**State Machine** (clarifications.md Q7):
- `free` → `trialing` → `active` → `canceled` / `past_due` → `lapsed`
- Grace period: 3 days of Premium access after `past_due` status

---

#### `subscription_items`
Laravel Cashier subscription items (managed by Cashier).

```sql
CREATE TABLE subscription_items (
    id BIGSERIAL PRIMARY KEY,
    subscription_id BIGINT NOT NULL REFERENCES subscriptions(id) ON DELETE CASCADE,
    stripe_id VARCHAR(255) NOT NULL UNIQUE,
    stripe_product VARCHAR(255) NOT NULL,
    stripe_price VARCHAR(255) NOT NULL,
    quantity INT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(subscription_id, stripe_price)
);

CREATE INDEX idx_subscription_items_subscription_id ON subscription_items(subscription_id);
CREATE INDEX idx_subscription_items_stripe_id ON subscription_items(stripe_id);
```

---

### Supporting Tables

#### `sync_queue`
Tracks pending changes for multi-device sync (clarifications.md Q10).

```sql
CREATE TABLE sync_queue (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    change_type VARCHAR(50) NOT NULL, -- 'profile_update', 'trigger_added', 'scan_marked', 'attack_logged'
    entity_type VARCHAR(50) NOT NULL, -- 'user_profile', 'user_trigger', 'scan', 'attack'
    entity_id BIGINT,

    change_data JSONB, -- Full entity data or delta

    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    broadcast_via_sse BOOLEAN DEFAULT false, -- If true, sent via SSE to other devices

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_sync_queue_user_id ON sync_queue(user_id, status);
CREATE INDEX idx_sync_queue_created_at ON sync_queue(created_at);
```

**Purpose**: Real-time sync via SSE. Backend broadcasts changes to all user's connected devices.

---

#### `weekly_summaries`
Automated analysis of user's week (scan trends, safety scores, recommendations).

```sql
CREATE TABLE weekly_summaries (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    week_start_date DATE NOT NULL,
    week_end_date DATE NOT NULL,

    -- Statistics
    total_scans INT DEFAULT 0,
    photo_scans INT DEFAULT 0,
    barcode_scans INT DEFAULT 0,
    average_safety_score DECIMAL(5,2),
    score_trend VARCHAR(20), -- 'improving', 'stable', 'declining'

    -- Safety breakdown
    safe_meals_count INT DEFAULT 0, -- Score 80-100
    moderate_meals_count INT DEFAULT 0, -- Score 50-79
    risky_meals_count INT DEFAULT 0, -- Score 0-49

    -- Trigger avoidance
    trigger_exposure_count INT DEFAULT 0,
    trigger_avoidance_rate DECIMAL(5,2), -- % of meals without triggers

    -- Attack-free tracking
    attack_free_days INT DEFAULT 7,

    -- Insights (AI-generated or rule-based)
    insights JSONB, -- ["You avoided fried foods 6 out of 7 days!", "Your average score improved 12 points"]
    recommendations JSONB, -- ["Try scanning breakfast more often", "Consider adding gluten-free preference"]

    -- Free vs Premium
    summary_type VARCHAR(20) DEFAULT 'basic' CHECK (summary_type IN ('basic', 'detailed')),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_weekly_summaries_user_id ON weekly_summaries(user_id, week_start_date DESC);
CREATE INDEX idx_weekly_summaries_week_start ON weekly_summaries(week_start_date);
```

**Eloquent Relationships**:
- `belongsTo(User::class)`

**Laravel Casts**:
```php
protected function casts(): array
{
    return [
        'week_start_date' => 'date',
        'week_end_date' => 'date',
        'insights' => 'array',
        'recommendations' => 'array',
    ];
}
```

---

## Entity Relationships

### Relationship Summary

```
User
├─ hasOne: UserProfile
├─ hasMany: UserTriggers
├─ hasMany: UserAllergens
├─ hasMany: DietaryPreferences
├─ hasMany: Scans
├─ hasMany: Attacks
├─ hasMany: PatternDetectionResults
├─ hasMany: RecipeModifications
├─ hasMany: Collections
├─ hasMany: WeeklySummaries
└─ hasOne: Subscription

Scan
├─ belongsTo: User
├─ belongsToMany: Collections (via collection_scans)
└─ correlatedAttacks() (custom relationship: attacks within 8 hours)

Attack
├─ belongsTo: User
├─ belongsTo: UserTrigger (suspected_trigger_id)
└─ correlatedScans() (custom relationship: scans 1-8 hours before onset)

UserTrigger
├─ belongsTo: User
└─ hasMany: Attacks (suspected_trigger_id)

PatternDetectionResult
├─ belongsTo: User
└─ belongsTo: UserTrigger (created_trigger_id)
```

### Cascade Delete Rules

- **User deleted** → CASCADE delete all related data (profile, triggers, scans, attacks, collections)
- **Scan deleted** → SOFT DELETE (scans.deleted_at), preserve attack correlations
- **Collection deleted** → CASCADE delete collection_scans pivot entries
- **Attack deleted** → SET NULL on pattern_detection_results.suspected_trigger_id
- **Trigger deleted** → SET NULL on attacks.suspected_trigger_id

---

## Indexes Strategy

### Primary Indexes (Performance Critical)

**Hot Path Queries** (cached in Redis):
1. User profile lookup: `users.id` → `user_profiles.user_id` (1:1, cached)
2. User triggers: `user_triggers.user_id + status = 'active'` (cached, TTL 1 hour)
3. User allergens: `user_allergens.user_id` (cached, TTL 1 hour)

**Scan History** (paginated from PostgreSQL):
```sql
-- Index for primary scan history query
CREATE INDEX idx_scans_user_history ON scans(user_id, created_at DESC) WHERE deleted_at IS NULL;

-- Query pattern:
SELECT * FROM scans
WHERE user_id = ? AND deleted_at IS NULL
ORDER BY created_at DESC
LIMIT 30 OFFSET 0;
```

**Attack Correlation** (pattern detection):
```sql
-- Index for "scans 1-8 hours before attack"
CREATE INDEX idx_scans_correlation_window ON scans(user_id, consumed_at)
WHERE consumed_at IS NOT NULL AND consumption_status = 'ate';

-- Query pattern:
SELECT * FROM scans
WHERE user_id = ?
  AND consumed_at BETWEEN (? - INTERVAL '8 hours') AND (? - INTERVAL '1 hour')
  AND consumption_status = 'ate';
```

### Composite Indexes

```sql
-- Filter scans by safety score + date
CREATE INDEX idx_scans_safety_date ON scans(user_id, safety_score, created_at DESC);

-- Filter scans by trigger detection (JSONB GIN index)
CREATE INDEX idx_scans_triggers_detected ON scans USING GIN (trigger_warnings);

-- Pattern detection: pending analysis
CREATE INDEX idx_attacks_pending_correlation ON attacks(user_id, correlated_scans_analyzed, onset_at);
```

### Unique Indexes (Business Logic)

```sql
-- No duplicate triggers per user
CREATE UNIQUE INDEX idx_user_triggers_unique ON user_triggers(user_id, trigger_name) WHERE status = 'active';

-- No duplicate allergens per user
CREATE UNIQUE INDEX idx_user_allergens_unique ON user_allergens(user_id, allergen_name);

-- No duplicate scans in collections
CREATE UNIQUE INDEX idx_collection_scans_unique ON collection_scans(collection_id, scan_id);
```

---

## Caching Strategy

### Redis Cache (Hot Data)

**Cache Keys**:
```
user:{user_id}:profile → Full user profile JSON (TTL: 1 hour)
user:{user_id}:triggers → Active triggers array (TTL: 1 hour)
user:{user_id}:allergens → Allergens array (TTL: 1 hour)
user:{user_id}:subscription → Subscription tier + status (TTL: 10 minutes)
barcode:{barcode} → Open Food Facts product data (TTL: 90 days)
scan:{scan_id} → Recent scan result (TTL: 24 hours)
```

**Cache Invalidation Triggers**:
- Profile updated → Invalidate `user:{user_id}:profile`
- Trigger added/removed → Invalidate `user:{user_id}:triggers`
- Subscription changed → Invalidate `user:{user_id}:subscription`
- Attack logged → Invalidate pattern detection cache (if exists)

**Cache-Through Pattern**:
```php
// ProfileService.php
public function getUserProfile(int $userId): UserProfile
{
    return Cache::remember("user:{$userId}:profile", 3600, function () use ($userId) {
        return UserProfile::with(['user.triggers', 'user.allergens'])
            ->where('user_id', $userId)
            ->firstOrFail();
    });
}
```

### PostgreSQL (Cold Data)

**Fetch from Database**:
- Scan history (paginated): Frequent writes, needs fresh data
- Attack history: Infrequent access, full history required for pattern detection
- Pattern detection results: Generated asynchronously, not cached
- Weekly summaries: Pre-computed, infrequent access

---

## Data Validation Rules

### User Profile Validation

```php
// UpdateProfileRequest.php
public function rules(): array
{
    return [
        'condition_type' => 'required|in:gallstones,post_surgery',
        'severity_level' => 'required|in:mild,moderate,severe',
        'diagnosis_date' => 'nullable|date|before_or_equal:today',
        'previous_attack_count' => 'nullable|integer|min:0|max:1000',
        'health_goals' => 'nullable|array',
        'health_goals.*' => 'in:avoid_attacks,identify_triggers,avoid_surgery,maintain_weight,reduce_medication',
        'emergency_contact_name' => 'nullable|string|max:255',
        'emergency_contact_phone' => 'nullable|string|max:50',
        'doctor_name' => 'nullable|string|max:255',
        'doctor_phone' => 'nullable|string|max:50',
    ];
}

public function messages(): array
{
    return [
        'diagnosis_date.before_or_equal' => 'Diagnosis date cannot be in the future.',
        'previous_attack_count.max' => 'If you\'ve had more than 1000 attacks, please consult your doctor immediately.',
    ];
}
```

### Trigger Validation

```php
// StoreUserTriggerRequest.php
public function rules(): array
{
    return [
        'trigger_name' => [
            'required',
            'string',
            'max:255',
            Rule::unique('user_triggers')->where(function ($query) {
                return $query->where('user_id', $this->user()->id)
                    ->where('status', 'active');
            }),
        ],
        'severity' => 'required|in:low,moderate,high',
        'identification_source' => 'required|in:user_input,pattern_detected,attack_correlated',
        'notes' => 'nullable|string|max:1000',
    ];
}

public function messages(): array
{
    return [
        'trigger_name.unique' => 'You\'ve already added this trigger. Update the existing one instead.',
    ];
}
```

### Scan Validation

```php
// StoreScanRequest.php
public function rules(): array
{
    return [
        'scan_type' => 'required|in:barcode,photo',
        'barcode' => 'required_if:scan_type,barcode|nullable|string|max:50',
        'photo' => 'required_if:scan_type,photo|nullable|file|mimes:jpg,jpeg,png,heic|max:10240', // 10MB
        'meal_type' => 'nullable|in:breakfast,lunch,dinner,snack,other',
        'location' => 'nullable|string|max:255',
    ];
}

protected function passedValidation()
{
    // Check daily scan limit for free tier
    if ($this->user()->subscription_tier === 'free' && $this->scan_type === 'photo') {
        $todayScans = Scan::where('user_id', $this->user()->id)
            ->where('scan_type', 'photo')
            ->whereDate('created_at', today())
            ->count();

        if ($todayScans >= 3) {
            throw ValidationException::withMessages([
                'scan_type' => 'Daily scan limit reached (3/day). Upgrade to Premium for unlimited scans.',
            ]);
        }
    }
}
```

### Attack Validation

```php
// StoreAttackRequest.php
public function rules(): array
{
    return [
        'onset_at' => 'required|date|before_or_equal:now',
        'duration_minutes' => 'nullable|integer|min:1|max:10080', // Max 1 week
        'pain_intensity' => 'required|integer|between:1,10',
        'pain_location' => 'nullable|array',
        'pain_location.x' => 'required_with:pain_location|numeric',
        'pain_location.y' => 'required_with:pain_location|numeric',
        'pain_location.region' => 'required_with:pain_location|string',
        'symptoms' => 'nullable|array',
        'symptoms.nausea' => 'boolean',
        'symptoms.vomiting' => 'boolean',
        'symptoms.fever' => 'boolean',
        'symptoms.jaundice' => 'boolean',
        'symptoms.back_pain' => 'boolean',
        'medical_care_type' => 'required|in:none,home_treatment,doctor_call,urgent_care,emergency_room,hospitalized',
        'diagnosis_received' => 'nullable|string|max:1000',
        'treatment_received' => 'nullable|string|max:1000',
        'notes' => 'nullable|string|max:2000',
    ];
}
```

---

## Sample Data (For Testing)

### Factory Definitions

#### UserFactory

```php
// database/factories/UserFactory.php
class UserFactory extends Factory
{
    public function definition(): array
    {
        return [
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'subscription_tier' => 'free',
            'trial_used' => false,
            'remember_token' => Str::random(10),
        ];
    }

    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_tier' => 'premium',
            'trial_used' => true,
            'subscription_expires_at' => now()->addMonth(),
        ]);
    }

    public function trialActive(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_tier' => 'premium',
            'trial_ends_at' => now()->addDays(5),
        ]);
    }
}
```

#### UserProfileFactory

```php
// database/factories/UserProfileFactory.php
class UserProfileFactory extends Factory
{
    public function definition(): array
    {
        return [
            'condition_type' => 'gallstones',
            'severity_level' => fake()->randomElement(['mild', 'moderate', 'severe']),
            'diagnosis_date' => fake()->dateTimeBetween('-2 years', '-1 month'),
            'previous_attack_count' => fake()->numberBetween(0, 10),
            'health_goals' => fake()->randomElements(
                ['avoid_attacks', 'identify_triggers', 'avoid_surgery', 'maintain_weight'],
                fake()->numberBetween(1, 3)
            ),
            'completeness_percentage' => 0, // Calculated after creation
        ];
    }

    public function withEmergencyContact(): static
    {
        return $this->state(fn (array $attributes) => [
            'emergency_contact_name' => fake()->name(),
            'emergency_contact_phone' => fake()->phoneNumber(),
        ]);
    }

    public function complete(): static
    {
        return $this->state(fn (array $attributes) => [
            'emergency_contact_name' => fake()->name(),
            'emergency_contact_phone' => fake()->phoneNumber(),
            'doctor_name' => 'Dr. ' . fake()->lastName(),
            'doctor_phone' => fake()->phoneNumber(),
            'completeness_percentage' => 85,
        ]);
    }
}
```

#### UserTriggerFactory

```php
// database/factories/UserTriggerFactory.php
class UserTriggerFactory extends Factory
{
    public function definition(): array
    {
        return [
            'trigger_name' => fake()->randomElement([
                'fried foods', 'full-fat dairy', 'chocolate', 'spicy foods',
                'red meat', 'coconut products', 'eggs', 'processed meats'
            ]),
            'severity' => fake()->randomElement(['low', 'moderate', 'high']),
            'identification_source' => 'user_input',
            'confidence_score' => 100.00,
            'attack_correlation_count' => 0,
            'total_exposures' => 0,
            'status' => 'active',
        ];
    }

    public function patternDetected(): static
    {
        return $this->state(fn (array $attributes) => [
            'identification_source' => 'pattern_detected',
            'confidence_score' => fake()->randomFloat(2, 60, 95),
            'attack_correlation_count' => fake()->numberBetween(2, 5),
            'total_exposures' => fake()->numberBetween(3, 10),
            'confirmed_at' => now(),
        ]);
    }
}
```

#### ScanFactory

```php
// database/factories/ScanFactory.php
class ScanFactory extends Factory
{
    public function definition(): array
    {
        $isSafe = fake()->boolean(60); // 60% safe meals

        return [
            'scan_type' => fake()->randomElement(['barcode', 'photo']),
            'status' => 'completed',
            'food_name' => fake()->randomElement([
                'Grilled Chicken Salad', 'Avocado Toast', 'Greek Yogurt Bowl',
                'Fried Chicken', 'Chocolate Cake', 'Pizza', 'Ice Cream'
            ]),
            'detected_ingredients' => ['ingredient 1', 'ingredient 2', 'ingredient 3'],
            'image_path' => 'scans/' . fake()->uuid() . '.jpg',
            'safety_score' => $isSafe ? fake()->numberBetween(80, 100) : fake()->numberBetween(10, 49),
            'base_score' => fake()->numberBetween(50, 90),
            'confidence_score' => fake()->randomFloat(2, 75, 95),
            'consumption_status' => fake()->randomElement(['ate', 'avoided', 'unknown']),
            'consumed_at' => fake()->dateTimeBetween('-30 days', 'now'),
            'analyzed_at' => now(),
            'ai_model_used' => 'gemini-2.0-flash-thinking-exp',
        ];
    }

    public function risky(): static
    {
        return $this->state(fn (array $attributes) => [
            'food_name' => 'Fried Chicken with Fries',
            'safety_score' => fake()->numberBetween(10, 35),
            'trigger_warnings' => ['Your known trigger "fried foods" detected'],
            'problem_ingredients' => [
                ['ingredient' => 'fried coating', 'reason' => 'matches YOUR trigger: fried foods', 'severity' => 'high']
            ],
        ]);
    }

    public function safe(): static
    {
        return $this->state(fn (array $attributes) => [
            'food_name' => 'Grilled Salmon with Vegetables',
            'safety_score' => fake()->numberBetween(85, 100),
            'safe_ingredients' => ['grilled salmon', 'steamed broccoli', 'brown rice'],
        ]);
    }
}
```

#### AttackFactory

```php
// database/factories/AttackFactory.php
class AttackFactory extends Factory
{
    public function definition(): array
    {
        return [
            'onset_at' => fake()->dateTimeBetween('-90 days', '-1 day'),
            'duration_minutes' => fake()->numberBetween(30, 480),
            'pain_intensity' => fake()->numberBetween(5, 10),
            'pain_location' => ['x' => 150, 'y' => 200, 'region' => 'upper_right_abdomen'],
            'symptoms' => [
                'nausea' => fake()->boolean(80),
                'vomiting' => fake()->boolean(50),
                'fever' => fake()->boolean(20),
                'jaundice' => fake()->boolean(10),
                'back_pain' => fake()->boolean(60),
            ],
            'medical_care_type' => fake()->randomElement([
                'emergency_room', 'urgent_care', 'doctor_call', 'home_treatment'
            ]),
            'correlated_scans_analyzed' => false,
        ];
    }
}
```

### Seeder Examples

#### PatternDetectionTestSeeder

```php
// database/seeders/PatternDetectionTestSeeder.php
class PatternDetectionTestSeeder extends Seeder
{
    /**
     * Create test user with data for pattern detection testing.
     */
    public function run(): void
    {
        // Create user with profile
        $user = User::factory()
            ->has(UserProfile::factory()->complete())
            ->create(['email' => '<EMAIL>']);

        // Add known trigger (user input)
        $friedFoodsTrigger = UserTrigger::factory()->create([
            'user_id' => $user->id,
            'trigger_name' => 'fried foods',
            'severity' => 'high',
            'attack_correlation_count' => 3,
        ]);

        // Create 20 scans over past 30 days
        $scans = Scan::factory(20)->create([
            'user_id' => $user->id,
        ]);

        // Create 3 attacks
        $attacks = Attack::factory(3)->create([
            'user_id' => $user->id,
        ]);

        // Manually correlate: Scan "fried chicken" → Attack 4 hours later (3 times)
        foreach ($attacks as $index => $attack) {
            $scan = $scans[$index * 5]; // Every 5th scan
            $scan->update([
                'food_name' => 'Fried Chicken with Fries',
                'safety_score' => 25,
                'consumption_status' => 'ate',
                'consumed_at' => $attack->onset_at->subHours(4), // 4 hours before attack
            ]);
        }

        // Pattern detection should identify "fried chicken" with 87% confidence
        // (3 correlations in 3-6h window, 100% consistency)
    }
}
```

---

## Database Migrations Execution Order

1. `users` (base table)
2. `user_profiles` (1:1 with users)
3. `user_triggers`, `user_allergens`, `dietary_preferences` (user foreign keys)
4. `scans` (user foreign key)
5. `attacks` (user + trigger foreign keys)
6. `pattern_detection_results` (user + trigger foreign keys)
7. `recipe_modifications` (user + scan foreign keys)
8. `collections` (user foreign key)
9. `collection_scans` (collection + scan foreign keys)
10. `subscriptions`, `subscription_items` (Laravel Cashier, user foreign key)
11. `sync_queue`, `weekly_summaries` (supporting tables)

---

## Performance Considerations

### Query Optimization

**Scan History Pagination** (Most Frequent Query):
```php
// Optimized with eager loading
Scan::with(['user.profile'])
    ->where('user_id', $userId)
    ->whereNull('deleted_at')
    ->orderBy('created_at', 'desc')
    ->cursorPaginate(30); // Cursor pagination for infinite scroll
```

**Attack Correlation** (Pattern Detection):
```php
// Use indexed columns for time window
Scan::where('user_id', $userId)
    ->where('consumption_status', 'ate')
    ->whereBetween('consumed_at', [
        $attack->onset_at->subHours(8),
        $attack->onset_at->subHours(1)
    ])
    ->orderBy('consumed_at', 'desc')
    ->get();
```

### N+1 Prevention

**Always Eager Load**:
- User profile when fetching user: `User::with('profile')`
- Triggers when analyzing scans: `User::with('triggers', 'allergens')`
- Collections when displaying scan: `Scan::with('collections')`

### Database Connection Pooling

```php
// config/database.php (PostgreSQL)
'pgsql' => [
    'driver' => 'pgsql',
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '5432'),
    'database' => env('DB_DATABASE', 'galldiet'),
    'username' => env('DB_USERNAME', 'postgres'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8',
    'prefix' => '',
    'schema' => 'public',
    'sslmode' => 'prefer',
    'options' => [
        PDO::ATTR_PERSISTENT => true, // Connection pooling
        PDO::ATTR_EMULATE_PREPARES => false,
    ],
],
```

---

## Data Model Complete

This data model supports all MVP requirements:
- ✅ Personalized scan analysis with user-specific triggers/allergens
- ✅ Pattern detection with 1-8 hour correlation windows
- ✅ Multi-device sync with conflict resolution
- ✅ Subscription management (Laravel Cashier + Stripe)
- ✅ Offline functionality (AsyncStorage cache strategy)
- ✅ Emergency support (attack logging with symptom tracking)
- ✅ Recipe modifications (Premium feature)
- ✅ Collections/favorites
- ✅ Weekly summaries
- ✅ GDPR compliance (soft deletes, data export ready)

**Next Phase**: API Contract Design (`plan.md`) defining endpoints, request/response formats, and authentication flows.
