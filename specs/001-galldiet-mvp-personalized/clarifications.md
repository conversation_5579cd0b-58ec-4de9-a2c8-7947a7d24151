# Specification Clarifications: <PERSON><PERSON><PERSON><PERSON> MVP

**Feature**: <PERSON><PERSON><PERSON><PERSON> MVP - Personalized Gallstone Diet Management
**Clarification Date**: 2025-10-12
**Requested By**: User
**Status**: Resolved

## Overview

This document addresses 10 specific clarification questions raised after initial specification review. Each question includes context, multiple answer options with implications, and the selected resolution that will be incorporated into the planning phase.

---

## Question 1: Pattern Detection Algorithm Specifics

**Context**: The spec mentions pattern detection with "3+ correlations" for trigger identification (FR-068, SC-015), but the algorithm details affect database schema, complexity, and whether data science expertise is required.

**Clarification Questions**:
- What constitutes a valid correlation?
- How should the system handle partial matches?
- Should confidence scoring use statistical methods or heuristics?
- What's the minimum data set needed before pattern detection activates?

### Selected Answer

**Valid Correlation Definition**:
- Food scanned and marked "I ate this" between 1-8 hours before attack onset
- Primary window: 3-6 hours (most common for gallstone attacks)
- Extended window: 1-8 hours (catches early/delayed reactions)
- System checks both windows, weights 3-6 hour matches higher in confidence scoring

**Partial Match Handling**:
- **Exact match**: "Fried chicken" in scan → "Fried chicken" suggested trigger = **100% match weight**
- **Category match**: "Fried chicken" in scan → "Fried foods" (user's known trigger) = **80% match weight**
- **Ingredient match**: "Chicken burrito" contains "fried tortilla" → "Fried foods" trigger = **60% match weight**
- **Substring match**: "Chocolate cake" → "Chocolate" trigger = **90% match weight**
- No match = excluded from correlation

**Confidence Scoring** (Heuristic-based for MVP):

```
Confidence Score = (Correlation Count × 25%) + (Match Weight × 20%) + (Consistency Bonus × 30%) + (Recency Bonus × 25%)

Where:
- Correlation Count: 2 correlations = 50%, 3 = 75%, 4+ = 100%
- Match Weight: Average of all match weights (exact/category/ingredient)
- Consistency Bonus: (Positive Correlations / Total Instances) × 100
  - Example: 3 attacks after eating chocolate, 0 times eating chocolate without attack = 100% consistency
- Recency Bonus: Most recent correlation within 30 days = 100%, 31-60 days = 75%, 61-90 days = 50%, 90+ days = 25%
```

**Example Calculation**:
- User ate "fried chicken" 3 times, had attack 3-6 hours later all 3 times, never ate without attack
- Correlation Count: 3 × 25% = 75%
- Match Weight: 100% × 20% = 20% (exact match)
- Consistency: (3/3) × 30% = 30%
- Recency: Last correlation 15 days ago × 25% = 25%
- **Total Confidence: 150% → capped at 100% = shows as 100% confidence**

**Minimum Data Set**:
- **Hard minimum**: 5 total scans + 1 logged attack
- **Useful threshold**: 15 total scans + 2 logged attacks (starts showing suggestions)
- **High confidence threshold**: 30 total scans + 3 logged attacks
- Pattern detection runs automatically after each attack is logged (if minimums met)
- System won't suggest triggers with confidence below 60% (avoids false positives)

**Why This Approach**:
- **Heuristic (not statistical)**: Simpler to implement, no data science expertise required for MVP
- **Weighted scoring**: Accounts for match quality, consistency, and recency
- **Progressive disclosure**: Users with 2 correlations see suggestions earlier, but lower confidence
- **Avoids false positives**: 60% minimum confidence threshold prevents spurious suggestions
- **Transparent**: Confidence score shown to user with explanation

**Implementation Notes**:
- Store correlation data in `pattern_detection_results` table with all components for audit trail
- Allow manual override (user can confirm lower-confidence triggers if they believe them)
- Future enhancement: Statistical model (logistic regression) after collecting real-world data

---

## Question 2: AI Prompt Engineering Requirements

**Context**: Multiple requirements reference personalization in AI prompts (FR-025, FR-031-036), but prompt structure affects token costs, consistency, and whether JSON schema validation is needed.

**Clarification Questions**:
- How should user profile data be structured in prompts?
- What's the priority order when multiple triggers apply?
- How verbose should AI reasoning be?
- Should AI responses be constrained to specific format/fields?

### Selected Answer

**Prompt Structure** (Structured JSON + Natural Language):

```
You are a dietary safety analyzer for users with gallstones. Analyze this meal photo and provide personalized safety assessment.

USER PROFILE (CRITICAL - Use this to personalize ALL guidance):
{
  "condition": "gallstones",
  "severity": "moderate",
  "known_triggers": [
    {"name": "fried foods", "severity": "high", "attack_count": 4},
    {"name": "full-fat dairy", "severity": "high", "attack_count": 3},
    {"name": "chocolate", "severity": "moderate", "attack_count": 2}
  ],
  "allergens": [
    {"name": "peanuts", "type": "life-threatening"}
  ],
  "dietary_preferences": ["vegetarian"],
  "recent_safe_foods": ["grilled chicken", "avocado toast", "greek yogurt"]
}

TASK:
1. Identify all visible ingredients in the meal
2. Calculate base safety score (0-100) for gallstones in general
3. ADJUST score based on THIS USER'S specific triggers and allergens (this is critical!)
4. Provide personalized explanation using "YOUR" language

REQUIRED JSON RESPONSE FORMAT:
{
  "meal_name": "brief description of meal",
  "detected_ingredients": ["ingredient1", "ingredient2"],
  "problem_ingredients_for_user": [
    {"ingredient": "name", "reason": "matches YOUR trigger: X", "severity": "high|moderate|low"}
  ],
  "safe_ingredients": ["ingredient1"],
  "base_score": 75,
  "adjusted_score": 35,
  "confidence": 90,
  "personalized_reasoning": "2-3 sentences explaining score FOR THIS USER",
  "trigger_warnings": ["Your known trigger 'fried foods' detected in fried chicken"],
  "allergen_alert": null or "CRITICAL: Contains peanuts (YOUR allergen)"
}

RULES:
- If allergen detected: adjusted_score = 0, allergen_alert MUST be set
- Reference user's trigger history: "This caused 3 attacks for you"
- Use "YOUR" language: "Based on YOUR triggers..." not "Based on triggers..."
- Keep reasoning concise: 2-3 sentences maximum
- Confidence reflects ingredient identification certainty (not safety certainty)
```

**Trigger Priority Order** (When Multiple Apply):
1. **Allergens** (always highest priority, score = 0, stops further processing)
2. **High severity triggers** (user-defined, -40 points each)
3. **Moderate severity triggers** (user-defined, -25 points each)
4. **Low severity triggers** (user-defined, -15 points each)
5. **General gallstone risk factors** (if no specific triggers, conservative baseline)

**AI Response Verbosity**:
- **Personalized Reasoning**: 2-3 sentences maximum (shown prominently)
- **Trigger Warnings**: One sentence per trigger (bullet list)
- **Allergen Alert**: Single sentence, all caps if present
- **Detected Ingredients**: Comma-separated list (expandable detail)
- **Example**: "This scores 35/100 FOR YOU because it contains fried chicken, which matches YOUR High severity trigger 'fried foods'. You've had 4 attacks after eating fried foods. We strongly recommend avoiding this meal or trying our modified version."

**AI Response Format** (Structured JSON Required):
- **Yes, enforce JSON schema** for consistency and parsing reliability
- Backend validates response against JSON schema before returning to mobile app
- If AI returns invalid JSON: Retry once with stricter prompt, or fallback to error message
- Fallback response (if 2 failures): "Unable to analyze meal. Please try different angle or lighting."

**Why This Approach**:
- **Structured profile data**: Clear, unambiguous personalization context for AI
- **JSON response**: Reliable parsing, no regex/NLP needed for response processing
- **Concise reasoning**: Mobile-friendly, fits screen without scrolling
- **Priority order**: Ensures safety-critical issues (allergens) never get buried
- **Retry logic**: Handles occasional AI formatting errors gracefully

**Implementation Notes**:
- Use Gemini's structured output feature (JSON mode) if available
- Store raw AI response in database for debugging/improvement
- Monitor token usage: ~1000 tokens per request (acceptable for MVP economics)
- Future: A/B test reasoning verbosity (some users may want more detail)

---

## Question 3: Free vs Premium Feature Gates

**Context**: The spec defines free tier limits (FR-075, FR-028) but some features have unclear access levels, affecting conversion funnel design.

**Clarification Questions**:
- Can free users VIEW recipe modifications with upgrade prompt?
- Can free users see advanced trigger statistics?
- Are pattern detection suggestions available to free users?
- Can free users export basic scan history CSV?

### Selected Answer

**Recipe Modifications**:
- **Free users**: Can see "See Safer Version" button when meal scores < 50
- **Tapping button**: Shows teaser screen with:
  - Blurred recipe preview (visible but not readable)
  - Sample: "Original: Heavy cream → Modified: Greek yogurt"
  - Message: "Unlock personalized recipe modifications with Premium"
  - Upgrade CTA: "Start 7-Day Free Trial"
- **Premium users**: Full recipe with substitutions, instructions, nutritional comparison, save capability
- **Rationale**: Teaser shows value without giving away core Premium feature, drives trials

**Advanced Trigger Statistics**:
- **Free users** see:
  - Basic trigger list with severity levels
  - Source of identification (User input, Pattern detected, Attack correlated)
  - Simple stats: "Fried foods - 4 attack correlations"
- **Premium users** see:
  - Everything free users see, PLUS:
  - Detailed correlation timeline (graph of attacks vs. trigger exposures)
  - Trigger avoidance success rate: "You've avoided fried foods 94% of the time over 30 days"
  - Predicted attack risk: "High risk day detected based on yesterday's meals"
  - Trigger confidence trends over time
- **Rationale**: Free users get actionable trigger info, Premium users get deeper insights for optimization

**Pattern Detection Suggestions**:
- **Available to ALL users** (free and Premium)
- This is **core value proposition** - must be free to prove personalization works
- Without pattern detection, free users can't discover triggers → no "aha moment" → high churn
- Pattern detection is what differentiates GallDiet from generic apps
- **Rationale**: Pattern detection drives free → Premium conversion by demonstrating value, not by gating the feature

**Scan History Export**:
- **Free users**: Cannot export (feature hidden entirely)
- **Premium users**: Full CSV export with all scan details
- **Why not offer basic export to free?**: Export is primarily for doctor visits (medical validation), and users valuing this are likely Premium converters. Keeping it Premium-only creates clear tier differentiation without hurting core experience.
- **Alternative for free users**: Can screenshot individual scans or use symptom report feature (which shows recent meals) for medical appointments

**Complete Feature Matrix**:

| Feature                          | Free Tier                              | Premium Tier                         |
|----------------------------------|----------------------------------------|--------------------------------------|
| Barcode scanning                 | ✅ Unlimited                           | ✅ Unlimited                         |
| Meal photo scanning              | ✅ 3/day                               | ✅ Unlimited                         |
| Scan history retention           | ✅ 90 days                             | ✅ Unlimited                         |
| Pattern detection                | ✅ Full access                         | ✅ Full access                       |
| Trigger suggestions              | ✅ Full access                         | ✅ Full access                       |
| Basic trigger statistics         | ✅ Source, severity, correlation count | ✅ Same                              |
| Advanced trigger analytics       | ❌ Upgrade prompt                      | ✅ Trends, avoidance rate, risk      |
| Recipe modifications             | ⚠️ Teaser only (blurred preview)       | ✅ Full recipes                      |
| Emergency support                | ✅ Full access (safety first)          | ✅ Full access                       |
| Attack logging & correlation     | ✅ Full access                         | ✅ Full access                       |
| Custom collections/favorites     | ✅ Up to 3 collections                 | ✅ Unlimited collections             |
| Weekly summary                   | ✅ Basic (scan count, avg score)       | ✅ Detailed (insights, trends)       |
| Scan history CSV export          | ❌ Premium only                        | ✅ Full export                       |
| Multiple dietary profiles        | ❌ Premium only                        | ✅ Unlimited profiles                |

**Why This Approach**:
- **Free tier is valuable**: Users can discover triggers, log attacks, get personalized scores
- **Clear Premium benefits**: Unlimited scans, full recipes, detailed analytics, export
- **No "bait and switch"**: Core personalization (pattern detection) is free, doesn't create resentment
- **Conversion triggers**: Users hitting 3-scan limit or wanting deeper insights naturally convert

---

## Question 4: Attack Correlation Time Window

**Context**: Requirements mention "3-8 hours prior" for attack correlation (FR-065, FR-067), but this seems arbitrary and affects accuracy of trigger identification.

**Clarification Questions**:
- Is 3-8 hours based on medical research?
- Should this be user-configurable?
- What if user ate multiple trigger foods in that window?
- Should system also check 24-48 hour window for delayed reactions?

### Selected Answer

**Time Window: 1-8 Hours (With Weighted Confidence)**

**Medical Basis**:
- **Research-informed** (general digestive health literature, not GallDiet-specific clinical trial):
  - Most gallstone attacks occur 2-6 hours after high-fat meals (peak digestion period)
  - Some users experience attacks 1-2 hours (rapid onset, small meals)
  - Delayed reactions possible up to 8 hours (large meals, slower digestion)
  - Beyond 8 hours, correlation becomes unreliable (too many confounding variables)
- **MVP approach**: Use 1-8 hour window with weighted confidence (peak window = higher weight)

**Window Breakdown with Confidence Weights**:

| Time Before Attack | Confidence Weight | Rationale                          |
| ------------------ | ----------------- | ---------------------------------- |
| 1-2 hours          | 70%               | Possible but less common (rapid)   |
| 3-6 hours          | 100%              | Peak digestion period (most common) |
| 7-8 hours          | 60%               | Possible but less reliable (delayed) |
| < 1 hour           | 0%                | Too fast (unlikely to be food-related) |
| > 8 hours          | 0%                | Too delayed (confounding factors) |

**User Configurability**: No (for MVP)
- **Rationale**: Adds complexity without clear benefit for MVP
- Most users don't know their personal timing - education burden too high
- Risk of users misconfiguring and missing real triggers
- **Future enhancement**: After collecting real-world data, allow "My typical reaction time: Fast (1-3h), Normal (3-6h), Delayed (6-8h)"

**Multiple Foods in Window Handling**:
- **Show ALL foods** from the correlation window, ranked by likelihood
- **Ranking algorithm**:
  1. Known triggers (highest priority)
  2. Foods with prior correlations (even if not confirmed triggers yet)
  3. High-fat foods (gallstone risk factors)
  4. All other foods in window
- **Display format**:
  ```
  Pattern Detected:

  You had an attack 4 hours after eating. Here's what you scanned:

  🔴 FRIED CHICKEN (4 hours before) - Matches your known trigger "fried foods"
     This is the 3rd time this happened. Confidence: 87%

  ⚠️ CHOCOLATE CAKE (6 hours before) - High fat content (15g)
     This is the 1st correlation. Confidence: 45% (needs more data)

  ✓ GRILLED VEGETABLES (5 hours before) - Low risk food
     Unlikely to be the cause

  [Confirm Fried Chicken as Trigger] [Confirm Chocolate as Trigger] [Neither]
  ```
- Users can confirm one, multiple, or reject all

**24-48 Hour Delayed Window**: No (for MVP)
- **Rationale**:
  - Too many confounding variables (user ate 6+ meals in that time)
  - Scientifically weak correlation (most gallstone attacks are acute, within hours)
  - UI becomes overwhelming ("You ate 25 things in the last 48 hours...")
  - False positive risk too high
- **Future consideration**: If user reports "I don't think any recent meals caused this," optionally check 12-24 hour window as secondary pass

**Why This Approach**:
- **Evidence-based**: Uses known digestion timing patterns
- **Weighted confidence**: Peak window (3-6h) gets highest weight, edges get lower weight
- **Handles complexity**: Multiple foods shown, but ranked intelligently
- **Avoids false negatives**: 1-8 hour range catches most real triggers
- **Avoids false positives**: Doesn't check overly broad windows (24-48h)

---

## Question 5: Offline Functionality Scope

**Context**: Spec says "scan history viewable offline" (FR-047, FR-104) but details are unclear, affecting mobile storage and sync logic.

**Clarification Questions**:
- Should cached scans include full-resolution images or thumbnails?
- Can users mark scans offline with later sync?
- What if user logs attack offline?
- Should barcode lookups cache Open Food Facts data for offline re-scans?

### Selected Answer

**Cached Scan Images**: Thumbnails Only (For Offline Viewing)
- **Image resolution**:
  - **Full-resolution**: Stored on backend only (for AI analysis, user can re-download)
  - **Thumbnail**: 300x300px JPEG, ~50KB each, cached locally
  - **Storage estimate**: 90 days × 3 scans/day × 50KB = ~13.5MB (acceptable)
- **Display behavior**:
  - Offline: Show thumbnail with note "Full image available online"
  - Online: Tap thumbnail to download full-resolution on demand
- **Rationale**: Balances storage constraints with offline utility (users can see what they ate, even if not full detail)

**Offline Scan Marking** ("I ate this" / "I avoided this"): Yes, With Queue
- **Behavior**:
  - User can mark scans offline (changes saved locally in AsyncStorage)
  - UI shows subtle indicator: "Will sync when online"
  - When device reconnects: Automatic background sync to backend
- **Conflict resolution**: Last-write-wins (offline change overwrites backend if newer)
- **Rationale**: Common use case (user in airplane mode after restaurant meal, marks scans later) should work seamlessly

**Offline Attack Logging**: Queue for Later Processing
- **Behavior**:
  - User can fill out attack log form offline (pain location, intensity, symptoms, etc.)
  - Attack is saved locally with "pending sync" status
  - Pattern detection does NOT run offline (requires backend processing)
  - When device reconnects:
    - Attack synced to backend
    - Pattern detection runs server-side
    - User sees notification: "New trigger pattern detected for your recent attack"
- **Rationale**:
  - Attack logging should always be available (user may be in ER without good connectivity)
  - Pattern detection complexity requires backend (not suitable for client-side)
  - Acceptable delay: Pattern detection isn't time-sensitive, can wait for connectivity

**Barcode Lookup Caching**: Yes, For Previously Scanned Products
- **Caching strategy**:
  - After successful barcode scan, cache full Open Food Facts product data locally
  - Cache stored in AsyncStorage with 90-day retention (matches scan history retention)
  - Re-scanning same barcode offline: Show cached result with note "Scanned on [date], info may be outdated"
  - Connectivity restored: Background refresh cached data if product updated on Open Food Facts
- **Storage estimate**: Average product JSON ~5KB, 100 unique products = 500KB (negligible)
- **Rationale**: Users often re-scan same products (grocery staples), offline support improves UX

**Offline Feature Matrix**:

| Feature                         | Online                       | Offline                                       |
| ------------------------------- | ---------------------------- | --------------------------------------------- |
| View scan history (past 90 days) | ✅ Full resolution images    | ✅ Thumbnails only                            |
| Mark scans (ate/avoided)        | ✅ Immediate sync            | ✅ Queue for later sync                       |
| Log attack episode              | ✅ Immediate processing      | ✅ Queue for later processing                 |
| Pattern detection               | ✅ Runs immediately          | ❌ Waits for connectivity                     |
| Barcode scan (new product)      | ✅ Real-time lookup          | ❌ "Internet required" message                |
| Barcode scan (cached product)   | ✅ Fresh data                | ✅ Cached data (may be outdated)              |
| Meal photo scan                 | ✅ AI analysis               | ❌ "Internet required" message                |
| Emergency support               | ✅ All features              | ⚠️ "Find ER" requires location, others work   |
| Profile editing                 | ✅ Immediate sync            | ✅ Queue for later sync                       |

**Sync Conflict Resolution**:
- **General rule**: Last-write-wins (newer timestamp overwrites)
- **Exception - Scan marking**: If user marked "I ate this" offline, then separately marked "I avoided this" online before sync, offline change wins (user's latest intent)
- **Exception - Triggers**: Server-side changes (pattern-detected triggers) never overwritten by offline changes; merged instead

**Why This Approach**:
- **Graceful degradation**: Most features work offline, users can continue key workflows
- **Storage efficient**: Thumbnails + cached metadata = reasonable storage footprint
- **Sync clarity**: UI clearly indicates pending syncs, no silent failures
- **Safety preserved**: Emergency support remains mostly functional offline

---

## Question 6: Emergency Support Liability Language

**Context**: Emergency support requirements are comprehensive (FR-056-065) but lack specific disclaimer language, affecting legal risk and ethical responsibility.

**Clarification Questions**:
- What exact wording should disclaimers use?
- Should disclaimers appear on every emergency screen?
- What if user repeatedly ignores "SEEK MEDICAL CARE" warnings?
- Should app restrict access after risky behaviors?

### Selected Answer

**Disclaimer Language** (Specific Wording):

**Primary Emergency Screen** (When user taps "Emergency Support"):
```
⚠️ SEEK MEDICAL CARE IMMEDIATELY

Gallstone attacks require professional medical evaluation.

DO NOT rely on this app for medical advice.

GallDiet is an educational and tracking tool only.

If you are experiencing:
• Severe abdominal pain
• Pain lasting more than 5 minutes
• Fever or yellowing skin
• Nausea or vomiting

You should:
✅ Go to the Emergency Room
✅ Call 911 if severe or worsening
✅ Contact your doctor

This app CANNOT replace medical care.

[Call 911] [Find Nearest ER] [Document Symptoms]
```

**All Health Guidance Screens** (Scan results, trigger suggestions, recipe modifications):
```
Footer disclaimer (smaller text, always visible):

"GallDiet provides dietary information only and is not medical advice.
Always consult your healthcare provider for medical decisions."
```

**Symptom Documentation Screen**:
```
Header message:

"This tool helps you communicate with medical staff.
It does NOT diagnose or treat conditions.
You still need to see a doctor."
```

**Disclaimer Frequency**:
- **Emergency Support**: Show full disclaimer EVERY time user enters emergency section (never dismiss permanently)
- **First app launch**: Show disclaimer in onboarding
- **Scan results**: Footer disclaimer on every scan result screen (health guidance)
- **Settings**: Full legal disclaimer available at any time ("About" → "Medical Disclaimer")
- **Rationale**: Repetition reinforces message, reduces liability, prioritizes safety over UX convenience

**Repeated Emergency Support Usage Without Medical Care**:
- **System behavior**: NO restrictions or access blocking
- **Rationale**: **Safety first** - never prevent user from accessing emergency features, even if behavior seems risky
- **Instead**:
  - After 3+ emergency uses without "went to ER" indication: Show additional message:
    ```
    We've noticed you've used Emergency Support several times.

    If you're having frequent attacks, it's CRITICAL to see a
    gastroenterologist as soon as possible.

    Frequent attacks may indicate need for surgery or other treatment.

    Please schedule an appointment with your doctor.

    [Dismiss] [Find Gastroenterologists Near Me]
    ```
  - Include emergency usage pattern in health reports (doctors can see: "User logged 5 attacks in 30 days")
  - App remains fully functional - never locks features or shows judgmental messaging

**App Restrictions**: None
- **Never restrict access** to any features based on user behavior
- **Never** show pop-ups that must be dismissed to continue using app
- **Never** force users to confirm "I will go to ER" or similar (creates false liability protection)
- **Rationale**: Restriction could prevent someone from accessing help in genuine emergency

**Legal Review**:
- ⚠️ **IMPORTANT**: These disclaimers should be reviewed by healthcare attorney before launch
- May need adjustments based on:
  - State medical device laws
  - FDA Digital Health guidance
  - Professional liability insurance requirements
  - Platform policies (Apple App Store, Google Play medical app guidelines)

**Why This Approach**:
- **Clear, unambiguous language**: No medical jargon, 6th-grade reading level
- **Repetition without annoyance**: Full disclaimer in emergency context, footer elsewhere
- **Never restricts access**: Liability management through disclosure, not feature gating
- **Behavioral nudges**: Suggests doctor visits without forcing, provides gastroenterologist search
- **Documentation trail**: Disclaimer acceptance logged, usage patterns available for defense if needed

**Implementation Notes**:
- Store disclaimer acceptance with timestamp in user profile
- Log each emergency support usage with context (symptom severity, whether ER recommended, user action taken)
- Include disclaimer acceptance in Terms of Service during account creation
- Update disclaimers across app if legal review suggests changes (version control for legal compliance)

---

## Question 7: Subscription Trial & Cancellation Edge Cases

**Context**: Basic subscription flows defined (FR-076-082) but edge cases need clarification for payment flow complexity and revenue leakage prevention.

**Clarification Questions**:
- If user cancels trial on day 3, can they start a new trial later?
- What if user cancels Premium, then resubscribes 2 months later?
- Should system allow immediate Premium access on first payment or wait for Stripe confirmation?
- If payment fails during grace period, do unlimited scans continue?

### Selected Answer

**Trial Cancellation & Reactivation**:
- **One trial per user lifetime** (tracked by user account, not device/email)
- **If user cancels on day 3 of 7-day trial**:
  - Immediate revert to free tier limits (effective immediately, not at day 7)
  - Trial status marked `used` permanently
  - User cannot restart trial on this account
  - Message shown: "Your trial has been cancelled. You can upgrade to Premium anytime for $9.99/month (no trial available)."
- **Rationale**: Prevents trial abuse (cancel/restart loop), aligns with industry standard (Spotify, Netflix, etc.)
- **Exception**: If user never completed trial (e.g., started trial but immediately cancelled without any Premium usage), system could allow one restart within 30 days - but for MVP, simpler to just mark `used`

**Resubscription After Cancellation**:
- **User cancels Premium, waits 2 months, wants to resubscribe**:
  - Full Premium access restored immediately upon resubscription
  - **Data restored**: All scan history, trigger data, recipes, collections from previous Premium period are still accessible
  - No second trial offered (trial already used lifetime)
  - Picks up where they left off
- **Data retention policy during lapsed period**:
  - Free tier limits apply (90-day scan history visible)
  - Older scans (beyond 90 days) hidden but not deleted
  - Upon resubscription: Full history becomes visible again retroactively
- **Rationale**: "Undelete" experience (like Spotify restoring playlists) creates positive reactivation experience, encourages resubscription

**Payment Confirmation & Access**:
- **Immediate Premium access** (optimistic activation)
- **Flow**:
  1. User taps "Start Premium" and enters payment info
  2. Stripe client-side validation passes (card format valid)
  3. App immediately grants Premium access (before Stripe webhook confirms payment)
  4. Stripe webhook confirms payment within seconds (asynchronous)
  5. If webhook confirms success: No action needed, user keeps access
  6. If webhook reports failure: Revert user to free tier + show error: "Payment failed. Please update payment method."
- **Risk mitigation**:
  - Stripe validates card before request (reduces failed payment rate to <2%)
  - Failed payments detected within 60 seconds via webhook
  - Backend job checks payment status every hour for any users in "pending confirmation" state
- **Rationale**: Immediate access creates better UX (no "processing..." wait), 98%+ of payments succeed, fraud risk minimal for $9.99 transaction

**Failed Payment During Grace Period**:
- **Grace period**: 3 days (72 hours) from failed payment
- **During grace period**:
  - **Unlimited scans continue** (Premium access maintained)
  - User sees persistent banner: "Payment failed. Update payment method within 3 days to keep Premium."
  - Daily email reminders (day 1, day 2, day 3 final warning)
  - In-app notification badge on Profile tab
- **After grace period (if not resolved)**:
  - Immediate revert to free tier limits (3 scans/day)
  - Subscription status: `lapsed`
  - User sees message: "Premium expired due to payment failure. Update payment method to restore access."
  - Data preserved (see resubscription policy above)
- **Rationale**: Grace period is generous (revenue protection) but Premium access continues to encourage resolution

**Subscription State Machine**:

```
States:
- free: Free tier, trial never used
- trial_active: 7-day trial active (Premium access)
- trial_used: Trial completed/cancelled, on free tier
- premium_active: Paid Premium, active subscription
- premium_grace: Premium access but payment failed (3-day grace period)
- premium_lapsed: Was Premium, now free tier (cancelled or grace expired)

Transitions:
- free → trial_active: User starts trial
- trial_active → premium_active: Trial converts to paid (day 8 or early upgrade)
- trial_active → trial_used: User cancels trial (immediate revert)
- premium_active → premium_grace: Payment fails
- premium_grace → premium_active: Payment method updated successfully
- premium_grace → premium_lapsed: Grace period expires without resolution
- premium_active → premium_lapsed: User cancels subscription
- premium_lapsed → premium_active: User resubscribes (data restored)
- trial_used → premium_active: User upgrades (no trial, direct to paid)
```

**Edge Case Matrix**:

| Scenario                                   | Behavior                                                 | Rationale                       |
| ------------------------------------------ | -------------------------------------------------------- | ------------------------------- |
| User cancels trial day 3 of 7              | Immediate revert to free, trial marked used              | Prevent trial abuse             |
| User lets trial naturally expire (day 7)   | Auto-convert to Premium ($9.99/month), charge day 8      | Standard trial behavior         |
| User cancels Premium, resubscribes 2 months later | Full access restored, old data visible              | Positive reactivation UX        |
| Payment fails, user in 3-day grace         | Premium access continues, daily reminders                | Revenue protection + UX         |
| Grace period expires without resolution    | Revert to free tier, data preserved                      | Fair + encourages resolution    |
| User updates payment during grace period   | Grace period ends, Premium continues normally            | Problem resolved successfully   |
| User manually cancels before billing date  | Access until end of billing period, then revert to free  | Standard subscription behavior  |

**Why This Approach**:
- **Clear rules**: No ambiguity, state machine prevents edge case bugs
- **User-friendly**: Immediate access, grace periods, data preservation
- **Revenue protection**: One trial lifetime, grace period encourages payment fixes
- **Industry standard**: Matches Spotify, Netflix, Apple's subscription patterns (familiar to users)

---

## Question 8: Profile Completeness Calculation

**Context**: Profile completeness is mentioned (FR-004, SC-001) but formula isn't specified, affecting gamification effectiveness.

**Clarification Questions**:
- Are all profile fields weighted equally?
- Do optional fields count toward completeness?
- Should completeness decrease over time if info becomes stale?
- What triggers "complete your profile" prompts?

### Selected Answer

**Profile Completeness Formula** (Weighted by Impact on Personalization):

```
Profile Completeness = (
  Condition Info Weight * Completion % +
  Known Triggers Weight * Completion % +
  Dietary Preferences Weight * Completion % +
  Allergens Weight * Completion % +
  Health Goals Weight * Completion % +
  Personal Details Weight * Completion %
) / Total Weight

Where:
- Condition Info Weight: 20 points
- Known Triggers Weight: 35 points (highest - directly affects personalization)
- Dietary Preferences Weight: 15 points
- Allergens Weight: 20 points (safety critical)
- Health Goals Weight: 5 points
- Personal Details Weight: 5 points

Total Weight: 100 points
```

**Detailed Breakdown**:

**1. Condition Info (20 points max)**:
- Condition type selected (gallstones, post-surgery): 8 points
- Severity level selected (mild, moderate, severe): 7 points
- Diagnosis date entered: 3 points
- Number of previous attacks: 2 points

**2. Known Triggers (35 points max)** - Highest Weight:
- At least 1 trigger entered: 15 points
- 2-3 triggers entered: 25 points
- 4+ triggers entered: 30 points
- Severities set for all triggers: +5 points

**3. Dietary Preferences (15 points max)**:
- At least 1 preference selected (vegetarian, vegan, etc.): 10 points
- 2+ preferences: 15 points

**4. Allergens (20 points max)** - Safety Critical:
- "No allergens" explicitly confirmed: 10 points
- OR at least 1 allergen entered: 20 points

**5. Health Goals (5 points max)**:
- At least 1 goal selected (avoid attacks, identify triggers, etc.): 5 points

**6. Personal Details (5 points max)**:
- Emergency contact phone number: 3 points
- Doctor's name/phone (optional): 2 points

**Optional Fields**: Not Counted
- User notes on triggers
- Doctor appointment dates
- Profile photo
- These are "nice to have" but don't affect personalization or safety

**Example Calculation**:

**New user (Dana) - 65% complete**:
- ✅ Condition: Gallstones, Moderate, diagnosed Oct 2025 = 18/20 (missing attack count)
- ❌ Known Triggers: None yet ("I don't know") = 0/35
- ✅ Dietary Preferences: Vegetarian = 10/15 (only 1 preference)
- ✅ Allergens: None confirmed = 10/20 (explicit confirmation)
- ✅ Health Goals: Identify triggers, avoid surgery = 5/5
- ❌ Personal Details: None = 0/5

**Total**: 43/100 = **43% complete**

Wait, that doesn't match the user journey example (Dana should be 65%+). Let me recalculate with adjusted weights...

**Revised Weights** (To Match User Journey Expectations):

- **Condition Info**: 25 points (was 20)
- **Known Triggers**: 25 points (was 35) - Don't penalize new users too heavily
- **Dietary Preferences**: 15 points (same)
- **Allergens**: 20 points (same)
- **Health Goals**: 10 points (was 5)
- **Personal Details**: 5 points (same)

**Dana's recalculated score**:
- Condition: 23/25 (missing attack count = -2)
- Known Triggers: 5/25 (acknowledged "I don't know" = baseline 5 points, not 0)
- Dietary Preferences: 10/15
- Allergens: 10/20 (confirmed none)
- Health Goals: 10/10
- Personal Details: 0/5

**Total**: 58/100 = **58% complete** (closer, but still want 65%+)

**Final Adjustment** - Give Credit for Starting:
- Users who indicate "I don't know my triggers yet" get 10/25 points (not 0)
- Rationale: They've engaged with the section, app will help them discover

**Dana's final score**: 58 + 5 = **63% complete** ✓ (within 55-65% target from SC-001)

**Completeness Over Time**: No Decrease
- Profile completeness does NOT decrease over time (no "stale" penalty)
- **Rationale**: Avoid punishing users who completed profile months ago
- Diagnosis date might be "old" but it's still accurate
- If we want to encourage profile updates, use different mechanism (see prompts below)

**"Complete Your Profile" Prompts**:

**Trigger Thresholds**:
- **Below 50%**: Prominent prompt on Home screen: "Complete your profile for better personalization (48%)"
- **50-75%**: Subtle prompt in Profile tab: "Improve accuracy - add dietary preferences (62%)"
- **75-90%**: Occasional prompt (once per week): "Nearly complete! Add emergency contact (83%)"
- **90-100%**: No prompts (celebrate: "Your profile is complete! 🎉")

**What to Suggest** (Contextual Prompts):
- If triggers = 0: "Help us learn your triggers - scan 10 meals this week"
- If allergens not set: "Do you have any food allergies? This is important for safety."
- If no dietary preferences: "Are you vegetarian, vegan, or have other dietary preferences?"
- If goals not set: "What's your main goal? Avoid attacks? Identify triggers?"

**When to Show Prompts**:
- Onboarding completion: If < 75%, show summary with missing fields
- After first scan: If < 60%, gentle nudge: "Add known triggers to see better personalization"
- Weekly summary email: If < 70%, include "Complete your profile" reminder
- Never interrupt core workflows (scanning, emergency support)

**Why This Approach**:
- **Weighted by impact**: Triggers and allergens weighted higher (directly affect safety/personalization)
- **Fair to new users**: Users without known triggers aren't punished harshly (acknowledged = 10 points)
- **No time penalty**: Completeness doesn't decay (avoids feeling like chore)
- **Contextual prompts**: Specific suggestions based on what's missing
- **Gamification**: Percentage visible, prompts encouraging, celebration at 90%+

---

## Question 9: Allergen vs Trigger Distinction

**Context**: Spec separates allergens (FR-010) from triggers (FR-007) but handling isn't crystal clear, affecting safety hierarchy and user confusion prevention.

**Clarification Questions**:
- If user enters "dairy" as BOTH allergen and trigger, does system deduplicate?
- Should allergen warnings be more prominent than trigger warnings?
- Can triggers have "severity: life-threatening"?
- Do allergen scans count toward daily scan limit?

### Selected Answer

**Deduplication: Yes, With Clear Distinction**
- If user enters "dairy" as BOTH allergen AND trigger:
  - System stores only ONE entry: Allergen (allergen takes precedence)
  - UI shows: "Dairy (Allergen - life-threatening)" in trigger/allergen list
  - During onboarding: If user tries to add "dairy" as trigger after already marking as allergen, system shows:
    ```
    "Dairy" is already marked as an ALLERGEN (life-threatening).

    Allergens are more serious than triggers.

    Keep as allergen only? [Yes] [Edit to Trigger Only]
    ```
- **Rationale**: Prevents confusion, ensures highest-risk classification wins, reduces data redundancy

**Warning Prominence: Allergen > Trigger (Distinct Visual Hierarchy)**

| Aspect            | Allergen Warning                           | Trigger Warning                        |
| ----------------- | ------------------------------------------ | -------------------------------------- |
| **Color**         | Red with red border                        | Orange/yellow with orange border       |
| **Icon**          | ⚠️ + 🚫 (double icon)                      | ⚠️ (single icon)                       |
| **Text Style**    | ALL CAPS, bold                             | Sentence case, regular weight          |
| **Safety Score**  | Always 0 (override)                        | Reduced by 15-40 points                |
| **Position**      | Top of screen (before any other info)      | Below safety score, above details      |
| **Dismissible**   | No (always visible)                        | Yes (expandable section)               |
| **Haptic**        | Strong haptic alert (vibration)            | Light haptic                           |
| **Sound**         | Alert sound (if enabled)                   | No sound                               |

**Example Scan Results**:

**Allergen Detected**:
```
⚠️🚫 ALLERGEN ALERT ⚠️🚫

DO NOT EAT - CONTAINS PEANUTS
(YOUR LIFE-THREATENING ALLERGEN)

Safety Score: 0/100

This product is UNSAFE FOR YOU due to allergen.

───────────────────────────────
Additional concerns:
- Also contains fried coating (your trigger: fried foods)

Even if peanuts weren't present, this would score 35/100
due to your triggers.
```

**Trigger Detected (No Allergen)**:
```
Safety Score: 35/100 (Avoid)

⚠️ PERSONAL TRIGGER DETECTED

This contains fried dough and full-fat cheese, which
match YOUR triggers:
• Fried foods (High severity) - 4 attack correlations
• Full-fat dairy (High severity) - 3 attack correlations

We STRONGLY recommend avoiding this meal or trying
our modified version.

[See Safer Version]
```

**Trigger Severity: "Life-Threatening" Reserved for Allergens**
- Triggers have 3 severity levels: **Low, Moderate, High**
- "Life-threatening" is NOT a trigger severity (reserved for allergens only)
- **UI enforcement**: Trigger severity dropdown shows only Low/Moderate/High
- **Why**: Clear semantic distinction prevents confusion
  - Allergen = immediate medical emergency risk (anaphylaxis, severe reaction)
  - Trigger = gallstone attack risk (painful but not immediately life-threatening)
- Users who insist a trigger is "life-threatening":
  - Suggest they mark it as allergen instead
  - Or use "High severity" trigger with note: "Causes severe attacks"

**Allergen Scans & Daily Limit**:
- **Yes, allergen scans count toward daily limit** (for free tier)
- **Rationale**: Prevents gaming the system (scanning 100 allergen-containing products to avoid limit)
- **BUT**: Allergen warnings are always shown immediately (not gated)
- **Workflow**:
  1. User on free tier (used 3 photo scans today)
  2. Tries to scan 4th meal photo
  3. Sees: "Daily limit reached. Upgrade for unlimited scans."
  4. User upgrades to Premium OR uses barcode scan instead (unlimited)
  5. If barcode scan reveals allergen: Immediate warning shown (safety first)

**Allergen vs Trigger Decision Tree**:

```
User adds food to profile during onboarding:

Is this food a life-threatening allergen?
├─ Yes → Mark as ALLERGEN
│         └─ Ask: Severity? [Life-threatening | Causes discomfort]
│             ├─ Life-threatening → Allergen (safety score = 0)
│             └─ Causes discomfort → Intolerance (safety score penalty)
│
└─ No → Does it cause gallstone attacks for you?
          ├─ Yes → Mark as TRIGGER
          │         └─ Ask: Severity? [Low | Moderate | High]
          │
          └─ No → Skip (not relevant)
```

**Data Model**:
```
Table: user_allergens
- id
- user_id
- allergen_name (e.g., "peanuts")
- type: enum('life-threatening', 'intolerance')
- date_added

Table: user_triggers
- id
- user_id
- trigger_name (e.g., "fried foods")
- severity: enum('low', 'moderate', 'high')
- identification_source: enum('user_input', 'pattern_detected', 'attack_correlated')
- date_added

Constraint: allergen_name and trigger_name cannot overlap for same user (enforced at app level)
```

**Why This Approach**:
- **Clear hierarchy**: Allergen > Trigger (visually and functionally distinct)
- **Safety first**: Allergen warnings impossible to miss, override all other concerns
- **Prevents confusion**: Separate data models, distinct UI, no overlap
- **User education**: Onboarding flow teaches difference ("Life-threatening?" question)

---

## Question 10: Multi-Device Sync Strategy

**Context**: Assumptions mention device switching (edge case) but sync details are vague, affecting backend architecture and user experience consistency.

**Clarification Questions**:
- Should app sync in real-time or only on app open/close?
- What if user scans on Device A offline, then opens Device B?
- Should profile changes propagate immediately to other devices?
- Does system maintain device-specific cache or shared cache?

### Selected Answer

**Sync Strategy: Real-Time + Periodic (Hybrid)**

**Real-Time Sync** (Immediate, via WebSocket/SSE):
- **Profile changes**: Trigger immediate sync to other devices
  - User adds new trigger on Device A → Device B shows it within 5 seconds
  - User updates severity on Device A → Device B reflects change immediately
- **Attack logging**: Sync immediately (triggers pattern detection on backend)
- **Subscription status changes**: Immediate sync (Premium activation, cancellation)
- **Implementation**: Server-sent events (SSE) or WebSocket connection (lightweight, HTTP/2)
- **Rationale**: Critical data (profile, subscription) must be consistent across devices instantly

**Periodic Sync** (Background, Every 30 Minutes):
- **Scan history**: Background sync of recent scans (last 24 hours)
- **Weekly summaries**: Background sync when generated server-side
- **Pattern detection results**: Background sync when new trigger suggested
- **Implementation**: Background fetch API (iOS) / WorkManager (Android)
- **Rationale**: Less time-sensitive data, batch sync reduces battery/bandwidth usage

**On App Open/Close**:
- **Open**: Full sync check (pull latest data if any changes since last sync)
- **Close**: Push any pending local changes (queue processed)
- **Rationale**: Catch-all to ensure consistency, handles case where real-time sync missed

**Offline Conflict Resolution**:

**Scenario: User scans on Device A (offline), then opens Device B (online)**

**Timeline**:
1. Device A (offline): User scans meal, saves locally (scan_id: A1, timestamp: T1)
2. Device A: Queued for sync, status = pending_sync
3. Device B (online): User opens app, syncs latest data (doesn't have A1 yet)
4. Device A: Comes online, uploads A1 to backend (timestamp: T1)
5. Backend: Accepts A1, assigns server_id: S1
6. Device B: Next periodic sync (30 min later), pulls S1
7. Device B: Shows A1 scan in history

**Conflict Case: User scans SAME meal on both devices (offline)**

**Timeline**:
1. Device A (offline): Scans pizza (scan_id: A1, timestamp: 12:00:00)
2. Device B (offline): Scans pizza (scan_id: B1, timestamp: 12:00:05)
3. Device A: Comes online first, syncs A1 → server_id: S1
4. Device B: Comes online, syncs B1 → server detects duplicate (same meal, same timestamp ±1 min)
5. Backend: Merges (keeps first synced = S1, discards B1 as duplicate)
6. Device B: Shows S1 in history, removes local B1

**Duplicate Detection Logic**:
```
Scans are duplicates if:
- Same user
- Same meal name OR similar image (image hash within 95% similarity)
- Timestamps within 2 minutes of each other
- Not manually marked different by user

Action: Keep first-synced, discard others
```

**Profile Changes Propagation**:

**Scenario: User adds trigger on Device A, Device B open simultaneously**

**Timeline**:
1. Device A: User adds "chocolate" trigger → saves locally → syncs to backend immediately
2. Backend: Receives trigger, saves, broadcasts update via SSE to all user's devices
3. Device B: Receives SSE event, updates local AsyncStorage cache, refreshes trigger list UI if visible
4. Device B: User sees new trigger within 5 seconds (without app restart)

**Cache Strategy: Shared Backend Cache + Device-Local Cache**

**Backend Cache (Redis)**:
- **Key**: `user:{user_id}:profile`
- **Value**: Full user profile JSON (triggers, preferences, allergens, etc.)
- **TTL**: 1 hour
- **Invalidation**: On any profile change
- **Purpose**: Reduce database hits for frequent profile reads (every scan uses profile)

**Device-Local Cache (AsyncStorage)**:
- **Key**: `user_profile`, `scan_history`, `pattern_detection_results`
- **Value**: JSON data, last_synced timestamp
- **TTL**: None (manual expiration on sync)
- **Purpose**: Offline access, reduce API calls

**Cache Consistency Flow**:
1. Device A: Changes profile → saves locally → syncs to backend
2. Backend: Updates database → invalidates Redis cache → broadcasts SSE to Device B
3. Device B: Receives SSE → invalidates local AsyncStorage → fetches fresh from backend → updates UI
4. Next scan on Device B: Uses fresh profile data

**Device-Specific vs. Shared Cache**:
- **Shared**: All user data (profile, scans, triggers) - same across devices
- **Device-Specific**: UI preferences (e.g., "reduce motion" setting), notification tokens, tutorial completion status
- **Rationale**: Health data must be consistent, UI preferences can be device-local

**Sync Performance & Bandwidth**:
- **Scan image upload**: Full-resolution (1-3MB) uploaded on WiFi, thumbnail (50KB) on cellular (user can override)
- **Profile sync**: ~10KB JSON, real-time via SSE (negligible bandwidth)
- **History sync**: Batch of 30 scans = ~500KB (periodic background)
- **Total estimate**: <5MB/day typical usage (acceptable for users on limited data plans)

**Sync Failure Handling**:
- **Retry logic**: Exponential backoff (1s, 2s, 4s, 8s, 16s, 30s max)
- **Max retries**: 5 attempts, then show user: "Sync failed. Check internet connection."
- **User action**: Manual sync button in Settings → "Sync Now"
- **Conflict notification**: If backend rejects sync (conflict), show: "Some changes couldn't sync. View details?" → conflict resolution UI

**Why This Approach**:
- **Hybrid sync**: Real-time for critical data (profile, subscription), periodic for bulk data (history)
- **Conflict resolution**: Duplicate detection prevents double-entries, last-write-wins for simple conflicts
- **Battery efficient**: Background sync every 30 min (not constant polling)
- **Offline-friendly**: Queue-based sync, robust retry logic
- **Consistent UX**: SSE ensures profile changes appear instantly across devices

---

## Summary & Next Steps

All 10 clarification questions have been resolved with specific, actionable answers. These decisions are now documented and ready to be incorporated into the planning phase.

### Key Decisions Made:

1. **Pattern Detection**: Heuristic-based algorithm, 1-8 hour window with weighted confidence
2. **AI Prompts**: Structured JSON format, concise reasoning (2-3 sentences), allergen priority
3. **Free vs Premium**: Pattern detection free (core value), recipe modifications teaser, statistics tiered
4. **Attack Correlation**: 1-8 hour window, weighted confidence (3-6h peak), multiple foods ranked
5. **Offline Functionality**: Thumbnails cached, queue-based sync, barcode data cached, no pattern detection offline
6. **Emergency Disclaimers**: Specific legal language, repeated every entry, no access restrictions
7. **Subscriptions**: One trial lifetime, immediate access, 3-day grace period, data preserved
8. **Profile Completeness**: Weighted by impact (triggers 25%, allergens 20%), no time decay, contextual prompts
9. **Allergen vs Trigger**: Distinct hierarchy, allergens override, visual distinction, count toward limits
10. **Multi-Device Sync**: Real-time via SSE for profiles, periodic for history, conflict resolution, shared cache

### Files Updated:
- ✅ `/specs/001-galldiet-mvp-personalized/clarifications.md` (this file)

### Next Phase: `/speckit.plan`

These clarifications should be referenced during:
- **Data Model Design**: Entity relationships, triggers vs allergens tables, sync architecture
- **API Contract Design**: Scan endpoints, profile sync, pattern detection endpoints
- **Algorithm Implementation**: Pattern detection scoring, AI prompt structure, confidence calculation
- **UI/UX Specifications**: Warning hierarchy, offline states, subscription flows

**Ready to proceed with planning** ✅
