# GallDiet Development Environment Quickstart

This guide will help you set up the complete GallDiet development environment on macOS. The application consists of a Laravel 12 API backend and a React Native + Expo mobile frontend.

**Estimated setup time:** 30-45 minutes

---

## Table of Contents

1. [Prerequisites](#1-prerequisites)
2. [Backend Setup (Laravel + Sail)](#2-backend-setup-laravel--sail)
3. [Mobile App Setup (React Native + Expo)](#3-mobile-app-setup-react-native--expo)
4. [External Services Setup](#4-external-services-setup)
5. [Testing Setup](#5-testing-setup)
6. [Troubleshooting](#6-troubleshooting)
7. [Development Workflow](#7-development-workflow)
8. [API Testing](#8-api-testing)
9. [Next Steps](#9-next-steps)

---

## 1. Prerequisites

Before you begin, ensure you have the following installed on your macOS system:

### Required Software

#### 1.1 Xcode Command Line Tools

```bash
xcode-select --install
```

**Verify installation:**
```bash
xcode-select -p
# Expected output: /Library/Developer/CommandLineTools
```

#### 1.2 Homebrew

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

**Verify installation:**
```bash
brew --version
# Expected output: Homebrew 4.x.x
```

#### 1.3 Docker Desktop

Download and install Docker Desktop for Mac from [docker.com/products/docker-desktop](https://www.docker.com/products/docker-desktop/)

**Verify installation:**
```bash
docker --version
# Expected output: Docker version 24.x.x

docker-compose --version
# Expected output: Docker Compose version v2.x.x
```

**Important:** Make sure Docker Desktop is running before proceeding.

#### 1.4 Node.js 18+ and npm

```bash
# Install Node.js via Homebrew
brew install node@20

# Add to PATH (add to ~/.zshrc or ~/.bash_profile)
echo 'export PATH="/usr/local/opt/node@20/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

**Verify installation:**
```bash
node --version
# Expected output: v20.x.x

npm --version
# Expected output: 10.x.x
```

#### 1.5 Git

```bash
brew install git
```

**Verify installation:**
```bash
git --version
# Expected output: git version 2.x.x
```

### Recommended Software

#### VS Code with Extensions

1. Download from [code.visualstudio.com](https://code.visualstudio.com/)
2. Install recommended extensions:
   - **PHP Intelephense** - Advanced PHP language support
   - **Laravel Blade Snippets** - Blade syntax highlighting (even though this is API-only)
   - **Laravel Extra Intellisense** - Better Laravel code completion
   - **Pest Snippets** - Pest test framework snippets
   - **ESLint** - JavaScript/TypeScript linting
   - **Prettier** - Code formatter
   - **React Native Tools** - React Native development support
   - **TypeScript** - TypeScript language support
   - **Expo Tools** - Expo development tools

---

## 2. Backend Setup (Laravel + Sail)

Laravel Sail provides a Docker-based development environment with PostgreSQL, Redis, and all necessary services.

### 2.1 Clone the Repository

```bash
# Clone the repository
git clone <repository-url> galldiet
cd galldiet

# Or if you already have it cloned
cd /Users/<USER>/apps/GallDiet
```

### 2.2 Install PHP Dependencies

Laravel Sail requires Composer to install dependencies initially. Since we're using Docker, we'll use a temporary PHP container:

```bash
# Install dependencies using Docker (if you don't have Composer locally)
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php84-composer:latest \
    composer install --ignore-platform-reqs
```

**Expected output:**
```
Installing dependencies from lock file (including require-dev)
...
Package operations: XX installs, 0 updates, 0 removals
...
Generating optimized autoload files
```

### 2.3 Configure Environment Variables

```bash
# Copy the environment example file
cp .env.example .env
```

**Edit `.env` file** with your preferred editor (nano, vim, or VS Code):

```bash
code .env  # or nano .env
```

**Key configurations to update:**

```env
# Application
APP_NAME=GallDiet
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost

# Database (PostgreSQL via Sail)
DB_CONNECTION=pgsql
DB_HOST=pgsql
DB_PORT=5432
DB_DATABASE=galldiet
DB_USERNAME=sail
DB_PASSWORD=password

# Redis (via Sail)
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Cache & Queue
CACHE_STORE=redis
QUEUE_CONNECTION=redis

# Session
SESSION_DRIVER=database
SESSION_LIFETIME=120

# Mail (for development - logs emails)
MAIL_MAILER=log

# Gemini API (add your key - see section 4.1)
GEMINI_API_KEY=your_gemini_api_key_here

# Stripe (add your test keys - see section 4.2)
STRIPE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Open Food Facts API (no key needed)
OPENFOODFACTS_API_URL=https://world.openfoodfacts.org

# Sanctum (for mobile API authentication)
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1
```

### 2.4 Start Laravel Sail

```bash
# Create a sail alias for convenience (add to ~/.zshrc or ~/.bash_profile)
alias sail='sh $([ -f sail ] && echo sail || echo vendor/bin/sail)'

# Start Sail containers in detached mode
./vendor/bin/sail up -d
```

**Expected output:**
```
[+] Running 4/4
 ✔ Network galldiet_sail        Created
 ✔ Container galldiet-redis-1   Started
 ✔ Container galldiet-pgsql-1   Started
 ✔ Container galldiet-laravel.test-1  Started
```

**Verify containers are running:**
```bash
./vendor/bin/sail ps
```

**Expected output:**
```
NAME                      STATUS    PORTS
galldiet-laravel.test-1   Up        0.0.0.0:80->80/tcp
galldiet-pgsql-1          Up        0.0.0.0:5432->5432/tcp
galldiet-redis-1          Up        0.0.0.0:6379->6379/tcp
```

### 2.5 Initialize the Application

```bash
# Generate application key
./vendor/bin/sail artisan key:generate

# Run database migrations
./vendor/bin/sail artisan migrate

# Seed the database with test data
./vendor/bin/sail artisan db:seed

# Create storage symlink (for file uploads)
./vendor/bin/sail artisan storage:link
```

**Expected output from migrations:**
```
INFO  Running migrations.

2014_10_12_000000_create_users_table ......................... 10ms DONE
2014_10_12_100000_create_password_reset_tokens_table ......... 5ms DONE
2019_08_19_000000_create_failed_jobs_table ................... 8ms DONE
2019_12_14_000001_create_personal_access_tokens_table ........ 12ms DONE
...
```

### 2.6 Install Frontend Dependencies

```bash
# Install Node.js dependencies for Vite
./vendor/bin/sail npm install

# Build frontend assets
./vendor/bin/sail npm run build
```

### 2.7 Start Development Server

You have two options:

**Option A: Use the convenience script (recommended)**
```bash
# This runs Laravel server, queue worker, logs, and Vite concurrently
./vendor/bin/sail composer run dev
```

**Option B: Run services manually**
```bash
# In terminal 1: Start Laravel server
./vendor/bin/sail artisan serve

# In terminal 2: Start queue worker
./vendor/bin/sail artisan queue:listen

# In terminal 3: Watch logs
./vendor/bin/sail artisan pail

# In terminal 4: Start Vite
./vendor/bin/sail npm run dev
```

### 2.8 Verify Backend is Running

Open your browser and visit:

- **Application:** http://localhost
- **Health Check:** http://localhost/up (should return a 200 OK)

You should see a Laravel welcome page or API response.

### 2.9 Run Backend Tests

```bash
# Run the full test suite
./vendor/bin/sail artisan test

# Or use the composer script
./vendor/bin/sail composer test
```

**Expected output:**
```
PASS  Tests\Unit\ExampleTest
✓ that true is true

PASS  Tests\Feature\ExampleTest
✓ the application returns a successful response

Tests:    2 passed (2 assertions)
Duration: 0.15s
```

---

## 3. Mobile App Setup (React Native + Expo)

The mobile app is located in the `/mobile` directory and uses Expo for development.

### 3.1 Navigate to Mobile Directory

```bash
cd /Users/<USER>/apps/GallDiet/mobile
```

### 3.2 Install Dependencies

```bash
npm install
```

**Expected output:**
```
added XXX packages in XXs
```

### 3.3 Configure Mobile Environment

```bash
# Create .env file (if not exists)
touch .env
```

**Edit `.env` file:**

```bash
nano .env  # or code .env
```

**Add the following configuration:**

```env
# API Configuration
API_URL=http://localhost/api
API_TIMEOUT=30000

# For iOS Simulator (use your machine's IP)
# API_URL=http://192.168.1.XXX/api

# For Android Emulator
# API_URL=http://********/api

# Feature Flags
ENABLE_ANALYTICS=false
ENABLE_CRASH_REPORTING=false

# Environment
ENV=development
```

**Finding your machine's IP address:**
```bash
ipconfig getifaddr en0  # For WiFi
# or
ipconfig getifaddr en1  # For Ethernet
```

### 3.4 Start Expo Development Server

```bash
npx expo start
```

**Expected output:**
```
Starting project at /Users/<USER>/apps/GallDiet/mobile
Starting Metro Bundler

› Metro waiting on exp://192.168.1.XXX:8081
› Scan the QR code above with Expo Go (Android) or the Camera app (iOS)

› Using Expo Go
› Press a │ open Android
› Press i │ open iOS simulator
› Press w │ open web

› Press j │ open debugger
› Press r │ reload app
› Press m │ toggle menu
› Press o │ open project code in your editor

› Press ? │ show all commands
```

### 3.5 Run on iOS Simulator

**Prerequisites:** Xcode must be installed with iOS Simulator.

```bash
# Install Xcode from App Store if not already installed
# Open Xcode at least once to install components

# Run on iOS simulator
npx expo start --ios
```

**Expected behavior:**
- iOS Simulator launches automatically
- App builds and installs
- App opens and displays the Expo welcome screen

### 3.6 Run on Android Emulator

**Prerequisites:** Android Studio with Android Emulator must be installed.

```bash
# Install Android Studio from https://developer.android.com/studio
# Create an Android Virtual Device (AVD) in Android Studio

# Start an emulator first (from Android Studio or command line)
# Then run:
npx expo start --android
```

**Expected behavior:**
- App builds and installs on the running Android emulator
- App opens and displays the Expo welcome screen

### 3.7 Run on Physical Device

1. Install **Expo Go** from App Store (iOS) or Google Play (Android)
2. Make sure your device is on the same WiFi network as your computer
3. Open Expo Go app
4. Scan the QR code displayed in the terminal

### 3.8 Verify API Connection

Once the app is running, you should be able to:
- Register a new user account
- Log in
- Make API calls to the Laravel backend

**If you get connection errors**, check:
1. Backend is running (`http://localhost` works in browser)
2. API_URL in mobile `.env` is correct
3. Your firewall allows connections
4. For physical devices: use your machine's IP address, not `localhost`

---

## 4. External Services Setup

GallDiet integrates with several external services. Here's how to set them up for development.

### 4.1 Gemini API (AI Food Analysis)

**Purpose:** AI-powered food analysis and nutritional information extraction.

1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy your API key
5. Add to backend `.env`:

```env
GEMINI_API_KEY=your_api_key_here
```

**Test the integration:**
```bash
./vendor/bin/sail artisan tinker
```

```php
# In tinker:
use Illuminate\Support\Facades\Http;

$response = Http::withHeaders([
    'Content-Type' => 'application/json',
])->post('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' . config('services.gemini.api_key'), [
    'contents' => [
        ['parts' => [['text' => 'Hello, what foods are high in fat?']]]
    ]
]);

$response->json();
```

### 4.2 Stripe (Payment Processing)

**Purpose:** Subscription management and payment processing.

1. Visit [Stripe Dashboard](https://dashboard.stripe.com/register)
2. Create an account or sign in
3. Switch to **Test Mode** (toggle in top right)
4. Navigate to **Developers > API Keys**
5. Copy your **Publishable key** and **Secret key**
6. Add to backend `.env`:

```env
STRIPE_KEY=pk_test_xxxxxxxxxxxxx
STRIPE_SECRET=sk_test_xxxxxxxxxxxxx
```

**Set up webhook for local development:**

```bash
# Install Stripe CLI
brew install stripe/stripe-cli/stripe

# Login to Stripe
stripe login

# Forward webhooks to local Laravel
stripe listen --forward-to localhost/api/webhooks/stripe
```

**Copy the webhook signing secret** from the output and add to `.env`:

```env
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxx
```

**Test the integration:**
```bash
# In another terminal, trigger a test webhook
stripe trigger payment_intent.succeeded
```

### 4.3 Open Food Facts API

**Purpose:** Food product database for barcode scanning.

**Good news:** Open Food Facts is a free, open API that requires no API key!

The API URL is already configured in `.env.example`:

```env
OPENFOODFACTS_API_URL=https://world.openfoodfacts.org
```

**Test the integration:**
```bash
# Test barcode lookup
curl "https://world.openfoodfacts.org/api/v0/product/************.json"
```

### 4.4 Sentry (Error Tracking) - Optional for MVP

**Purpose:** Real-time error tracking and monitoring.

1. Visit [sentry.io](https://sentry.io/signup/)
2. Create an account
3. Create a new project (select Laravel)
4. Copy your DSN
5. Install Sentry SDK:

```bash
./vendor/bin/sail composer require sentry/sentry-laravel
```

6. Configure in `.env`:

```env
SENTRY_LARAVEL_DSN=https://<EMAIL>/xxxxx
SENTRY_TRACES_SAMPLE_RATE=1.0
```

7. Test:

```bash
./vendor/bin/sail artisan sentry:test
```

### 4.5 PostHog (Product Analytics) - Optional for MVP

**Purpose:** Product analytics and feature flags.

1. Visit [posthog.com](https://posthog.com/signup)
2. Create an account (choose cloud or self-hosted)
3. Create a new project
4. Copy your **Project API Key**
5. Add to mobile `.env`:

```env
POSTHOG_API_KEY=phc_xxxxxxxxxxxxx
POSTHOG_HOST=https://app.posthog.com
```

---

## 5. Testing Setup

### 5.1 Backend Tests (Laravel + Pest)

**Run all tests:**
```bash
./vendor/bin/sail artisan test
```

**Run specific test file:**
```bash
./vendor/bin/sail artisan test tests/Feature/Auth/RegistrationTest.php
```

**Run tests matching a filter:**
```bash
./vendor/bin/sail artisan test --filter=ProfileTest
```

**Run tests with coverage:**
```bash
./vendor/bin/sail artisan test --coverage
```

**Run parallel tests (faster):**
```bash
./vendor/bin/sail artisan test --parallel
```

**Expected output:**
```
PASS  Tests\Feature\Auth\AuthenticationTest
✓ login screen can be rendered
✓ users can authenticate using the login screen
✓ users can not authenticate with invalid password

Tests:    3 passed (12 assertions)
Duration: 0.45s
```

### 5.2 Code Formatting (Laravel Pint)

**Format all PHP files:**
```bash
./vendor/bin/sail exec laravel.test vendor/bin/pint
```

**Check formatting without fixing:**
```bash
./vendor/bin/sail exec laravel.test vendor/bin/pint --test
```

**Format only changed files:**
```bash
./vendor/bin/sail exec laravel.test vendor/bin/pint --dirty
```

### 5.3 Mobile Tests (Jest + React Native Testing Library)

**Note:** Mobile tests are not yet set up in the current project.

**To add testing, run:**
```bash
cd /Users/<USER>/apps/GallDiet/mobile
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native
```

**Run tests (once configured):**
```bash
npm test
```

### 5.4 Linting

**Backend (PHP - using Pint):**
```bash
./vendor/bin/sail exec laravel.test vendor/bin/pint
```

**Mobile (ESLint):**
```bash
cd /Users/<USER>/apps/GallDiet/mobile
npm run lint
```

---

## 6. Troubleshooting

### 6.1 Common Issues

#### Problem: Port 80 is already in use

**Error:**
```
Error response from daemon: Ports are not available: exposing port TCP 0.0.0.0:80 -> 0.0.0.0:0: listen tcp 0.0.0.0:80: bind: address already in use
```

**Solution 1: Stop conflicting service**
```bash
# Find what's using port 80
sudo lsof -i :80

# Stop the service (example: Apache)
sudo apachectl stop
```

**Solution 2: Change Laravel Sail port**

Edit `docker-compose.yml`:
```yaml
services:
  laravel.test:
    ports:
      - '${APP_PORT:-8080}:80'  # Changed from 80 to 8080
```

Add to `.env`:
```env
APP_PORT=8080
APP_URL=http://localhost:8080
```

Restart Sail:
```bash
./vendor/bin/sail down
./vendor/bin/sail up -d
```

#### Problem: Docker Desktop not running

**Error:**
```
Cannot connect to the Docker daemon at unix:///var/run/docker.sock
```

**Solution:**
1. Open Docker Desktop application
2. Wait for it to fully start (whale icon in menu bar should be steady)
3. Retry your command

#### Problem: Permission errors with Sail

**Error:**
```
Permission denied
```

**Solution:**
```bash
# Fix permissions on storage and bootstrap/cache
./vendor/bin/sail exec laravel.test chmod -R 777 storage bootstrap/cache

# Or if Sail isn't running yet
sudo chmod -R 777 storage bootstrap/cache
```

#### Problem: Database connection refused

**Error:**
```
SQLSTATE[08006] [7] could not connect to server: Connection refused
```

**Solution:**
```bash
# Check if PostgreSQL container is running
./vendor/bin/sail ps

# Restart containers
./vendor/bin/sail down
./vendor/bin/sail up -d

# Check database credentials in .env match docker-compose.yml
```

#### Problem: Expo app can't connect to API

**Error:**
```
Network request failed
```

**Solutions:**

1. **For iOS Simulator:** Use your machine's IP address
```bash
# Find your IP
ipconfig getifaddr en0

# Update mobile/.env
API_URL=http://192.168.1.XXX/api
```

2. **For Android Emulator:** Use special IP
```env
API_URL=http://********/api
```

3. **Check backend is accessible:**
```bash
# From your terminal
curl http://localhost/api/health

# From inside mobile directory
curl http://$(ipconfig getifaddr en0)/api/health
```

4. **Check firewall settings:**
```bash
# macOS Firewall might be blocking incoming connections
# System Settings > Network > Firewall > Allow incoming connections for Laravel
```

#### Problem: Vite manifest not found

**Error:**
```
Illuminate\Foundation\ViteException: Unable to locate file in Vite manifest
```

**Solution:**
```bash
# Build Vite assets
./vendor/bin/sail npm run build

# Or run Vite in dev mode
./vendor/bin/sail npm run dev
```

### 6.2 Reset Commands

#### Reset Database

```bash
# Drop all tables and re-run migrations
./vendor/bin/sail artisan migrate:fresh

# Drop all tables, re-run migrations, and seed
./vendor/bin/sail artisan migrate:fresh --seed
```

#### Clear All Caches

```bash
# Clear application cache
./vendor/bin/sail artisan cache:clear

# Clear configuration cache
./vendor/bin/sail artisan config:clear

# Clear route cache
./vendor/bin/sail artisan route:clear

# Clear view cache
./vendor/bin/sail artisan view:clear

# Clear Redis cache
./vendor/bin/sail artisan redis:flush
```

#### Reset Expo Project

```bash
cd /Users/<USER>/apps/GallDiet/mobile

# Clear Expo cache
npx expo start --clear

# Or use the reset script
npm run reset-project
```

#### Complete Backend Reset

```bash
# Stop Sail
./vendor/bin/sail down -v  # -v removes volumes (database data)

# Remove vendor directory
rm -rf vendor/

# Reinstall dependencies
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php84-composer:latest \
    composer install --ignore-platform-reqs

# Start fresh
./vendor/bin/sail up -d
./vendor/bin/sail artisan key:generate
./vendor/bin/sail artisan migrate:fresh --seed
./vendor/bin/sail artisan storage:link
```

#### Complete Mobile Reset

```bash
cd /Users/<USER>/apps/GallDiet/mobile

# Remove node_modules and lock file
rm -rf node_modules package-lock.json

# Clear npm cache
npm cache clean --force

# Reinstall
npm install

# Clear Expo cache
npx expo start --clear
```

### 6.3 Debugging Tools

#### Backend Debugging

**Laravel Tinker (Interactive REPL):**
```bash
./vendor/bin/sail artisan tinker
```

Example usage:
```php
# Check database connection
DB::connection()->getPdo();

# Create a test user
$user = \App\Models\User::factory()->create();

# Test Eloquent relationships
$user->profile;

# Check config values
config('app.name');
```

**Logs:**
```bash
# Tail Laravel logs
./vendor/bin/sail artisan pail

# Or view log file directly
./vendor/bin/sail exec laravel.test tail -f storage/logs/laravel.log
```

**Database queries:**
```bash
# Access PostgreSQL CLI
./vendor/bin/sail psql

# Run queries
\dt  # List tables
\d users  # Describe users table
SELECT * FROM users;
\q  # Quit
```

#### Mobile Debugging

**React Native Debugger:**
1. Download from [github.com/jhen0409/react-native-debugger](https://github.com/jhen0409/react-native-debugger/releases)
2. Install and open
3. In Expo app, shake device or press `Cmd+D` (iOS) / `Cmd+M` (Android)
4. Select "Debug Remote JS"

**Expo DevTools:**
```bash
# Open in browser
npx expo start

# Press 'j' to open debugger
```

**View mobile logs:**
```bash
# iOS Simulator logs
xcrun simctl spawn booted log stream --predicate 'process == "Expo"'

# Android Emulator logs
adb logcat | grep -i "expo"
```

---

## 7. Development Workflow

### 7.1 Branch Strategy

```bash
# Always work on feature branches
git checkout main
git pull origin main
git checkout -b feature/user-profile

# Make changes, commit, and push
git add .
git commit -m "Add user profile feature"
git push origin feature/user-profile

# Create pull request on GitHub/GitLab
```

### 7.2 Commit Conventions

Follow conventional commits format:

```
feat: Add user profile endpoint
fix: Resolve authentication bug in login flow
docs: Update API documentation
test: Add tests for meal logging
refactor: Simplify food safety score calculation
chore: Update dependencies
```

### 7.3 Testing Before Commits

**Always run tests before committing:**

```bash
# Backend tests
./vendor/bin/sail artisan test

# Format code
./vendor/bin/sail exec laravel.test vendor/bin/pint

# Mobile lint
cd mobile && npm run lint
```

### 7.4 Development Loop

**Backend development:**

1. Start Sail: `./vendor/bin/sail up -d`
2. Start dev server: `./vendor/bin/sail composer run dev`
3. Make changes to code
4. Changes auto-reload (Vite hot module replacement)
5. Write/update tests
6. Run tests: `./vendor/bin/sail artisan test --filter=YourTest`
7. Format code: `./vendor/bin/sail exec laravel.test vendor/bin/pint`
8. Commit changes

**Mobile development:**

1. Start Expo: `npx expo start`
2. Run on simulator/device
3. Make changes to code
4. App auto-reloads (Fast Refresh)
5. Test in app
6. Lint: `npm run lint`
7. Commit changes

### 7.5 Debugging Tools Summary

**Backend:**
- **Tinker:** `./vendor/bin/sail artisan tinker`
- **Logs:** `./vendor/bin/sail artisan pail`
- **Database:** `./vendor/bin/sail psql`
- **API Testing:** Postman, Insomnia, or HTTPie

**Mobile:**
- **React Native Debugger:** Standalone app
- **Expo DevTools:** Web-based debugger
- **Console logs:** View in terminal where Expo is running
- **Network inspection:** React Native Debugger or Flipper

---

## 8. API Testing

### 8.1 Using cURL

**Register a new user:**
```bash
curl -X POST http://localhost/api/register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
  }'
```

**Expected response:**
```json
{
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "created_at": "2025-10-12T10:00:00.000000Z",
    "updated_at": "2025-10-12T10:00:00.000000Z"
  },
  "token": "1|xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

**Login:**
```bash
curl -X POST http://localhost/api/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**Authenticated request (using token from login):**
```bash
curl -X GET http://localhost/api/user \
  -H "Accept: application/json" \
  -H "Authorization: Bearer 1|xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

### 8.2 Using HTTPie (Recommended)

**Install HTTPie:**
```bash
brew install httpie
```

**Register:**
```bash
http POST localhost/api/register \
  name="John Doe" \
  email="<EMAIL>" \
  password="password123" \
  password_confirmation="password123"
```

**Login:**
```bash
http POST localhost/api/login \
  email="<EMAIL>" \
  password="password123"
```

**Authenticated request:**
```bash
http GET localhost/api/user \
  "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 8.3 Using Postman/Insomnia

#### Postman Setup

1. Download [Postman](https://www.postman.com/downloads/)
2. Create a new Collection: "GallDiet API"
3. Set Collection variables:
   - `base_url`: `http://localhost/api`
   - `token`: (will be set after login)

#### Example Requests

**Register User:**
- Method: `POST`
- URL: `{{base_url}}/register`
- Body (JSON):
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}
```

**Login:**
- Method: `POST`
- URL: `{{base_url}}/login`
- Body (JSON):
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- Tests (to auto-save token):
```javascript
var jsonData = pm.response.json();
pm.collectionVariables.set("token", jsonData.token);
```

**Get User Profile:**
- Method: `GET`
- URL: `{{base_url}}/user`
- Authorization: Bearer Token `{{token}}`

### 8.4 Import OpenAPI Spec

If you have an OpenAPI specification file:

```bash
# Check if contracts directory exists
ls /Users/<USER>/apps/GallDiet/specs/001-galldiet-mvp-personalized/contracts/

# Import into Postman:
# File > Import > Select api-spec.yaml
```

### 8.5 Common API Endpoints

```bash
# Authentication
POST   /api/register          # Register new user
POST   /api/login             # Login user
POST   /api/logout            # Logout user
POST   /api/forgot-password   # Send password reset email
POST   /api/reset-password    # Reset password

# User Profile
GET    /api/user              # Get authenticated user
PUT    /api/user/profile      # Update profile
POST   /api/user/preferences  # Update dietary preferences

# Food Logging
GET    /api/foods             # List foods
POST   /api/foods             # Log a food
GET    /api/foods/{id}        # Get food details
PUT    /api/foods/{id}        # Update food
DELETE /api/foods/{id}        # Delete food

# Meals
GET    /api/meals             # List meals
POST   /api/meals             # Create meal
GET    /api/meals/{id}        # Get meal details

# Food Safety Analysis
POST   /api/analyze/food      # Analyze food safety
POST   /api/analyze/image     # Analyze food from image
POST   /api/analyze/barcode   # Analyze from barcode

# Pattern Detection
GET    /api/patterns/triggers # Get identified triggers
GET    /api/patterns/safe     # Get safe foods
```

---

## 9. Next Steps

Congratulations! Your development environment is now set up. Here's what to do next:

### 9.1 Explore the Codebase

**Backend structure:**
```
/Users/<USER>/apps/GallDiet/
├── app/
│   ├── Http/
│   │   ├── Controllers/     # API controllers
│   │   └── Requests/        # Form request validation
│   ├── Models/              # Eloquent models
│   ├── Policies/            # Authorization policies
│   └── Services/            # Business logic services
├── routes/
│   ├── api.php              # API routes
│   └── web.php              # Web routes (minimal for API-only)
├── database/
│   ├── migrations/          # Database migrations
│   ├── factories/           # Model factories for testing
│   └── seeders/             # Database seeders
├── tests/
│   ├── Feature/             # Feature tests
│   └── Unit/                # Unit tests
└── config/                  # Configuration files
```

**Mobile structure:**
```
/Users/<USER>/apps/GallDiet/mobile/
├── app/                     # App screens (Expo Router)
│   ├── (tabs)/             # Tab navigation
│   ├── _layout.tsx         # Root layout
│   └── +not-found.tsx      # 404 page
├── components/              # Reusable components
├── constants/               # App constants
├── hooks/                   # Custom React hooks
└── assets/                  # Images, fonts, etc.
```

### 9.2 Read Documentation

1. **Feature Specification:** `/Users/<USER>/apps/GallDiet/specs/001-galldiet-mvp-personalized/spec.md`
   - Complete feature requirements
   - User stories and acceptance criteria

2. **Data Model:** `/Users/<USER>/apps/GallDiet/specs/001-galldiet-mvp-personalized/data-model.md`
   - Database schema
   - Entity relationships
   - Data flow diagrams

3. **Implementation Plan:** `/Users/<USER>/apps/GallDiet/specs/001-galldiet-mvp-personalized/plan.md`
   - Technical architecture decisions
   - Implementation strategy

4. **CLAUDE.md:** `/Users/<USER>/apps/GallDiet/CLAUDE.md`
   - AI development guidance
   - Project-specific conventions
   - Laravel best practices

### 9.3 Generate Implementation Tasks

Use the Spec-Kit tools to generate an actionable task list:

```bash
# Generate tasks from spec
/speckit.tasks
```

This will create `/Users/<USER>/apps/GallDiet/specs/001-galldiet-mvp-personalized/tasks.md` with:
- Dependency-ordered tasks
- Clear acceptance criteria
- Implementation priorities

### 9.4 Start Implementing Features

**Recommended order:**

1. **Authentication & User Management**
   - User registration and login
   - Profile management
   - Email verification

2. **Core User Profile**
   - Demographic information
   - Dietary preferences
   - Health conditions

3. **Food Logging**
   - Manual food entry
   - Barcode scanning
   - Image recognition

4. **AI Food Analysis**
   - Gemini API integration
   - Nutritional analysis
   - Safety score calculation

5. **Pattern Detection**
   - Trigger identification
   - Safe food identification
   - Confidence scoring

### 9.5 Development Best Practices

**Backend (Laravel):**
- Write Pest tests for all features
- Use Eloquent API Resources for responses
- Create Form Request classes for validation
- Use Laravel Pint for code formatting
- Follow Laravel conventions and patterns

**Mobile (React Native):**
- Use TypeScript for type safety
- Follow React Native best practices
- Use Expo Router for navigation
- Keep components small and focused
- Use custom hooks for reusable logic

### 9.6 Need Help?

**Documentation:**
- Laravel Docs: [laravel.com/docs](https://laravel.com/docs)
- Expo Docs: [docs.expo.dev](https://docs.expo.dev)
- React Native Docs: [reactnative.dev/docs](https://reactnative.dev/docs)
- Pest Docs: [pestphp.com/docs](https://pestphp.com/docs)

**Community:**
- Laravel Discord: [discord.gg/laravel](https://discord.gg/laravel)
- Expo Discord: [chat.expo.dev](https://chat.expo.dev)
- Stack Overflow: Tag `laravel`, `react-native`, `expo`

**Project Resources:**
- Project Spec: `specs/001-galldiet-mvp-personalized/spec.md`
- Data Model: `specs/001-galldiet-mvp-personalized/data-model.md`
- Research Notes: `specs/001-galldiet-mvp-personalized/research.md`

---

## Quick Reference

### Essential Commands

```bash
# Backend
./vendor/bin/sail up -d                 # Start containers
./vendor/bin/sail down                  # Stop containers
./vendor/bin/sail artisan migrate       # Run migrations
./vendor/bin/sail artisan test          # Run tests
./vendor/bin/sail artisan tinker        # Interactive shell
./vendor/bin/sail composer run dev      # Start dev server with all services

# Mobile
cd mobile
npx expo start                          # Start Expo
npx expo start --ios                    # Run on iOS
npx expo start --android                # Run on Android
npm run lint                            # Lint code

# Useful Aliases (add to ~/.zshrc)
alias sail='./vendor/bin/sail'
alias sa='./vendor/bin/sail artisan'
alias sat='./vendor/bin/sail artisan test'
alias mobile='cd /Users/<USER>/apps/GallDiet/mobile'
```

### Environment URLs

- Backend: http://localhost
- Backend API: http://localhost/api
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### Default Credentials

Development database (PostgreSQL via Sail):
- Host: `pgsql`
- Port: `5432`
- Database: `galldiet`
- Username: `sail`
- Password: `password`

---

**You're all set!** Start building GallDiet and helping users manage their gallstone diet safely.
