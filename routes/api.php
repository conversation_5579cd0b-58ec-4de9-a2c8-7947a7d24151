<?php

use App\Http\Controllers\Api\AttackController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\EmergencyController;
use App\Http\Controllers\Api\HistoryController;
use App\Http\Controllers\Api\PatternController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\ScanController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\TriggerController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Public authentication routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Profile management
    Route::get('/profile', [ProfileController::class, 'show']);
    Route::put('/profile', [ProfileController::class, 'update']);

    // Trigger management
    Route::get('/triggers', [TriggerController::class, 'index']);
    Route::get('/triggers/me', [TriggerController::class, 'userTriggers']);
    Route::post('/triggers/{trigger}/confirm', [TriggerController::class, 'confirm']);
    Route::post('/triggers/{trigger}/deny', [TriggerController::class, 'deny']);
    Route::delete('/triggers/{trigger}', [TriggerController::class, 'remove']);

    // Scanning (Photo and Barcode)
    Route::get('/scan/recent', [ScanController::class, 'getRecent']); // T114 - Must be before parameterized routes
    Route::post('/scan/photo', [ScanController::class, 'storePhoto'])
        ->middleware('subscription:photo'); // T150 - Apply subscription limits to photo scans
    Route::post('/scan/barcode', [ScanController::class, 'storeBarcode']); // T139 - Barcode scanning (unlimited)
    Route::get('/scan/{scan}/status', [ScanController::class, 'showStatus']);
    Route::get('/scan/{scan}/result', [ScanController::class, 'showResult']);
    Route::patch('/scan/{scan}/outcome', [ScanController::class, 'updateOutcome']);

    // Attack logging
    Route::get('/attacks', [AttackController::class, 'index']);
    Route::post('/attacks', [AttackController::class, 'store']);
    Route::get('/attacks/{attack}', [AttackController::class, 'show']);

    // Pattern suggestions
    Route::get('/patterns/suggestions', [PatternController::class, 'index']);
    Route::post('/patterns/{suggestion}/confirm', [PatternController::class, 'confirm']);
    Route::post('/patterns/{suggestion}/reject', [PatternController::class, 'reject']);

    // Scan History (T145)
    Route::get('/history', [HistoryController::class, 'index']);
    Route::get('/history/{scan}', [HistoryController::class, 'show']);

    // Subscription Management
    Route::get('/subscription/scan-count', [SubscriptionController::class, 'scanCount']);

    // Emergency Support (T178)
    Route::get('/emergency/support', [EmergencyController::class, 'show']);
    Route::post('/emergency/report', [EmergencyController::class, 'generateReport']);
});
